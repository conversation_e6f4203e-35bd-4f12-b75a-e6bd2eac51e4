name: Build and Push to AWS ECR

on:
  push:
    branches:
      - dev

jobs:
  build-and-push:
    name: Build and Push to AWS ECR
    runs-on: ubuntu-latest

    steps:
      - name: Checkout source
        uses: actions/checkout@v4

      - name: <PERSON>lone Tier0-Backend (dev branch)
        env:
          GH_SSH_KEY: ${{ secrets.GH_SSH_KEY }}
        run: |
          mkdir -p ~/.ssh
          echo "$GH_SSH_KEY" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan github.com >> ~/.ssh/known_hosts
          <NAME_EMAIL>:FREEZONEX/Tier0-Backend.git --branch dev

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Login to ECR
        run: |
          aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com

      - name: Set image tag
        id: tag
        run: |
          TAG=$(date -u +"CICD.%Y-%m-%dT%H-%M-%SZ")
          echo "tag=$TAG" >> $GITHUB_OUTPUT

      - name: Build and push SYS image
        run: |
          TAG=${{ steps.tag.outputs.tag }}
          IMAGE=${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com/tier0/tier0-backend-sys

          docker build -f DockerfileSys -t $IMAGE:latest ./Tier0-Backend
          docker tag $IMAGE:latest $IMAGE:$TAG

          docker push $IMAGE:latest
          docker push $IMAGE:$TAG

      - name: Build and push UNS image
        run: |
          TAG=${{ steps.tag.outputs.tag }}
          IMAGE=${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.${{ secrets.AWS_REGION }}.amazonaws.com/tier0/tier0-backend-uns

          docker build -f DockerfileUns -t $IMAGE:latest ./Tier0-Backend
          docker tag $IMAGE:latest $IMAGE:$TAG

          docker push $IMAGE:latest
          docker push $IMAGE:$TAG

      - name: Deploy to EC2
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: tier0-dev.supos.app
          username: ubuntu
          key: ${{ secrets.SSH_PRIVATE_KEY }}
          script: |
            cd /home/<USER>/supos/run-all
            sudo make update
