{"name": "node-express", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@copilotkit/runtime": "1.8.13", "@langchain/anthropic": "^0.3.15", "@langchain/ollama": "^0.1.2", "@langchain/openai": "^0.3.14", "express": "^4.21.1", "openai": "^4.68.1"}}