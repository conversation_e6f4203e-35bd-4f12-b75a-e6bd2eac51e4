package stores

import (
	"gitee.com/unitedrhino/share/errors"
	"gorm.io/gorm"
	"strings"
)

func ErrFmt(err error) error {
	if err == nil {
		return nil
	}
	if err.Error() == "redis: nil" {
		return errors.NotFind
	}
	if _, ok := err.(*errors.CodeError); ok {
		return err
	}
	if err == gorm.ErrRecordNotFound {
		return errors.NotFind.WithStack(1)
	}
	if strings.Contains(err.<PERSON>(), "23505") || strings.Contains(err.<PERSON>rror(), "Duplicate entry") || strings.Contains(err.<PERSON><PERSON>r(), "duplicate key") {
		return errors.Duplicate.AddDetail(err)
	}
	return errors.Database.AddDetail(err)
}
