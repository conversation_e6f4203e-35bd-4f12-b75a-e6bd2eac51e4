version: '3.2'
# Network
networks:
  supos_net:
    name: supos_net
    ipam:
      config:
        - subnet: **********/16

services:
  syssvr:
    image: 654654246745.dkr.ecr.ap-southeast-1.amazonaws.com/tier0/tier0-backend-sys:latest
    container_name: syssvr
    hostname: syssvr
    restart: always
    ports:
      - "9540:9540"
    healthcheck:
      test: ["CMD", "nc", "-v", "-z", "localhost", "9540"]
      interval: 30s
      retries: 3
      start_period: 10s
      timeout: 5s
    depends_on:
      - etcd
      - redis
      - emqx
    environment:
      - confSuffix=${confSuffix}
      - devOpenApiApiKey=${devOpenApiApiKey} #emq的秘钥配置,参考: https://doc.unitedrhino.com/pages/24d647/#%E8%AE%BE%E5%A4%87%E5%9C%A8%E7%BA%BF%E7%8A%B6%E6%80%81%E6%A0%A1%E5%87%86-%E5%8F%AF%E9%80%89
      - devOpenApiSecretKey=${devOpenApiSecretKey} #如上
      - isInitTable=${isInitTable}
      - dbType=${dbType}
      - dbDSN=${dbDSN}
      - ossAk=${ossAk}
      - ossSk=${ossSk}
    logging:
      options:
        max-size: "50M"
        max-file: "10"
      driver: json-file
    networks:
      - supos_net
  unssvr:
    image: 654654246745.dkr.ecr.ap-southeast-1.amazonaws.com/tier0/tier0-backend-uns:latest
    container_name: unssvr
    hostname: unssvr
    restart: always
    ports:
      - "9623:9623"
    healthcheck:
      test: ["CMD", "nc", "-v", "-z", "localhost", "9623"]
      interval: 30s
      retries: 3
      start_period: 10s
      timeout: 5s
    depends_on:
      - etcd
      - redis
      - emqx
    environment:
      - confSuffix=${confSuffix}
      - devOpenApiApiKey=${devOpenApiApiKey} #emq的秘钥配置,参考: https://doc.unitedrhino.com/pages/24d647/#%E8%AE%BE%E5%A4%87%E5%9C%A8%E7%BA%BF%E7%8A%B6%E6%80%81%E6%A0%A1%E5%87%86-%E5%8F%AF%E9%80%89
      - devOpenApiSecretKey=${devOpenApiSecretKey} #如上
      - isInitTable=${isInitTable}
      - dbType=${dbType}
      - dbDSN=${dbDSN}
      - ossAk=${ossAk}
      - ossSk=${ossSk}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - AWS_REGION=${AWS_REGION}
    logging:
      options:
        max-size: "50M"
        max-file: "10"
      driver: json-file
    networks:
      - supos_net
  etcd:
    image: registry.cn-qingdao.aliyuncs.com/ithings-open/ithings-open:bitnami-etcd-3.5
    container_name: etcd
    hostname: etcd
    restart: always
    ports:
      - "2379:2379"
      - "2380:2380"
    environment:
      - ALLOW_NONE_AUTHENTICATION=yes # 允许不用密码登录
      - ETCD_NAME=etcd                                     # etcd 的名字
      - ETCD_INITIAL_ADVERTISE_PEER_URLS=http://etcd:2380  # 列出这个成员的客户端URL，如果是wsl2需要修改为服务可以访问的地址
      - ETCD_LISTEN_PEER_URLS=http://0.0.0.0:2380           # 用于监听伙伴通讯的URL列表
      - ETCD_LISTEN_CLIENT_URLS=http://0.0.0.0:2379         # 用于监听客户端通讯的URL列表
      - ETCD_ADVERTISE_CLIENT_URLS=http://etcd:2379        # 列出这个成员的客户端URL，如果是wsl2需要修改为服务可以访问的地址
    logging:
      options:
        max-size: "50M"
        max-file: "10"
      driver: json-file
    networks:
      - supos_net

  postgres: # 服务名称
    image: timescale/timescaledb-ha:pg17    # 指定镜像及其版本
    container_name: postgres # 指定容器的名称
    hostname: postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: password
      #POSTGRES_DB: default
    ports: # 端口映射
      - "5432:5432"
    volumes: # 数据持久化的配置
      - ../conf/pgsql/sql:/docker-entrypoint-initdb.d
      - ../conf/pgsql/data:/var/lib/postgresql/data
      - ../conf/pgsql/log:/var/log/postgresql
    logging:
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - supos_net


  redis:
    image: registry.cn-qingdao.aliyuncs.com/ithings-open/ithings-open:redis-7-alpine
    container_name: redis
    hostname: redis
    restart: always
    ports:
      - "6379:6379"
    logging:
      options:
        max-size: "50M"
        max-file: "10"
      driver: json-file
    networks:
      - supos_net
    volumes:
      - ../conf/redis:/usr/local/etc/redis
      - ../conf/redis/data:/data
  emqx:
    image: emqx/emqx:latest
    container_name: emqx
    hostname: emqx
    restart: always
    ports:
      - "1883:1883"
      - "8083:8083"
      - "8883:8883"
      - "8084:8084"
      - "18083:18083"
#    volumes:
#      - ../conf/emqx5-env/etc:/opt/emqx/etc
    environment:
      - "EMQX_NAME=emqx"
      - "EMQX_HOST=emqx"
    logging:
      options:
        max-size: "50M"
        max-file: "10"
      driver: json-file
    networks:
      - supos_net
  kong:
    image: kong:3.6
    container_name: kong
    environment:
      KONG_WORKER_STATE_UPDATE_FREQUENCY: 10
      KONG_DB_CACHE_TTL: 600
      KONG_NGINX_WORKER_PROCESSES: 4
      KONG_DATABASE: postgres
      KONG_PG_HOST: postgres
      KONG_PG_USER: postgres
      KONG_PG_PASSWORD: password
      KONG_SSL_CERT: /etc/kong/ssl/fullchain.cer
      KONG_SSL_CERT_KEY: /etc/kong/ssl/private.key
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_PROXY_LISTEN: 0.0.0.0:8000, 0.0.0.0:8443 ssl
    networks:
      - supos_net
    volumes:
      - ../conf/kong/certificationfile:/etc/kong/ssl:ro
      - ../conf/kong/kong_config.yml:/etc/kong/kong_config.yml
    ports:
      - "80:8000"
      - "443:8443"
    command: >
      sh -c "kong migrations bootstrap &&
             kong config db_import /etc/kong/kong_config.yml &&
             kong start"
  konga:
    image: pantsel/konga:latest
    container_name: konga
    environment:
      NODE_ENV: production
      NO_AUTH: "true"
      KONGA_SEED_KONG_NODE_DATA_SOURCE_FILE: /node.data
    ports:
      - "1337:1337"
    volumes:
      - ../conf/konga/db/:/app/kongadata/
      - ../conf/konga/node.data:/node.data  # 持久化数据库数据
    restart: always
    networks:
      - supos_net
  frontend:
    image: 654654246745.dkr.ecr.ap-southeast-1.amazonaws.com/tier0/tier0-frontend:latest
    container_name: frontend
    environment:
      NEXT_PUBLIC_API_URL: https://tier0-dev.supos.app
      NEXTAUTH_SECRET: super-secret-value
      NEXTAUTH_URL: https://tier0-dev.supos.app
      GRPC_UNS_HOST: *************:9623
      GRPC_SYS_HOST: *************:9540
    networks:
      - supos_net
    restart: unless-stopped 
