##--------------------------------------------------------------------
## HTTP Auth/ACL Plugin
##--------------------------------------------------------------------

## HTTP URL API path for Auth Request
##
## Value: URL
##
## Examples: http://ithings:80/mqtt/auth, https://[::1]:80/mqtt/auth
auth.http.auth_req.url = http://ithings:7777/api/v1/things/device/auth/login

## HTTP Request Method for Auth Request
##
## Value: post | get
auth.http.auth_req.method = post

## HTTP Request Headers for Auth Request, Content-Type header is configured by default.
## The possible values of the Content-Type header: application/x-www-form-urlencoded, application/json
##
## Examples: auth.http.auth_req.headers.accept = */*
auth.http.auth_req.headers.content_type = application/json

## Parameters used to construct the request body or query string parameters
## When the request method is GET, these parameters will be converted into query string parameters
## When the request method is POST, the final format is determined by content-type
##
## Available Variables:
##  - %u: username
##  - %c: clientid
##  - %a: ipaddress
##  - %r: protocol
##  - %P: password
##  - %p: sockport of server accepted
##  - %C: common name of client TLS cert
##  - %d: subject of client TLS cert
##
## Value: <K1>=<V1>,<K2>=<V2>,...
auth.http.auth_req.params = username=%u,password=%P,clientID=%c,ip=%a

## HTTP URL API path for SuperUser Request
##
## Value: URL
##
## Examples: http://ithings:80/mqtt/superuser, https://[::1]:80/mqtt/superuser
auth.http.super_req.url = http://ithings:7777/api/v1/things/device/auth/root-check

## HTTP Request Method for SuperUser Request
##
## Value: post | get
auth.http.super_req.method = post

## HTTP Request Headers for SuperUser Request, Content-Type header is configured by default.
## The possible values of the Content-Type header: application/x-www-form-urlencoded, application/json
##
## Examples: auth.http.super_req.headers.accept = */*
auth.http.super_req.headers.content-type = application/json

## Parameters used to construct the request body or query string parameters
## When the request method is GET, these parameters will be converted into query string parameters
## When the request method is POST, the final format is determined by content-type
##
## Available Variables:
##  - %u: username
##  - %c: clientid
##  - %a: ipaddress
##  - %r: protocol
##  - %P: password
##  - %p: sockport of server accepted
##  - %C: common name of client TLS cert
##  - %d: subject of client TLS cert
##
## Value: <K1>=<V1>,<K2>=<V2>,...
auth.http.super_req.params = username=%u,password=%P,clientID=%c,ip=%a

## HTTP URL API path for ACL Request
## Comment out this config to disable ACL checks
##
## Value: URL
##
## Examples: http://ithings:80/mqtt/acl, https://[::1]:80/mqtt/acl
auth.http.acl_req.url = http://ithings:7777/api/v1/things/device/auth/access

## HTTP Request Method for ACL Request
##
## Value: post | get
auth.http.acl_req.method = post

## HTTP Request Headers for ACL Request, Content-Type header is configured by default.
## The possible values of the Content-Type header: application/x-www-form-urlencoded, application/json
##
## Examples: auth.http.acl_req.headers.accept = */*
auth.http.acl_req.headers.content-type = application/json

## Parameters used to construct the request body or query string parameters
## When the request method is GET, these parameters will be converted into query string parameters
## When the request method is POST, the final format is determined by content-type
##
## Available Variables:
##  - %A: access (1 - subscribe, 2 - publish)
##  - %u: username
##  - %c: clientid
##  - %a: ipaddress
##  - %r: protocol
##  - %P: password
##  - %p: sockport of server accepted
##  - %C: common name of client TLS cert
##  - %d: subject of client TLS cert
##  - %t: topic
##
## Value: <K1>=<V1>,<K2>=<V2>,...
auth.http.acl_req.params = username=%u,access=%A,clientID=%c,ip=%a,topic=%t

## Time-out time for the request.
##
## Value: Duration
## -h: hour, e.g. '2h' for 2 hours
## -m: minute, e.g. '5m' for 5 minutes
## -s: second, e.g. '30s' for 30 seconds
##
## Default: 5s
auth.http.timeout = 5s

## Connection time-out time, used during the initial request,
## when the client is connecting to the server.
##
## Value: Duration
## -h: hour, e.g. '2h' for 2 hours
## -m: minute, e.g. '5m' for 5 minutes
## -s: second, e.g. '30s' for 30 seconds
##
## Default: 5s
auth.http.connect_timeout = 5s

## Connection process pool size
##
## Value: Number
auth.http.pool_size = 32

## Whether to enable HTTP Pipelining
##
## See: https://en.wikipedia.org/wiki/HTTP_pipelining
auth.http.enable_pipelining = true

##------------------------------------------------------------------------------
## SSL options

## Path to the file containing PEM-encoded CA certificates. The CA certificates
## are used during server authentication and when building the client certificate chain.
##
## Value: File
## auth.http.ssl.cacertfile = etc/certs/ca.pem

## The path to a file containing the client's certificate.
##
## Value: File
## auth.http.ssl.certfile = etc/certs/client-cert.pem

## Path to a file containing the client's private PEM-encoded key.
##
## Value: File
## auth.http.ssl.keyfile = etc/certs/client-key.pem

## In mode verify_none the default behavior is to allow all x509-path
## validation errors.
##
## Value: true | false
## auth.http.ssl.verify = false

## If not specified, the server's names returned in server's certificate is validated against
## what's provided `auth.http.auth_req.url` config's host part.
## Setting to 'disable' will make EMQ X ignore unmatched server names.
## If set with a host name, the server's names returned in server's certificate is validated
## against this value.
##
## Value: String | disable
## auth.http.ssl.server_name_indication = disable
