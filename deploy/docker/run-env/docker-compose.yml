version: '3.2'
# Network
networks:
  supos_net:
    name: supos_net
    ipam:
      config:
        - subnet: **********/16

services:
  etcd:
    image: registry.cn-qingdao.aliyuncs.com/ithings-open/ithings-open:bitnami-etcd-3.5
    container_name: etcd
    hostname: etcd
    restart: always
    ports:
      - "2379:2379"
      - "2380:2380"
    environment:
      - ALLOW_NONE_AUTHENTICATION=yes # 允许不用密码登录
      - ETCD_NAME=etcd                                     # etcd 的名字
      - ETCD_INITIAL_ADVERTISE_PEER_URLS=http://**********:2380  # 列出这个成员的客户端URL，如果是wsl2需要修改为服务可以访问的地址
      - ETCD_LISTEN_PEER_URLS=http://0.0.0.0:2380           # 用于监听伙伴通讯的URL列表
      - ETCD_LISTEN_CLIENT_URLS=http://0.0.0.0:2379         # 用于监听客户端通讯的URL列表
      - ETCD_ADVERTISE_CLIENT_URLS=http://**********:2379        # 列出这个成员的客户端URL，如果是wsl2需要修改为服务可以访问的地址
    logging:
      options:
        max-size: "50M"
        max-file: "10"
      driver: json-file
    networks:
      - supos_net

  postgres: # 服务名称
    image: timescale/timescaledb-ha:pg17    # 指定镜像及其版本
    container_name: postgres # 指定容器的名称
    hostname: postgres
    restart: always
    environment:
      POSTGRES_PASSWORD: password
      #POSTGRES_DB: default
    ports: # 端口映射
      - "5432:5432"
    volumes: # 数据持久化的配置
      - ../conf/pgsql/sql:/docker-entrypoint-initdb.d
      - ../conf/pgsql/data:/var/lib/postgresql/data
      - ../conf/pgsql/log:/var/log/postgresql
    logging:
      options:
        max-size: "10m"
        max-file: "3"
    networks:
      - supos_net


  redis:
    image: registry.cn-qingdao.aliyuncs.com/ithings-open/ithings-open:redis-7-alpine
    container_name: redis
    hostname: redis
    restart: always
    ports:
      - "6379:6379"
    logging:
      options:
        max-size: "50M"
        max-file: "10"
      driver: json-file
    networks:
      - supos_net
    volumes:
      - ../conf/redis:/usr/local/etc/redis
      - ../conf/redis/data:/data
  emqx:
    image: emqx/emqx:latest
    container_name: emqx
    hostname: emqx
    restart: always
    ports:
      - "1883:1883"
      - "8083:8083"
      - "8883:8883"
      - "8084:8084"
      - "18083:18083"
#    volumes:
#      - ../conf/emqx5-env/etc:/opt/emqx/etc
    environment:
      - "EMQX_NAME=emqx"
      - "EMQX_HOST=emqx"
    logging:
      options:
        max-size: "50M"
        max-file: "10"
      driver: json-file
    networks:
      - supos_net
#  minio:
#    image: minio/minio:latest
#    hostname: minio
#    container_name: ienv-minio
#    ports:
#      - "9000:9000"
#      - "9090:9090" # 控制台端口
#    restart: always
#    command: server --console-address ':9090' /data  #指定容器中的目录 /data
#    environment:
#      MINIO_ROOT_USER: root
#      MINIO_ROOT_PASSWORD: password #大于等于8位
#    logging:
#      options:
#        max-size: "50M" # 最大文件上传限制
#        max-file: "10"
#      driver: json-file
#    volumes:
#      - ../conf/minio/data:/data               #映射当前目录下的data目录至容器内/data目录
#      - ../conf/minio/config:/root/.minio/     #映射配置目录
#    networks:
#      - supos_net
