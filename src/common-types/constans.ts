// storage
export const SUPOS_STORAGE_MENU_TYPE = 'SUPOS_STORAGE_MENU_TYPE';
export const SUPOS_PRIMARY_COLOR = 'SUPOS_PRIMARY_COLOR';
export const SUPOS_STORAGE_MENU_WIDTH = 'SUPOS_STORAGE_MENU_WIDTH';
export const SUPOS_THEME = 'SUPOS_THEME_V2';
export const SUPOS_REAL_THEME = 'SUPOS_REAL_THEME_V2';
export const SUPOS_LANG_MESSAGE = 'SUPOS_LANG_MESSAGE';
export const SUPOS_LANG = 'SUPOS_LANG';
export const SUPOS_USER_BUTTON_LIST = 'SUPOS_USER_BUTTON_LIST';
export const SUPOS_USER_GUIDE_ROUTES = 'SUPOS_USER_GUIDE_ROUTES'; // 用户新手引导路由标识集合,isVisited是否访问过, [{name:'', isVisited: false, menu:{url:'',...}}]
export const SUPOS_USER_LAST_LOGIN_ENABLE = 'SUPOS_USER_LAST_LOGIN_ENABLE'; // 上一次用户是否免登录
export const SUPOS_USER_TIPS_ENABLE = 'SUPOS_USER_TIPS_ENABLE'; // tips是否需要展示
// cookie里面的code
export const SUPOS_COMMUNITY_TOKEN = 'supos_community_token';
export const LOGIN_URL = '/supos-login';
// 默认的title名称
export const APP_TITLE = 'supOS';
// 存储的固定位置 /minio/inter  /files/system/resource
export const STORAGE_PATH = '/files/system/resource';
// 菜单放的路径
export const MENU_TARGET_PATH = '/supos';
// 高阶使用等放的路径
export const Original_TARGET_PATH = '/icons';
export const SUPOS_UNS_TREE = 'SUPOS_UNS_TREE'; // tips是否需要展示
