/* 
  控制按钮权限,命名   button: 模块名  ： 按钮名（可以和国际化key一致）
*/

export const ButtonPermission = {
  'uns.templateDelete': 'button:uns.templateDelete',
  'uns.templateCopy': 'button:uns.templateCopy',
  'uns.templateAdd': 'button:uns.templateAdd',
  'uns.templateDefinition': 'button:uns.templateDefinition',
  'uns.editTemplateDescription': 'button:uns.editTemplateDescription',
  'uns.editTemplateName': 'button:uns.editTemplateName',
  'uns.labelDelete': 'button:uns.labelDelete',
  'uns.labelEditName': 'button:uns.labelEditName',
  'uns.labelAdd': 'button:uns.labelAdd',
  'uns.labelFileAdd': 'button:uns.labelFileAdd',
  'uns.labelFileDel': 'button:uns.labelFileDel',
  'uns.copy': 'button:uns.copy',
  'uns.importNamespace': 'button:uns.importNamespace',
  'uns.export': 'button:uns.export',
  'uns.delete': 'button:uns.delete',
  'uns.uploadDoc': 'button:uns.uploadDoc',
  'uns.editFileDetail': 'button:uns.editFileDetail',
  'uns.editFolderDetail': 'button:uns.editFolderDetail',
  'uns.definition': 'button:uns.definition',
  'uns.addFile': 'button:uns.addFile',
  'uns.editFolderDescription': 'button:uns.editFolderDescription',
  'uns.addFolder': 'button:uns.addFolder',
  'uns.rightKeyCopy': 'button:uns.rightKeyCopy',
  'uns.paste': 'button:uns.paste',
  'uns.batchReverseGeneration': 'button:uns.batchReverseGeneration',
  'uns.editFolderName': 'button:uns.editFolderName',
  'uns.editFileName': 'button:uns.editFileName',
  'appDisplay.add': 'button:appDisplay.add',
  'appDisplay.delete': 'button:appDisplay.delete',
  'collectionFlow.add': 'button:collectionFlow.add',
  'collectionFlow.edit': 'button:collectionFlow.edit',
  'collectionFlow.delete': 'button:collectionFlow.delete',
  'collectionFlow.design': 'button:collectionFlow.design',
  'collectionFlow.save': 'button:collectionFlow.save',
  'collectionFlow.deploy': 'button:collectionFlow.deploy',
  'collectionFlow.import': 'button:collectionFlow.import',
  'collectionFlow.export': 'button:collectionFlow.export',
  'collectionFlow.nodeManagement': 'button:collectionFlow.nodeManagement',
  'collectionFlow.process': 'button:collectionFlow.process',
  'collectionFlow.copy': 'button:collectionFlow.copy',
  'eventFlow.add': 'button:eventFlow.add',
  'eventFlow.edit': 'button:eventFlow.edit',
  'eventFlow.delete': 'button:eventFlow.delete',
  'eventFlow.design': 'button:eventFlow.design',
  'eventFlow.save': 'button:eventFlow.save',
  'eventFlow.deploy': 'button:eventFlow.deploy',
  'eventFlow.import': 'button:eventFlow.import',
  'eventFlow.export': 'button:eventFlow.export',
  'eventFlow.nodeManagement': 'button:eventFlow.nodeManagement',
  'eventFlow.process': 'button:eventFlow.process',
  'eventFlow.copy': 'button:eventFlow.copy',
  'appSpace.add': 'button:appSpace.add',
  'appSpace.delete': 'button:appSpace.delete',
  'appSpace.deleteHTML': 'button:appSpace.deleteHTML',
  'appSpace.coding': 'button:appSpace.coding',
  'appSpace.showPage': 'button:appSpace.showPage',
  'appSpace.newPage': 'button:appSpace.newPage',
  'appSpace.newgenerate': 'button:appSpace.newgenerate',
  'dashboards.add': 'button:dashboards.add',
  'dashboards.preview': 'button:appSpace.preview',
  'dashboards.design': 'button:appSpace.design',
  'dashboards.edit': 'button:dashboards.edit',
  'dashboards.delete': 'button:dashboards.delete',
  'dashboards.save': 'button:dashboards.save',
  'dashboards.export': 'button:dashboards.export',
  'dashboards.import': 'button:dashboards.import',
  'appGui.deploy': 'button:appGui.deploy',
  'appGui.generate': 'button:appGui.generate',
  'flowEditor.save': 'button:flowEditor.save',
  'flowEditor.deploy': 'button:flowEditor.deploy',
  'alert.edit': 'button:alert.edit',
  'alert.show': 'button:alert.show',
  'alert.delete': 'button:alert.delete',
  'alert.add': 'button:alert.add',
  'alert.confirm': 'button:alert.confirm',
  'accountManagement.add': 'button:accountManagement.add',
  'accountManagement.edit': 'button:accountManagement.edit',
  'accountManagement.enable': 'button:accountManagement.enable',
  'accountManagement.disable': 'button:accountManagement.disable',
  'accountManagement.resetPassword': 'button:accountManagement.resetPassword',
  'accountManagement.delete': 'button:accountManagement.delete',
  'accountManagement.roleSettings': 'button:accountManagement.roleSettings',
  'common.routerEdit': 'button:common.routerEdit',
  'common.processTask': 'button:common.processTask',
  'collectionGatewayManagement.edit': 'button:collectionGatewayManagement.edit',
  'collectionGatewayManagement.view': 'button:collectionGatewayManagement.view',
  'collectionGatewayManagement.copy': 'button:collectionGatewayManagement.copy',
  'collectionGatewayManagement.add': 'button:collectionGatewayManagement.add',
  'collectionGatewayManagement.delete': 'button:collectionGatewayManagement.delete',
  'webHook.copy': 'button:webHook.copy',
  'webHook.edit': 'button:webHook.edit',
  'webHook.delete': 'button:webHook.delete',
  'webHook.add': 'button:webHook.add',
  'codeManagement.addCode': 'button:codeManagement.addCode',
  'codeManagement.deleteCode': 'button:codeManagement.deleteCode',
  'codeManagement.editCode': 'button:codeManagement.editCode',
  'codeManagement.addCodeValue': 'button:codeManagement.addCodeValue',
  'codeManagement.editCodeValue': 'button:codeManagement.editCodeValue',
  'codeManagement.delCodeValue': 'button:codeManagement.delCodeValue',
  'openData.addKey': 'button:openData.addKey',
  'openData.enable': 'button:openData.enable',
  'openData.disable': 'button:openData.disable',
  'openData.delete': 'button:openData.delete',
  'appManagement.install': 'button:appManagement.install',
  'appManagement.unInstall': 'button:appManagement.unInstall',
  'appManagement.updateConfig': 'button:appManagement.updateConfig',
  'appManagement.pause': 'button:appManagement.pause',
  'appManagement.start': 'button:appManagement.start',
  'appManagement.upload': 'button:appManagement.upload',
  'appManagement.delete': 'button:appManagement.delete',
  'notificationManagement.notificationSetting': 'button:notificationManagement.notificationSetting',
  'notificationManagement.add': 'button:notificationManagement.add',
  'notificationManagement.edit': 'button:notificationManagement.edit',
  'notificationManagement.delete': 'button:notificationManagement.delete',
  'pluginManagement.install': 'button:pluginManagement.install',
  'pluginManagement.unInstall': 'button:pluginManagement.unInstall',
};

export const buttonLocal: { [key: string]: any } = {
  // 基础操作
  delete: { 'zh-CN': '删除', 'en-US': 'Delete' },
  copy: { 'zh-CN': '复制', 'en-US': 'Copy' },
  add: { 'zh-CN': '新增', 'en-US': 'Add' },
  edit: { 'zh-CN': '编辑', 'en-US': 'Edit' },
  save: { 'zh-CN': '保存', 'en-US': 'Save' },
  definition: { 'zh-CN': '编辑文件夹定义', 'en-US': 'Definition' },
  preview: { 'zh-CN': '预览', 'en-US': 'Preview' },
  design: { 'zh-CN': '设计', 'en-US': 'Design' },
  deploy: { 'zh-CN': '部署', 'en-US': 'Deploy' },
  import: { 'zh-CN': '导入', 'en-US': 'Import' },
  export: { 'zh-CN': '导出', 'en-US': 'Export' },
  process: { 'zh-CN': '处理', 'en-US': 'Process' },
  show: { 'zh-CN': '显示', 'en-US': 'Show' },
  confirm: { 'zh-CN': '确认', 'en-US': 'Confirm' },
  enable: { 'zh-CN': '启用', 'en-US': 'Enable' },
  disable: { 'zh-CN': '禁用', 'en-US': 'Disable' },
  generate: { 'zh-CN': '生成', 'en-US': 'Generate' },
  view: { 'zh-CN': '查看', 'en-US': 'View' },
  install: { 'zh-CN': '安装', 'en-US': 'Install' },
  unInstall: { 'zh-CN': '卸载', 'en-US': 'UnInstall' },
  updateConfig: { 'zh-CN': '更新配置', 'en-US': 'Update Config' },
  pause: { 'zh-CN': '暂停', 'en-US': 'Pause' },
  start: { 'zh-CN': '启动', 'en-US': 'Start' },
  upload: { 'zh-CN': '上传', 'en-US': 'Upload' },
  notificationSetting: { 'zh-CN': '通知方式设置', 'en-US': 'Start' },

  // 文件操作
  fileAdd: { 'zh-CN': '添加文件', 'en-US': 'Add File' },
  fileDel: { 'zh-CN': '删除文件', 'en-US': 'Delete File' },
  uploadDoc: { 'zh-CN': '上传附件', 'en-US': 'Upload Document' },

  // 特殊操作
  editDescription: { 'zh-CN': '编辑描述', 'en-US': 'Edit Description' },
  editName: { 'zh-CN': '编辑名称', 'en-US': 'Edit Name' },
  importNamespace: { 'zh-CN': '导入', 'en-US': 'Import Namespace' },
  rightKeyCopy: { 'zh-CN': '右键复制', 'en-US': 'Right-click Copy' },
  paste: { 'zh-CN': '粘贴', 'en-US': 'Paste' },
  batchReverseGeneration: { 'zh-CN': '批量逆向生成', 'en-US': 'Batch Reverse Generation' },
  editFileDetail: { 'zh-CN': '编辑文件详情', 'en-US': 'Edit File Detail' },
  editFolderDetail: { 'zh-CN': '编辑文件夹详情', 'en-US': 'Edit Folder Detail' },
  editFolderDescription: { 'zh-CN': '编辑文件夹描述', 'en-US': 'Edit Folder Description' },
  editFolderName: { 'zh-CN': '编辑文件夹名称', 'en-US': 'Edit Folder Name' },
  editFileName: { 'zh-CN': '编辑文件名称', 'en-US': 'Edit File Name' },
  deleteHTML: { 'zh-CN': '删除HTML', 'en-US': 'Delete HTML' },
  coding: { 'zh-CN': '代码编辑', 'en-US': 'Coding' },
  showPage: { 'zh-CN': '显示页面', 'en-US': 'Show Page' },
  newPage: { 'zh-CN': '新建页面', 'en-US': 'New Page' },
  newgenerate: { 'zh-CN': '重新生成', 'en-US': 'Regenerate' },
  nodeManagement: { 'zh-CN': '节点管理', 'en-US': 'Node Management' },
  resetPassword: { 'zh-CN': '重置密码', 'en-US': 'Reset Password' },
  roleSettings: { 'zh-CN': '角色设置', 'en-US': 'Role Settings' },
  routerEdit: { 'zh-CN': '路由编辑', 'en-US': 'Router Edit' },
  processTask: { 'zh-CN': '处理任务', 'en-US': 'Process Task' },
  addFile: { 'zh-CN': '新增文件', 'en-US': 'Add File' },
  addFolder: { 'zh-CN': '新增文件夹', 'en-US': 'Add Folder' },
  templateCopy: { 'zh-CN': '复制模板', 'en-US': 'Copy Template' },
  templateDelete: { 'zh-CN': '删除模板', 'en-US': 'Delete Template' },
  templateAdd: { 'zh-CN': '新增模板', 'en-US': 'Add Template' },
  templateDefinition: { 'zh-CN': '编辑模板定义', 'en-US': 'Template Definition' },
  editTemplateDescription: { 'zh-CN': '修改模板描述', 'en-US': 'Edit Template Description' },
  editTemplateName: { 'zh-CN': '编辑模板名称', 'en-US': 'Edit Template Name' },
  labelDelete: { 'zh-CN': '删除标签', 'en-US': 'Delete Label' },
  labelEditName: { 'zh-CN': '修改标签名称', 'en-US': 'Edit Label Name' },
  labelAdd: { 'zh-CN': '新增标签', 'en-US': 'Label Add' },
  labelFileAdd: { 'zh-CN': '标签新增文件', 'en-US': 'Label Add File' },
  labelFileDel: { 'zh-CN': '标签删除文件', 'en-US': 'Label Delete File' },
  addCode: { 'zh-CN': '新增编码', 'en-US': 'Add Code' },
  deleteCode: { 'zh-CN': '删除编码', 'en-US': 'Delete Code' },
  editCode: { 'zh-CN': '编辑编码', 'en-US': 'Edit Code' },
  addCodeValue: { 'zh-CN': '新增编码值', 'en-US': 'Add Code Value' },
  editCodeValue: { 'zh-CN': '编辑编码值', 'en-US': 'Edit Code Value' },
  delCodeValue: { 'zh-CN': '删除编码值', 'en-US': 'Delete Code Value' },
  addKey: { 'zh-CN': '新增密钥', 'en-US': 'Add Key' },
};

export const getLatestCode = (path: string) => {
  const pathMap = new Map([
    ['/uns', 'uns'],
    ['/app-display', 'appDisplay'],
    ['/collection-flow', 'collectionFlow'],
    ['/EventFlow', 'eventFlow'],
    ['/app-space', 'appSpace'],
    // ['/app-gui', 'appGui'],
    ['/dashboards', 'dashboards'],
    // ['/flow-editor', 'flowEditor'],
    ['/alert', 'alert'],
    ['/Alert', 'alert'],
    ['/account-management', 'accountManagement'],
    ['/collection-gateway-management', 'collectionGatewayManagement'],
    ['/CollectionGatewayManagement', 'collectionGatewayManagement'],
    ['/WebHooks', 'webHook'],
    ['/code-management', 'codeManagement'],
    ['/CodeManagement', 'codeManagement'],
    ['/open-data', 'openData'],
    ['/OpenData', 'openData'],
    ['/app-management', 'appManagement'],
    ['/AppManagement', 'appManagement'],
    ['/plugin-management', 'pluginManagement'],
    ['/notification-management', 'notificationManagement'],
    ['/NotificationManagement', 'notificationManagement'],
  ]);
  const code = pathMap.has(path) ? pathMap.get(path) : path;
  return code;
};
