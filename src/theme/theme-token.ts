export default {
  token: {
    borderRadius: 3,
    colorBgElevated: 'var(--supos-bg-color)',
    colorFill: 'var(--supos-color-fill)',
    colorTextPlaceholder: 'var(--supos-t-gray-color-30)',
    colorText: 'var(--supos-text-color)',
    colorTextDescription: 'var(--supos-text-color)',
    colorTextDisabled: 'var(--supos-select-d-color)',
    boxShadowSecondary: '1px 1px 10px 2px var(--supos-boxshadow-color)',
    colorPrimaryHover: 'var(--supos-theme-color)',
    colorBgMask: 'var(--supos-modal-mask-bg)',
    colorFillSecondary: 'var(--supos-fill-secondary)',
    colorFillTertiary: 'var(--supos-fill-tertiary)',
  },
  components: {
    Menu: {
      itemBg: 'var(--supos-bg-color)',
      itemColor: 'var(--supos-text-color)',
      itemHoverColor: 'var(--supos-text-color)',
      itemHoverBg: 'var(--supos-menu-hover-color)',
      itemSelectedBg: 'var(--supos-menu-active-color)',
      subMenuItemBg: 'var(--supos-bg-color)',
      popupBg: 'var(--supos-bg-color)',
      itemSelectedColor: 'var(--supos-text-color)',
      horizontalItemSelectedColor: 'var(--supos-theme-color)',
    },
    Tag: {
      defaultColor: 'var(--supos-text-color)',
      defaultBg: 'var(--supos-tag-color)',
    },
    Dropdown: {
      colorText: 'var(--supos-text-color)',
      colorBgElevated: 'var(--supos-bg-color)',
      controlItemBgHover: 'var(--supos-menu-hover-color)',
      controlItemBgActive: 'var(--supos-menu-active-color)',
      controlItemBgActiveHover: 'var(--supos-menu-active-color)',
    },
    Select: {
      colorTextDescription: 'var(--supos-text-color)',
      activeBorderColor: 'var(--supos-theme-color)',
      colorBgContainerDisabled: 'var(--supos-t-button-d-color)',
      zIndexPopup: 9001,
      colorBgElevated: 'var(--supos-input-color)',
      colorText: 'var(--supos-text-color)',
      selectorBg: 'var(--supos-input-color)',
      optionSelectedBg: 'var(--supos-select-active-color)',
      colorBgContainer: 'var(--supos-input-color)',
      multipleItemBg: 'var(--supos-fill-secondary)',
    },
    Input: {
      colorBgContainer: 'var(--supos-input-color)',
      colorText: 'var(--supos-text-color)',
      colorBgContainerDisabled: 'var(--supos-t-button-d-color)',
      activeBorderColor: 'var(--supos-theme-color)',
    },
    Message: {
      zIndexPopup: 9001,
    },
    Form: {
      labelColor: 'var(--supos-text-color)',
    },
    InputNumber: {
      colorBgContainer: 'var(--supos-input-color)',
    },
    Modal: {
      contentBg: 'var(--supos-modal-color)',
    },
    Button: {
      colorPrimary: 'var(--supos-theme-color)',
      colorPrimaryHover: 'var(--supos-theme-button-hover-color)',
      colorPrimaryActive: 'var(--supos-theme-button-active-color)',
      defaultBg: 'var(--supos-bg-color)',
      defaultHoverBg: 'var(--supos-bg-color)',
      colorLink: 'var(--supos-theme-color)',
      colorLinkHover: 'var(--supos-theme-button-hover-color)',
      colorPrimaryBg: 'var(--supos-primary-bg)',
      colorPrimaryBgHover: 'var(--supos-primary-bg-hover)',
      colorFill: 'var(--supos-fill-tertiary)',
    },
    Tabs: {
      itemSelectedColor: 'var(--supos-text-color)',
      inkBarColor: 'var(--supos-theme-color)',
    },
    DatePicker: {
      colorBgContainer: 'var(--supos-input-color)',
      colorPrimary: 'var(--supos-theme-color)',
      cellActiveWithRangeBg: 'var(--supos-active-with-range-bg)',
      controlItemBgActive: 'var(--supos-active-with-range-bg)',
      colorIcon: '#848484',
      cellHoverBg: 'var(--supos-cell-hover-bg)',
    },
    Radio: {
      colorPrimary: 'var(--supos-theme-color)',
      buttonBg: 'var(--supos-bg-color)',
      buttonCheckedBg: 'var(--supos-bg-color)',
      radioSize: 18,
      dotSize: 8,
      motionDurationMid: '0s',
      motionDurationSlow: '0s',
    },
    Table: {
      colorPrimary: 'var(--supos-theme-color)',
      colorBgContainer: 'var(--supos-bg-color)',
      headerBg: 'var(--supos-table-head-color)',
      headerColor: 'var(--supos-text-color)',
      rowHoverBg: 'var(--supos-menu-hover-color)',
      headerBorderRadius: 0,
      headerSplitColor: 'var(--supos-header-splitter-color)',
      headerFilterHoverBg: 'var(--supos-fill-secondary)',
      headerSortHoverBg: 'var(--supos-fill-secondary)',
      headerSortActiveBg: 'var(--supos-fill-secondary)',
      borderColor: 'var(--supos-table-tr-color)',
    },
    Typography: {
      colorLink: 'var(--supos-theme-color)',
      colorLinkHover: 'var(--supos-theme-button-hover-color)',
      colorLinkActive: 'var(--supos-theme-color)',
    },
    Checkbox: {
      colorPrimary: 'var(--supos-theme-color)',
      colorPrimaryHover: 'none',
      motionDurationFast: '0s',
      motionDurationMid: '0s',
      motionDurationSlow: '0s',
    },
    Tree: {
      colorPrimary: 'var(--supos-theme-color)',
      nodeSelectedBg: 'var(--supos-select-active-color)',
      nodeHoverBg: 'var(--supos-fill-secondary)',
      colorBgContainer: 'var(--supos-bg-color)',
    },
    TreeSelect: {
      colorPrimary: 'var(--supos-theme-color)',
      nodeSelectedBg: 'var(--supos-select-active-color)',
      nodeHoverBg: 'var(--supos-fill-secondary)',
      colorBgContainer: 'var(--supos-bg-color)',
    },
    Switch: {
      colorPrimary: 'var(--supos-theme-color)',
      handleShadow: '',
    },
    Pagination: {
      itemBg: 'var(--supos-bg-color)',
      itemActiveBg: 'var(--supos-bg-color)',
    },
  },
};
