{"restConnect": "REST 连接", "appSecretKey": "密钥", "restApiUrl": "RestAPI 地址", "instructions": "调用说明", "createSecretKey": "申请密钥", "addSecretKeyTip": "调用时需要在调用header中添加密钥", "restApiAccessMethod": "RestAPI 接入方式", "websocketAccessMethod": "websocket 接入方式", "websocketUrl": "websocket 地址", "mqttAccessMethod": "MQTT 接入方式", "request": "请求", "response": "返回", "push": "推送", "commonHeaderTip": "通用请求头信息：", "realTimeDataSubscription": "实时数据订阅", "eventSubscription": "事件订阅", "versionInfo": "版本信息", "commandEnumerationValue": "命令枚举值", "messageDirection": "消息方向", "commandEnumerationType": "命令枚举类型", "clientToServer": "客户端到服务端", "serverToClient": "服务端到客户端", "subscriptionResponse": "订阅响应", "realTimeValuePush": "实时值推送", "metadataChangePush": "元数据变更推送（暂不支持）", "allValues": "所有值", "partValues": "部分值", "fileAliasing": "文件别名", "fileName": "文件值", "supportWildcard": "topic集合，支持.*通配符", "responseCode": "响应码", "requestCommandNumber": "请求命令号", "collectorAlias": "采集器别名", "eventContent": "事件内容，BASE64编码", "logicFun": "逻辑函数", "mathFun": "数学函数", "dateFun": "日期函数", "textFun": "文本函数", "fieldTypeSTRING": "文本", "fieldTypeDOUBLE": "数字", "fieldTypeINT": "数字", "fieldTypeLONG": "数字", "fieldTypeFLOAT": "数字", "fieldTypeDECIMAL": "数字", "fieldTypeDATETIME": "日期", "fieldTypeENUM": "枚举", "fieldTypeSET_ENUM": "数组", "fieldTypeSYSCODE": "系统编码", "fieldTypeSET_SYSCODE": "数组", "fieldTypeSYSDEPT": "部门", "fieldTypeSYSSTAFF": "人员", "fieldTypeSUBFORM": "数组", "fieldTypeLINKDATA": "关联数据", "fieldTypeCOUNTER": "流水号", "logic0label": "逻辑与", "logic0intro": "如果所有参数都为真，AND函数返回布尔值true，否则返回布尔值 false", "logic0usage": "AND(逻辑表达式1,逻辑表达式2,...)", "logic0example": "AND({语文成绩}>90,{数学成绩}>90,{英语成绩}>90)，如果三门课成绩都> 90，返回true，否则返回false", "logic1label": "逻辑或", "logic1intro": "如果任意参数为真，OR 函数返回布尔值true；如果所有参数为假，返回布尔值false。", "logic1usage": "OR(逻辑表达式1,逻辑表达式2,...)", "logic1example": "OR({语文成绩}>90,{数学成绩}>90,{英语成绩}>90)，任何一门课成绩> 90，返回true，否则返回false", "logic2label": "逻辑真", "logic2intro": "TRUE函数返回布尔值true", "logic2example": "略", "logic3label": "逻辑假", "logic3intro": "FALSE函数返回布尔值false", "logic3example": "略", "logic4label": "条件运算", "logic4intro": "IF函数判断一个条件能否满足；如果满足返回一个值，如果不满足则返回另外一个值", "logic4usage": "IF(逻辑表达式,为true时返回的值,为false时返回的值)", "logic4example": "IF({语文成绩}>60,'及格','不及格')，当语文成绩>60时返回及格，否则返回不及格。", "logic5label": "多条件", "logic5intro": "IFS函数检查是否满足一个或多个条件，且返回符合第一个TRUE条件的值，IFS可以取代多个嵌套IF语句。", "logic5usage": "IFS(逻辑表达式1,逻辑表达式1为true返回该值,逻辑表达式2,逻辑表达式2为true返回该值,...)", "logic5example": "IFS({语文成绩}>90,'优秀',{语文成绩}>80,'良好',{语文成绩}>=60,'及格',{语文成绩}<60,'不及格')，根据成绩返回对应的评价。", "logic6label": "取反", "logic6intro": "NOT函数返回与指定表达式相反的布尔值。", "logic6usage": "NOT(逻辑表达式)", "logic6example": "NOT({语文成绩}>60)，如果语文成绩大于60返回false，否则返回true", "logic7label": "异或", "logic7intro": "XOR函数可以返回所有参数的异或值", "logic7usage": "XOR(逻辑表达式1, 逻辑表达式2,...)", "logic7example": "XOR({语文成绩}>90,{数学成绩}>90)，如果两门成绩都>90,返回false；如果两门成绩都<90，返回false；如果其中一门>90，另外一门<90，返回true", "logic8label": "判空", "logic8intro": "ISEMPTY函数可以用来判断值是否为空文本、空对象或者空数组", "logic8usage": "ISEMPTY(文本)", "logic8example": "略", "math0label": "绝对值", "math0intro": "ABS函数可以获取一个数的绝对值", "math0usage": "ABS(数字)", "math0example": "ABS(-8)可以返回8，也就是-8的绝对值", "math1label": "平均值", "math1intro": "AVERAGE函数可以获取一组数值的算术平均值", "math1usage": "AVERAGE(数字1,数字2,...)", "math1example": "AVERAGE({语文成绩},{数学成绩}, {英语成绩})返回三门课程的平均分", "math2label": "数量统计", "math2intro": "COUNT函数可以获取参数的数量", "math2usage": "COUNT(值,值,...)", "math2example": "COUNT(小明,小王,小张,小李)返回4，也就是人员的数量", "math3label": "四舍五入", "math3intro": "FIXED函数可将数字舍入到指定的小数位数并输出为文本", "math3usage": "FIXED(数字,小数位数)", "math3example": "FIXED(3.1415,2)返回'3.14'", "math4label": "取整", "math4intro": "INT函数可以获取一个数的整数部分", "math4usage": "INT(数字)", "math4example": "INT(3.1415)返回3，也就是3.1415的整数部分", "math5label": "求对数", "math5intro": "LOG函数可以根据指定底数返回数字的对数", "math5usage": "LOG(数字,底数)", "math5example": "LOG(100,10)返回2，也就是以10为底数100的对数", "math6label": "求余数", "math6intro": "MOD函数可以获取两数相除的余数", "math6usage": "MOD(被除数,除数)", "math6example": "MOD(4,3)返回1，也就是4/3的余数", "math7label": "取最大值", "math7intro": "MAX函数可以获取一组数值的最大值", "math7usage": "MAX(数字1,数字2,...)", "math7example": "MAX({语文成绩},{数学成绩},{英语成绩})返回三门课程中的最高分", "math8label": "取最小值", "math8intro": "MIN函数可以获取一组数值的最小值", "math8usage": "MIN(数字1,数字2,...)", "math8example": "MIN(语文成绩,数学成绩,英语成绩)返回三门课程中的最低分", "math9label": "乘幂", "math9intro": "POWER函数可以获取数字乘幂的结果", "math9usage": "POWER(数字,指数)", "math9example": "POWER(3，2)返回9，也就是3的2次方", "math10label": "乘积", "math10intro": "PRODUCT函数可以获取一组数值的乘积", "math10usage": "PRODUCT(数字1,数字2,...)", "math10example": "PRODUCT({单价}, {数量})获取总价，也就是单价和数量的乘积", "math11label": "正平方根", "math11intro": "SQRT函数可以获取一个数字的正平方根", "math11usage": "SQRT(数字)", "math11example": "SQRT(9)返回3，也就是9的正平方根", "math12label": "求和", "math12intro": "SUM函数可以获取一组数值的总和", "math12usage": "SUM(数字1,数字2,...)", "math12example": "SUM({语文成绩},{数学成绩}, {英语成绩})返回三门课程的总分", "math13label": "加权求和", "math13intro": "SUMPRODUCT函数可以将数组间对应的元素相乘，并返回乘积之和，适用于加权求和", "math13usage": "SUMPRODUCT(数组,数组...)", "math13example": "SUMPRODUCT([1,2,3],[0.1,0.2,0.3])返回1.4，也就是 1×0.1 + 2×0.2 + 3×0.3的值", "math14label": "随机数", "math14intro": "RANDOM函数返回0~1之间的随机数", "math14usage": "RANDOM()", "math14example": "RANDOM()获取随机数", "date0label": "日期对象", "date0intro": "DATE函数可以将时间戳转换为日期对象", "date0usage": "DATE(时间戳)", "date0example": "略", "date1label": "第几日", "date1intro": "DAY函数可以获取某日期是当月的第几日", "date1usage": "DAY(时间戳)", "date1example": "略", "date2label": "相差天数", "date2intro": "DAYS函数可以返回两个日期之间相差的天数。", "date2usage": "DAYS(结束日期,开始日期)", "date2example": "略", "date3label": "时间差值", "date3intro": "DATEDIF函数可以计算两个日期时间相差的年数、月数、天数、小时数、分钟数、秒数。", "date3usage": "DATEDIF(开始时间,结束时间,[单位])，单位可以是 'y' 、'M'、'd'、'h'、'm'、's'", "date3example": "DATEDIF({下单时间},{付款时间},'h')，如果下单时间是9:00，付款时间为当天10:30，计算得到的小时差为1.5。", "date4label": "加减天数", "date4intro": "DATEDELTA函数可以将指定日期加/减指定天数", "date4usage": "DATEDELTA(指定日期,需要加减的天数)", "date4example": "略", "date5label": "小时数", "date5intro": "HOUR函数可以返回某日期的小时数", "date5usage": "HOUR(时间戳)", "date5example": "略", "date6label": "月份", "date6intro": "MONTH返回某日期的月份", "date6usage": "MONTH(时间戳)", "date6example": "略", "date7label": "分钟数", "date7intro": "MINUTE函数可以返回某日期的分钟数", "date7usage": "MINUTE(时间戳)", "date7example": "略", "date8label": "当前时间", "date8intro": "NOW函数可以获取当前时间", "date8example": "略", "date9label": "第几秒", "date9intro": "SECOND函数可以返回某日期的秒数", "date9usage": "SECOND(时间戳)", "date9example": "略", "date10label": "今日", "date10intro": "TODAY函数可以返回今天", "date10example": "略", "date11label": "年份", "date11intro": "YEAR函数可以返回某日期的年份", "date11usage": "YEAR(时间戳)", "date11example": "略", "text0label": "合并文本", "text0intro": "CONCATENATE函数可以将多个文本合并成一个文本", "text0usage": "CONCATENATE(文本1,文本2,...)", "text0example": "CONCATENATE('三年二班','周杰伦')会返回'三年二班周杰伦'", "text1label": "相等比较", "text1intro": "EXACT函数可以比较两个文本是否完全相同，完全相同则返回true，否则返回false", "text1usage": "EXACT(文本1, 文本2)", "text1example": "EXACT({手机号},{中奖手机号})，如果两者相同，返回true，如果不相同，返回false", "text2label": "左截取", "text2intro": "LEFT函数可以从一个文本的第一个字符开始返回指定个数的字符", "text2usage": "LEFT(文本,文本长度)", "text2example": "LEFT('三年二班周杰伦',2)返回'三年'，也就是'三年二班周杰伦'的从左往右的前2个字符", "text3label": "文本长度", "text3intro": "LEN函数可以获取文本中的字符个数", "text3usage": "LEN(文本)", "text3example": "LEN('朝辞白帝彩云间')返回7，因为这句诗中有7个字符", "text4label": "小写转换", "text4intro": "LOWER函数可以将一个文本中的所有大写字母转换为小写字母", "text4usage": "LOWER(文本)", "text4example": "LOWER('JAYZ')返回'jayz'", "text5label": "右截取", "text5intro": "RIGHT函数可以获取由给定文本右端指定数量的字符构成的文本值", "text5usage": "RIGHT(文本,文本长度)", "text5example": "RIGHT('三年二班周杰伦',3)返回'周杰伦'，也就是'三年二班周杰伦'从右往左的前3个字符", "text6label": "首尾去空", "text6intro": "TRIM函数可以删除文本首尾的空格", "text6usage": "TRIM(文本)", "text6example": "TRIM('   蓝卓工业互联网   ')返回'蓝卓工业互联网'", "text7label": "大写转换", "text7intro": "UPPER函数可以将一个文本中的所有小写字母转换为大写字母", "text7usage": "UPPER(文本)", "text7example": "UPPER('jayz')返回'JAYZ'", "text8label": "文本转换", "text8intro": "TEXT函数可以将数字转化成文本", "text8usage": "TEXT(数字)", "text8example": "TEXT(3.1415)返回'3.1415'", "text9label": "转化数字", "text9intro": "VALUE函数可以将文本转化为数字", "text9usage": "VALUE(文本)", "text9example": "VALUE('3.1415')返回3.1415", "aboutus.aboutus": "关于我们", "aboutus.overview": "{appTitle}是基于开源开放策略推出的一款产品，通过开源社区开发、维护并发布，借助社区的力量完善产品。", "aboutus.overview2": "{appTitle}产品积极倡导“统一命名空间”（UNS）的理念，旨在通过集中化的数据管理，使数据能够在各种应用场景中得以充分利用并发挥最大价值。", "aboutus.openSourceLicense": "开源许可证: MulanPSL-2.0", "account.enable": "启用", "account.disable": "禁用", "account.confirmpassWord": "确认密码", "account.newpassWord": "新密码", "account.oldPassWord": "旧密码", "account.updateDisplayName": "更新展示名字", "account.displayName": "展示名", "account.passwordMatch": "新密码不匹配!", "account.passwordSame": "新旧密码不能相同!", "account.newUsers": "新增用户", "account.editUsers": "编辑用户", "account.resetpassword": "重置密码", "account.roleSettings": "角色设置", "account.settings": "用户设置", "account.homePage": "个人首页", "account.profile": "个人设置", "account.available": "可用", "account.unavailable": "不可用", "account.account": "账户", "account.email": "邮箱", "account.phone": "手机号", "account.role": "角色", "account.addRole": "新增角色", "account.addRoleName": "新增角色名称", "account.addRoleMax": "最多限制创建10个角色", "advancedUse.advancedUse": "高阶使用", "advancedUse.overview": "我们开放了软件的所有权限，使其更加开发者友好，并为开发者提供了极大的灵活性和可操作性。但是，这也增加了使用软件的难度！", "appSpace.tree": "App空间", "appSpace.name": "应用名称", "appSpace.generateHere": "在此生成您的应用程序", "appGui.yourCodeDatabaseName": "输入您的代码数据库名称", "appSpace.newgenerate": "生成", "appSpace.coding": "编码", "appSpace.add": "添加", "appSpace.yourAppName": "在此处键入您的应用程序名称", "appSpace.go": "前往", "appSpace.newPage": "新建页面", "appSpace.showPage": "显示页面", "appSpace.setHomepage": "设置首页", "appSpace.deleteHTML": "删除html", "appGui.deploy": "部署", "appGui.deployOk": "部署成功！", "appGui.targetApp": "目标应用", "appGui.targetHTML": "目标HTML", "appGui.goAppDisplay": "前往应用显示", "appGui.databaseName": "实例", "appGui.aiError": "正在生成,稍后再试", "appGui.password": "密码", "appGui.enterCodeCommand": "输入您的代码命令", "appGui.saveSuccess": "保存成功！", "appGui.goHTML": "跳转至页面", "collectionFlow.flowTemplate": "连接模板", "collectionFlow.newFlow": "新建数据连接", "flowEditor.process": "查找流程", "flowEditor.nodeManagement": "节点管理", "eventFlow.newFlow": "新建事件流程", "dashboards.dashboardsTemplate": "展示模板", "dashboards.preview": "预览", "dashboards.newDashboard": "新建仪表盘", "global.tipCheckbox": "下次登录不再显示", "global.tipNextOne": "下一条", "global.tipLastOne": "上一条", "global.tipExit": "退出", "global.tipDone": "完成", "global.tipGo": "确认", "global.tipInfo": "在这里，你可以：", "global.tip1Text1": "自定义路由配置菜单，细化权限管理，选择图标。", "global.tip2Text1": "创建命名空间并利用AI生成应用程序。", "global.tip3Text1": "支持导入关系型数据和Modbus数据以自动生成工作流和仪表盘。", "global.tip4Text1": "监控MQTT代理的状态。", "global.tip5Text1": "支持GraphQL和PostgreSQL数据库的在线查看和调试。", "global.tip6Text1": "代码存储、CI/CD和部署。", "global.userGuide": "新手指南", "global.userGuideLabel1": "数据建模", "global.userGuideLabel2": "数据连接", "global.userTips": "功能介绍", "home.guideText": "本次导览将为您介绍我们提供的关键功能和特性，确保您能够顺利且成功地开始使用。", "home.guideUnsTitle": "建立数据模型", "home.guide1Title": "首页", "home.guide1Text1": "首页是通往所有系统<strong>功能</strong>和<strong>应用</strong>的门户。", "home.guide1Text2": "<strong>点击卡片或图标</strong>即可快速访问核心模块。", "home.guide2Title": "顶部栏", "home.guide2Text1": "导航栏可以帮助你访问{appTitle}的任何页面。", "home.guide3Title": "顶部栏", "home.guide3Text1": "导航栏功能键区域包括：", "home.guide3Text2": "<strong>帮助：</strong> 重新访问新手指南。", "home.guide3Text3": "<strong>用户：</strong> 查看和编辑你的用户信息。", "home.guide3Text4": "<strong>编辑菜单：</strong> 自定义菜单。", "home.guide3Text5": "<strong>切换：</strong> 在两种导航风格之间切换。", "home.guide3Text6": "<strong>搜索：</strong> 搜索菜单,点击快速跳转到对应的菜单。", "home.guide3Text7": "<strong>待办：</strong> 查看待办中心。", "home.guide4Title": "系统菜单", "home.guide4Text1": "{appTitle}采用<strong>一级菜单 + 二级菜单</strong>的结构，方便快速查找和切换。", "home.guide4Text2": "<strong>一级菜单：</strong>展示主要功能类别。", "home.guide4Text3": "<strong>二级菜单：</strong>子导航中的详细功能模块。", "home.guide5Title": "准备深入了解吗？", "home.guide5Text1": "继续前往数据建模指南，以获取更多关于有效组织和结构化数据的见解。", "home.guide1Next": "为我进行引导", "home.guide1Exit": "我不需要帮助，我自己来", "notice.notification": "消息通知", "notice.batchDelete": "批量删除", "notice.batchRead": "批量已读", "notice.read": "已读", "notice.unRead": "未读", "notice.noticeContent": "通知内容", "notice.batchDeleteSuccess": "批量删除成功！", "notice.batchReadSuccess": "批量已读成功！", "notice.deleteTip": "确定删除这条通知？", "notice.deleteMoreTip": "确定删除这些通知？", "plugin.installed": "已安装", "plugin.notInstall": "未安装", "plugin.installFail": "安装失败", "plugin.install": "安装", "plugin.reInstall": "重新安装", "plugin.errorTitle": "加载失败", "plugin.retry": "重试", "route.flowEditor": "数据连接编辑", "route.eventFlowEditor": "事件流程编辑", "route.dashboardsPreview": "数据看板预览", "route.appGUI": "AI图形界面", "route.appPreview": "AI应用预览", "route.appIframe": "AI应用展示", "rule.required": "必填项", "rule.characterLimit": "字符长度限制", "rule.illegality": "不合法", "rule.invalidChars": "请输入汉字、字母、数字或 _-.@&+ 符号", "rule.in": "于", "rule.greaterThanThreshold": "超过预定的阀值", "rule.lessThanThreshold": "小于预定的阀值", "rule.lessEqualThreshold": "小于等于预定的阀值", "rule.greaterEqualThreshold": "大于等于预定的阀值", "rule.equalThreshold": "等于预定的阀值", "rule.noEqualThreshold": "不等于预定的阀值", "rule.deal": "请及时处理", "rule.alertCancel": "报警消失", "rule.currentValue": "当前值为", "rule.customCharacterLimit": "字符长度限制{length}", "rule.phone": "手机号无效", "rule.password": "只支持英文大小写、数字、特殊字符", "streams.dataSource": "数据源", "streams.functionType": "函数类型", "streams.windowType": "窗口类型", "streams.intervalValue": "间隔值", "streams.intervalOffset": "间隔偏移", "streams.slidingValue": "滑动值", "streams.tolValue": "容差值", "streams.countValue": "计数值", "streams.trigger": "触发器", "streams.delayTime": "延迟时间", "streams.watermark": "水印", "streams.deleteMark": "删除标记", "streams.fillHistory": "填充历史", "streams.ignoreUpdate": "忽略更新", "streams.ignoreExpired": "忽略过期", "streams.startTime": "开始时间", "streams.endTime": "结束时间", "streams.yes": "是", "streams.no": "否", "streams.whereCondition": "where条件表达式", "streams.havingCondition": "having条件表达式", "streams.advancedOptions": "高级流选项", "streams.referenceField": "引用字段", "streams.startWith": "以…开始", "streams.endWith": "以…结束", "streams.countSlidingLessThanCountValue": "滑动值必须小于计数值", "uns.tree": "工厂模型", "uns.treeList": "工厂模型", "uns.inputText": "搜索输入文本", "uns.addTemplate": "新建模板", "uns.importNamespace": "导入命名空间", "uns.export": "导出", "uns.sqlEditor": "SQL编辑器", "uns.namespace": "命名空间", "uns.templateDescription": "模板描述", "uns.dataOperation": "数据展示", "uns.definition": "定义", "uns.payload": "消息体", "uns.model": "文件夹", "uns.MQTTAccessPoint": "MQTT接入点", "uns.MQTTAccessMethod": "MQTT接入方式", "uns.MQTTUrl": "MQTT 地址", "uns.MQTTPort": "MQTT 端口", "uns.graphqlUrl": "GraphQL 地址", "uns.upload": "Broker 信息", "uns.dbInfo": "数据库信息", "uns.historyData": "历史数据", "uns.realtimeData": "实时数据", "uns.queryOffset": "查询偏移量", "uns.queryLimit": "查询数量", "uns.apiKey": "密钥", "uns.topic": "Topic", "uns.instance": "文件", "uns.optional": "选填", "uns.description": "描述", "uns.databaseType": "数据类型", "uns.timeSeries": "时序型", "uns.relational": "关系型", "uns.autoFlow": "数据连接", "uns.mockData": "模拟数据", "uns.autoDashboard": "数据看板", "uns.deleteAutoFlow": "删除数据连接", "uns.deleteAutoDashboard": "删除数据看板", "uns.dashboard": "仪表盘", "uns.type": "类型", "uns.minute": "分钟", "uns.second": "秒", "uns.hour": "小时", "uns.deleteFile": "确认删除该文件？", "uns.deleteFolder": "确认删除该文件夹？", "uns.deleteFolderTip": "该操作会删除此文件下所有子文件夹和子文件！", "uns.front": "前端配置", "uns.backend": "后端配置", "uns.key": "键", "uns.value": "值", "uns.newSuccessfullyAdded": "新增成功", "uns.theFileFormatOnlySupportsXlsx": "文件格式仅支持.xlsx和.json！", "uns.pleaseUploadTheFile": "请上传文件！", "uns.PartialDataImportFailed": "部分数据导入失败！您确定要下载失败的数据吗？", "uns.totalModel": "文件夹总数", "uns.totalInstance": "文件总数", "uns.allConnections": "所有Mqtt连接", "uns.liveConnections": "实时Mqtt连接", "uns.topologyMap": "拓扑图", "uns.topology": "拓扑图", "uns.content": "内容", "uns.showLess": "显示更少", "uns.showMore": "显示更多", "uns.copyToClipboard": "复制到剪贴板", "uns.pleaseInputName": "请输入名称", "uns.nameFormat": "仅支持中文、字母、数字、下划线（_）和连字符（-）", "uns.pleaseInputNamespace": "请输入命名空间", "uns.modelFormat": "不支持特殊字符", "uns.pleaseInputKeyName": "请输入键名", "uns.pleaseSelectKeyType": "请选择类型", "uns.keyNameFormat": "字母开头，支持字母、数字和下划线", "uns.pleaseInputValue": "请输入值", "uns.pleaseSelectUnit": "请选择单位", "uns.importFinished": "导入完成", "uns.index": "索引", "uns.alias": "别名", "uns.pleaseEnterJSON": "请输入JSON数据", "uns.errorInTheSyntaxOfTheJSON": "JSON语法有错误,请更正", "uns.mqttServer": "MQTT服务器地址和端口", "uns.mqttClientId": "客户端ID", "uns.mqttTopic": "订阅的主题", "uns.mqttTopicPosted": "发布的主题", "uns.mqttQos": "消息质量服务等级", "uns.mqttMessage": "要发布的消息内容", "uns.mqttCreateClient": "创建一个MQTT异步客户端", "uns.mqttSetOptions": "设置连接选项", "uns.mqttConnect": "设置回调，处理接收到的消息", "uns.ConnectToMQTTServer": "连接到MQTT服务器", "uns.mqttPublish": "发布消息", "uns.mqttDisconnect": "等待一段时间后断开连接", "uns.calculation": "计算型", "uns.calculationType": "计算类型", "uns.realtime": "实时", "uns.thisNodeHasAssociatedComputingNodes": "该节点有已关联的节点，确认全部删除？", "uns.realtimeCalculation": "实时计算", "uns.historicalCalculation": "历史计算", "uns.instanceCountStatistics": "文件数统计", "uns.variable": "变量", "uns.collectionFlowDetail": "采集流程详情", "uns.CollectionFlowName": "采集流程名称", "uns.flowTemplate": "流程模板", "uns.displayName": "显示名称", "uns.remark": "备注", "uns.position": "位置", "uns.persistence": "持久化", "uns.eg": "如", "uns.dependent": "依赖", "uns.true": "是", "uns.false": "否", "uns.forMoreInformationPleaseSearch": "查看更多请搜索", "uns.aggregation": "聚合", "uns.frequency": "频率", "uns.aggregationTarget": "聚合目标", "uns.searchInstance": "搜索实例", "uns.importDocument": "导入附件", "uns.importDocumentMax": "最大附件大小: 10 MB", "uns.pleaseSelectTheInstanceToExport": "请选择需要导出的文件", "uns.exportFolderTip": "导出选中文件夹下所有文件", "uns.exportFileTip": "可单独导出文件", "uns.exportFileToolTip": "仅支持导出关系型和时序型的文件", "uns.downloadAll": "全部下载", "uns.exportSuccessful": "导出成功！", "uns.noData": "无数据", "uns.pleaseEnterAtLeastOneAttribute": "请输入至少一个属性", "uns.select100Items": "全选（最多100条）", "uns.editSuccessful": "编辑成功", "uns.source": "来源", "uns.newFolder": "新建文件夹", "uns.newFile": "新建文件", "uns.pasteFolder": "粘贴文件夹", "uns.pasteFile": "粘贴文件", "uns.areYouSureToDeleteThisLabel": "确认删除该标签？", "uns.newLabel": "新建标签", "uns.generationTemplate": "生成模板", "uns.referenceTemplate": "引用模板", "uns.sourceTemplate": "来源模板", "uns.referenceDataSource": "引用数据源", "uns.hasTemplate": "含模板", "uns.hasLabel": "含标签", "uns.folderDescription": "文件夹描述", "uns.fileDescription": "文件描述", "uns.mainKey": "主键", "uns.noTemplate": "暂无引用模板", "uns.noLabel": "暂无关联标签", "uns.last5minutes": "过去5分钟", "uns.last30minutes": "过去30分钟", "uns.last1hour": "过去1小时", "uns.last6hours": "过去6小时", "uns.last24hours": "过去24小时", "uns.last1week": "过去1周", "uns.last6weeks": "过去6周", "uns.last1year": "过去1年", "uns.copyTip": "请先复制文件夹或文件！", "uns.attributeGenerationMethod": "属性生成方式", "uns.reverseGeneration": "逆向生成", "uns.TableNoFieldsTip": "该表没有字段，请换一个！", "uns.noFieldsTip": "请进行下一步获取键列表！", "uns.noTablesTip": "暂无表数据", "uns.batchReverseGeneration": "批量逆向生成", "uns.schemaGenerated": "已生成架构", "uns.treeNoCheckedTip": "请勾选需要逆向生成的节点！", "uns.topicDuplicateTip": "选中节点名称有重复，请修改！", "uns.otherTopic": "其他Topic", "uns.clearMsg": "清除消息", "uns.reimport": "重新导入", "uns.reference": "引用", "uns.referenceTarget": "引用目标", "uns.fileType": "文件类型", "uns.guideVideo1Title": "从UNS模型开始", "uns.guideVideo1Info": "这是组织数据的第一步。", "uns.guideVideo2Title": "访问数据源", "uns.guideVideo2Info": "这是你将真实世界的数据连接到你的数字工作空间的地方。", "uns.editDetails": "编辑明细", "uns.expandedInformation": "扩展属性", "uns.namespaceTooltip": "对相关文件进行分组的主题前缀/文件路径（例如/factory/line1）。", "uns.aliasTooltip": "自动生成为<文件名>-<UUID>（例如press01-temp-b8f8c7d2）全局唯一，兼作数据库表名。保存后只读。", "uns.dataTypeTooltip-TimeSeries": "带时间戳的传感器/日志数据。", "uns.dataTypeTooltip-Relational": "经典的行和列。", "uns.dataTypeTooltip-Calculation": "将任何主题的属性与自定义表达式组合在一起。", "uns.dataTypeTooltip-Aggregation": "将两个主题的JSON有效载荷合并到一个JSON数组中。", "uns.dataTypeTooltip-Reference": "引用其它时序或者关系型的主题。", "uns.attributeGenerationMethodTooltip-Custom": "手动定义每个属性。", "uns.attributeGenerationMethodTooltip-Template": "从预定义的架构开始。", "uns.attributeGenerationMethodTooltip-ReverseGeneration": "自动检测JSON示例或现有DB表中的字段和类型。", "uns.keyTooltip": "单击加号添加属性，单击减号删除属性。", "uns.labelTooltip": "单个标签值可帮助您对文件进行分组或搜索（例如生产、测试）。", "uns.mockDataTooltip": "打开一个预构建的Node-RED流，该流将示例消息发布到此主题。稍后在Node-RED中配置详细信息。", "uns.autoDashboardTooltip": "生成一个连接到此文件数据的启动Grafana仪表板。", "uns.persistenceTooltip": "将每条MOTT消息存储在备份数据库表（Alias）中。支持保留和索引。", "uns.variablePickerTooltip": "选择一个主题字段以将其转换为变量。单击加号添加更多内容。", "uns.variableChipsTooltip": "通过将所选主题字段设置为变量，可以自由单击并使用它。", "uns.expressionTooltip": "在此处编写数学、逻辑或字符串运算。使用如上所示的变量名。示例：AVG(变量1、变量2)*60", "uns.functionCalculatorTooltip": "浏览现成的函数并一键插入。", "uns.frequencyTooltip": "选择聚合的运行频率（例如1秒、3分钟、6小时）。", "uns.aggregationTargetTooltip": "选择两个或多个源文件（实例），其JSON有效载荷将合并到一个数组中。", "uns.originalName": "原始名称", "uns.labelMaxLength": "{label}最多{length}个字符", "uns.duplicateKeyNameTip": "键名重复", "uns.batchPersistence": "批量持久化", "uns.batchMockData": "批量模拟数据", "uns.batchAutoDashboard": "批量数据看板", "uns.timeReferenceTooltip": "选择一个文件引用该文件的质量码和时间戳", "uns.fieldsRequiredTip": "至少1个属性（不包括默认属性）", "common.url": "地址", "common.usage": "用法", "common.example": "示例", "common.notFound": "未找到", "common.pageNotFound": "对不起，您访问的页面不存在", "common.pageNoPermission": "抱歉，您无权访问此页面", "common.menuList": "菜单列表", "common.networkFailed": "网络连接失败，请检查网络", "common.noPermission": "无权限", "common.interfaceNotExist": "有接口不存在", "common.serverBusy": "服务器繁忙, 请稍后再试", "common.prev": "上一步", "common.next": "下一步", "common.back": "返回", "common.goHome": "返回首页", "common.update": "更新", "common.port": "端口", "common.save": "保存", "common.unSave": "不保存", "common.unsavedChanges": "该页面信息暂未保存，是否保存?", "common.creationTime": "创建时间", "common.processingTime": "处理时间", "common.detail": "明细", "common.name": "名称", "common.document": "附件", "common.cancel": "取消", "common.confirm": "确认", "common.edit": "编辑", "common.todo": "待办中心", "common.all": "全部", "common.toDo": "待办", "common.completed": "已办", "common.myCompletedList": "我的已办", "common.taskCenter": "待办中心", "common.origin": "模块", "common.task": "任务", "common.total": "总共", "common.processTask": "处理任务", "common.ToDoPerson": "待办人", "common.completedPerson": "已办人", "common.recipient": "接收人", "common.copy": "复制", "common.viewLabels": "查看标签", "common.viewTemplate": "查看模版", "common.paste": "粘贴", "common.createNewFolder": "创建新文件夹", "common.createNewFile": "创建新文件", "common.expandFolder": "展开文件夹", "common.collapseFolder": "收起文件夹", "common.create": "创建", "common.download": "下载", "common.downloadTemplate": "下载模板", "common.latestUpdate": "更新时间", "common.deleteConfirm": "您确认要删除吗？", "common.uninstallConfirm": "您确认要卸载吗？", "common.clearData": "业务数据将被清除！", "common.deleteTemplateConfirm": "删除模版会同步删除模版关联的文件，您确认要删除吗？", "common.copyConfirm": "复制成功，您确定要打开复制的流程吗?", "common.confirmOpt": "您确认要进行该操作吗？", "common.send": "发送", "common.question": "向聊天机器人提问", "common.chatbot": "你好！👋 今天有什么我能帮忙的吗？", "common.logout": "退出登录", "common.settings": "设置", "common.information": "消息", "common.timezone": "时区", "common.language": "语言", "common.theme": "主题", "common.themeColor": "主题色", "common.dark": "暗色", "common.light": "亮色", "common.darkChartreuse": "暗黑绿", "common.followSystem": "跟随系统", "common.lightChartreuse": "亮色绿", "common.optsuccess": "操作成功!", "common.fullScreen": "全屏", "common.exitFullScreen": "退出全屏", "common.theFileFormatType": "文件格式仅支持{fileType}！", "common.theFileSizeMax": "文件大小不能超过{size}！", "common.status": "状态", "common.operation": "操作", "common.refresh": "刷新", "common.refreshSuccessful": "刷新成功", "common.close": "关闭", "common.closeOther": "关闭其他", "common.welcome": "欢迎来到{appTitle}！", "common.excellence": "助力企业数字化转型！", "common.delete": "删除", "common.import": "导入", "common.deleteSuccessfully": "删除成功！", "common.copySuccess": "复制成功", "common.updateRouteSuccess": "更新路由成功", "common.deleteHtmlSuccess": "页面删除成功！", "common.settingSuccess": "设置成功！", "common.UrlLose": "地址丢失", "common.running": "运行中", "common.stopped": "停止", "common.pending": "加载中", "common.draft": "草稿", "common.items": "项", "common.calculator": "函数计算器", "common.expression": "表达式", "common.conditional": "条件运算", "common.basic": "基础计算", "common.searchFunction": "搜索函数名", "common.open": "打开", "common.search": "搜索", "common.noPermissionMessage": "您没有权限执行该操作，请联系管理员。", "common.select": "请选择", "common.fileList": "文件列表", "common.share": "分享", "common.nodeRed": "数据连接", "common.apps": "AI应用集", "common.gui": "AI开发工具", "common.template": "模板", "common.label": "标签", "common.custom": "自定义", "common.confirmInstall": "确认安装？", "common.confirmUnInstall": "确认卸载？", "common.install": "安装", "common.installed": "已安装", "common.unInstall": "卸载", "common.installedSuccess": "安装成功！", "common.unInstalledSuccess": "卸载成功！", "common.grafanaDesign": "Grafana面板", "common.change": "切换", "common.table": "表", "common.clearSearchInput": "清除搜索输入", "common.upload": "上传", "common.length": "长度", "common.subscribe": "订阅", "common.reset": "重置", "common.importProgress": "导入进度", "common.uploadApp": "上传APP", "common.pagePermission": "页面权限", "common.actionPermission": "操作权限", "common.commonPlaceholder": "请输入", "common.searchPage": "搜索页面", "common.selectTree": "请在右侧选择要查看的内容", "common.loadMore": "加载更多...", "common.errorInfo": "错误信息", "common.searchPlaceholder": "请输入名称或描述进行搜索", "common.searchPlaceholderUns": "请输入名称/别名/路径进行搜索", "common.searchPlaceholderTem": "请输入名称进行搜索", "common.searchPlaceholderLabel": "请输入名称进行搜索", "common.unconfirmed": "未确认", "common.confirmed": "已确认", "common.confirmAll": "全部确认", "common.new": "新", "common.design": "设计", "common.overview": "概述", "common.history": "历史", "common.partialFailure": "部分失败！", "common.prohibitSpacesTip": "禁止只输入空格", "common.password": "密码", "common.connected": "连接", "common.unconnected": "未连接", "common.nextStep": "下一步", "common.clickSourceFlow": "点击此处设置SourceFlow的数据连接。", "common.dev": "开发者", "common.version": "版本", "common.description": "描述", "common.states": "状态", "common.config": "配置"}