{"restConnect": "REST Connect", "appSecretKey": "App Key", "restApiUrl": "RestAPI Url", "instructions": "Call Instructions", "createSecretKey": "Apply for Access Credentials", "addSecretKeyTip": "Include Credentials in Request Headers", "restApiAccessMethod": "RestAPI Access Method", "websocketAccessMethod": "websocket Access Method", "websocketUrl": "Websocket Url", "mqttAccessMethod": "MQTT Access Method", "request": "Request", "response": "Response", "push": "<PERSON><PERSON>", "commonHeaderTip": "Request header information:", "realTimeDataSubscription": "Real-time data subscription", "eventSubscription": "Event subscription", "versionInfo": "Version Information", "commandEnumerationValue": "Command Enum Value", "messageDirection": "Direction", "commandEnumerationType": "Enum Type", "clientToServer": "Client To Server", "serverToClient": "Server To Client", "subscriptionResponse": "Subscription Response", "realTimeValuePush": "Real-time Value Push", "metadataChangePush": "Metadata <PERSON> Push (not currently supported)", "allValues": "All Values", "partValues": "Portion Value", "fileAliasing": "<PERSON> ", "fileName": "File Name", "supportWildcard": "Topic collection, supported.* wildcard", "responseCode": "Response Code", "requestCommandNumber": "Request Command Number", "collectorAlias": "Collector <PERSON><PERSON>", "eventContent": "Event Content, BASE64 code", "logicFun": "Logic Functions", "mathFun": "Math Functions", "dateFun": "Date Functions", "textFun": "Text Functions", "fieldTypeSTRING": "Text", "fieldTypeDOUBLE": "Number", "fieldTypeINT": "Number", "fieldTypeLONG": "Number", "fieldTypeFLOAT": "Number", "fieldTypeDECIMAL": "Number", "fieldTypeDATETIME": "Date", "fieldTypeENUM": "Enum", "fieldTypeSET_ENUM": "Array", "fieldTypeSYSCODE": "System Code", "fieldTypeSET_SYSCODE": "Array", "fieldTypeSYSDEPT": "Department", "fieldTypeSYSSTAFF": "Staff", "fieldTypeSUBFORM": "Array", "fieldTypeLINKDATA": "Linked Data", "fieldTypeCOUNTER": "Serial Number", "logic0label": "Logical AND", "logic0intro": "The AND function returns TRUE if all arguments are true; returns FALSE if any argument is false", "logic0usage": "AND(logical1, logical2, ...)", "logic0example": "AND({Chinese}>90, {Math}>90, {English}>90), returns true if all three scores are >90, otherwise returns false", "logic1label": "Logical OR", "logic1intro": "The OR function returns TRUE if any argument is true; returns FALSE if all arguments are false", "logic1usage": "OR(logical1, logical2, ...)", "logic1example": "OR({Chinese}>90, {Math}>90, {English}>90), returns true if any score is >90, otherwise returns false", "logic2label": "TRUE", "logic2intro": "The TRUE function returns the logical value TRUE", "logic2example": "N/A", "logic3label": "FALSE", "logic3intro": "The FALSE function returns the logical value FALSE", "logic3example": "N/A", "logic4label": "IF", "logic4intro": "The IF function tests a condition and returns one value if true, another if false", "logic4usage": "IF(logical_test, value_if_true, value_if_false)", "logic4example": "IF({Chinese}>60, 'Pass', 'Fail'), returns 'Pass' when Chinese score >60, otherwise returns 'Fail'", "logic5label": "IFS", "logic5intro": "The IFS function checks multiple conditions and returns a value corresponding to the first TRUE condition", "logic5usage": "IFS(logical1, value1, logical2, value2, ...)", "logic5example": "IFS({Chinese}>90, 'Excellent', {Chinese}>80, 'Good', {Chinese}>=60, 'Pass', {Chinese}<60, 'Fail'), returns evaluation based on score", "logic6label": "NOT", "logic6intro": "The NOT function returns the opposite of the given logical value", "logic6usage": "NOT(logical)", "logic6example": "NOT({Chinese}>60), returns false if Chinese score is >60, otherwise returns true", "logic7label": "XOR", "logic7intro": "The XOR function returns the exclusive OR of all arguments", "logic7usage": "XOR(logical1, logical2, ...)", "logic7example": "XOR({Chinese}>90, {Math}>90), returns false if both scores are >90 or both are <90; returns true if exactly one score is >90", "logic8label": "ISEMPTY", "logic8intro": "The ISEMPTY function checks if a value is empty text, empty object, or empty array", "logic8usage": "ISEMPTY(value)", "logic8example": "N/A", "math0label": "Absolute Value", "math0intro": "The ABS function returns the absolute value of a number", "math0usage": "ABS(number)", "math0example": "ABS(-8) returns 8, which is the absolute value of -8", "math1label": "Average", "math1intro": "The AVERAGE function returns the arithmetic mean of the arguments", "math1usage": "AVERAGE(number1, number2, ...)", "math1example": "AVERAGE({Chinese}, {Math}, {English}) returns the average score of three subjects", "math2label": "Count", "math2intro": "The COUNT function counts the number of items", "math2usage": "COUNT(value1, value2, ...)", "math2example": "COUNT(<PERSON>, <PERSON>, <PERSON>, <PERSON>) returns 4, which is the number of people", "math3label": "Round", "math3intro": "The FIXED function rounds a number to specified decimals and returns text", "math3usage": "FIXED(number, decimals)", "math3example": "FIXED(3.1415, 2) returns '3.14'", "math4label": "Integer", "math4intro": "The INT function returns the integer part of a number", "math4usage": "INT(number)", "math4example": "INT(3.1415) returns 3, the integer part of 3.1415", "math5label": "Logarithm", "math5intro": "The LOG function returns the logarithm of a number with the specified base", "math5usage": "LOG(number, base)", "math5example": "LOG(100, 10) returns 2, which is the logarithm of 100 with base 10", "math6label": "<PERSON><PERSON><PERSON>", "math6intro": "The MOD function returns the remainder after division", "math6usage": "MOD(dividend, divisor)", "math6example": "MOD(4, 3) returns 1, which is the remainder of 4/3", "math7label": "Maximum", "math7intro": "The MAX function returns the largest value in a set of values", "math7usage": "MAX(number1, number2, ...)", "math7example": "MAX({Chinese}, {Math}, {English}) returns the highest score among three subjects", "math8label": "Minimum", "math8intro": "The MIN function returns the smallest value in a set of values", "math8usage": "MIN(number1, number2, ...)", "math8example": "MIN({Chinese},{Math},{English}) returns the lowest score among three subjects", "math9label": "Power", "math9intro": "The POWER function returns a number raised to a power", "math9usage": "POWER(number, power)", "math9example": "POWER(3, 2) returns 9, which is 3 squared", "math10label": "Product", "math10intro": "The PRODUCT function multiplies all the numbers given as arguments", "math10usage": "PRODUCT(number1, number2, ...)", "math10example": "PRODUCT({Price}, {Quantity}) returns total price, which is price multiplied by quantity", "math11label": "Square Root", "math11intro": "The SQRT function returns the positive square root of a number", "math11usage": "SQRT(number)", "math11example": "SQRT(9) returns 3, which is the square root of 9", "math12label": "Sum", "math12intro": "The SUM function adds all the numbers specified as arguments", "math12usage": "SUM(number1, number2, ...)", "math12example": "SUM({Chinese}, {Math}, {English}) returns the total score of three subjects", "math13label": "Weighted Sum", "math13intro": "The SUMPRODUCT function multiplies corresponding components and returns the sum of those products", "math13usage": "SUMPRODUCT(array1, array2, ...)", "math13example": "SUMPRODUCT([1,2,3], [0.1,0.2,0.3]) returns 1.4, which is 1×0.1 + 2×0.2 + 3×0.3", "math14label": "Random Number", "math14intro": "The RANDOM function returns a random number between 0 and 1", "math14usage": "RANDOM()", "math14example": "RANDOM() generates a random number", "date0label": "Date Object", "date0intro": "The DATE function converts a timestamp to a date object", "date0usage": "DATE(timestamp)", "date0example": "N/A", "date1label": "Day", "date1intro": "The DAY function returns the day of the month for a given date", "date1usage": "DAY(timestamp)", "date1example": "N/A", "date2label": "Days Between", "date2intro": "The DAYS function returns the number of days between two dates", "date2usage": "DAYS(end_date, start_date)", "date2example": "N/A", "date3label": "Date Difference", "date3intro": "The DATEDIF function calculates the difference between two dates in years, months, days, hours, minutes, or seconds", "date3usage": "DATEDIF(start_date, end_date, [unit]), unit can be 'y', 'M', 'd', 'h', 'm', 's'", "date3example": "DATEDIF({OrderTime}, {PaymentTime}, 'h'), if order time is 9:00 and payment time is 10:30 same day, returns 1.5 hours", "date4label": "Date Add/Subtract", "date4intro": "The DATEDELTA function adds or subtracts a specified number of days to a date", "date4usage": "DATEDELTA(date, days)", "date4example": "N/A", "date5label": "Hour", "date5intro": "The HOUR function returns the hour of a timestamp", "date5usage": "HOUR(timestamp)", "date5example": "N/A", "date6label": "Month", "date6intro": "The MONTH function returns the month of a date", "date6usage": "MONTH(timestamp)", "date6example": "N/A", "date7label": "Minute", "date7intro": "The MINUTE function returns the minute of a timestamp", "date7usage": "MINUTE(timestamp)", "date7example": "N/A", "date8label": "Now", "date8intro": "The NOW function returns the current date and time", "date8example": "N/A", "date9label": "Second", "date9intro": "The SECOND function returns the seconds of a timestamp", "date9usage": "SECOND(timestamp)", "date9example": "N/A", "date10label": "Today", "date10intro": "The TODAY function returns today's date", "date10example": "N/A", "date11label": "Year", "date11intro": "The YEAR function returns the year of a date", "date11usage": "YEAR(timestamp)", "date11example": "N/A", "text0label": "Concatenate", "text0intro": "The CONCATENATE function joins several text strings into one text string", "text0usage": "CONCATENATE(text1, text2, ...)", "text0example": "CONCATENATE('Class 3B', '<PERSON>') returns 'Class 3B <PERSON>'", "text1label": "Exact", "text1intro": "The EXACT function compares two text strings and returns TRUE if they are exactly the same, FALSE otherwise", "text1usage": "EXACT(text1, text2)", "text1example": "EXACT({Phone}, {WinningNumber}), returns true if they match, false if they don't", "text2label": "Left", "text2intro": "The LEFT function returns the specified number of characters from the start of a text string", "text2usage": "LEFT(text, num_chars)", "text2example": "LEFT('Class 3B <PERSON>', 2) returns 'Cl', the first 2 characters", "text3label": "Length", "text3intro": "The LEN function returns the number of characters in a text string", "text3usage": "LEN(text)", "text3example": "L<PERSON>('Morning sun') returns 11, as there are 11 characters including space", "text4label": "Lower", "text4intro": "The LOWER function converts text to lowercase", "text4usage": "LOWER(text)", "text4example": "LOWER('JAYZ') returns 'jayz'", "text5label": "Right", "text5intro": "The RIGHT function returns the specified number of characters from the end of a text string", "text5usage": "RIGHT(text, num_chars)", "text5example": "<PERSON><PERSON><PERSON>('Class 3B <PERSON>', 3) returns 'hou', the last 3 characters", "text6label": "<PERSON><PERSON>", "text6intro": "The TRIM function removes leading and trailing spaces from text", "text6usage": "TRIM(text)", "text6example": "TRIM('   Lanzhuo Industrial Internet   ') returns 'Lanzhuo Industrial Internet'", "text7label": "Upper", "text7intro": "The UPPER function converts text to uppercase", "text7usage": "UPPER(text)", "text7example": "UPPER('jayz') returns 'JAYZ'", "text8label": "Text", "text8intro": "The TEXT function converts a number to text", "text8usage": "TEXT(number)", "text8example": "TEXT(3.1415) returns '3.1415'", "text9label": "Value", "text9intro": "The VALUE function converts text to number", "text9usage": "VALUE(text)", "text9example": "VALUE('3.1415') returns 3.1415", "aboutus.aboutus": "About Us", "aboutus.overview": "{appTitle} is a cutting-edge platform developed and maintained by the open-source community, designed to revolutionize data management and application development through its unique concept of Unified Namespace (UNS).", "aboutus.overview2": "{appTitle} product actively promotes the concept of \"Unified namespace\" (UNS), which aims to enable data to be fully utilized and maximized in a variety of application scenarios through centralized data management.", "aboutus.openSourceLicense": "Open Source License: MulanPSL-2.0", "account.enable": "Enable", "account.disable": "Disable", "account.confirmpassWord": "Confirm Password", "account.newpassWord": "New Password", "account.oldPassWord": "Old Password", "account.updateDisplayName": "Update Your Display Name", "account.displayName": "Display Name", "account.passwordMatch": "The new password that you entered does not match!", "account.passwordSame": "The old and new passwords cannot be the same!", "account.newUsers": "New User", "account.editUsers": "Edit User", "account.resetpassword": "Reset Password", "account.roleSettings": "Role Permission", "account.settings": "User Settings", "account.homePage": "Homepage", "account.profile": "Profile", "account.available": "Available", "account.unavailable": "Unavailable", "account.account": "Account", "account.email": "Email", "account.phone": "Phone", "account.role": "Role", "account.addRole": "New Role", "account.addRoleName": "New Role Name", "account.addRoleMax": "Limit the creation of a maximum of 10 roles", "advancedUse.advancedUse": "Advanced Use", "advancedUse.overview": "We have enhanced our software with additional permissions to provide greater flexibility and control for developers, enabling more effective integration and customization of solutions. While this increase in capability offers significant benefits, it also demands careful management to ensure optimal performance and user experience.", "appSpace.tree": "App Space Tree", "appSpace.name": "App Name", "appSpace.generateHere": "Generate your own here", "appGui.yourCodeDatabaseName": "Enter your code database name", "appSpace.newgenerate": "Generate", "appSpace.coding": "Coding", "appSpace.add": "Add", "appSpace.yourAppName": "Type your app name here", "appSpace.go": "Go", "appSpace.newPage": "New Page", "appSpace.showPage": "Show Page", "appSpace.setHomepage": "Set Homepage", "appSpace.deleteHTML": "Delete HTML", "appGui.deploy": "Deploy", "appGui.deployOk": "Success!", "appGui.targetApp": "Target App", "appGui.targetHTML": "Target HTML", "appGui.goAppDisplay": "Go to App Display", "appGui.databaseName": "Database Name", "appGui.aiError": "Is Building. Please try again later", "appGui.password": "Password", "appGui.enterCodeCommand": "Enter your code command", "appGui.saveSuccess": "Success!", "appGui.goHTML": "go HTML", "collectionFlow.flowTemplate": "Flow Template", "collectionFlow.newFlow": "New SourceFlow", "flowEditor.process": "Search for process", "flowEditor.nodeManagement": "Node Management", "eventFlow.newFlow": "New EventFlow", "dashboards.dashboardsTemplate": "Show Template", "dashboards.preview": "Preview", "dashboards.newDashboard": "New Dashboard", "global.tipCheckbox": "Don’t show again at next login", "global.tipNextOne": "Next", "global.tipLastOne": "Back", "global.tipExit": "Exit", "global.tipDone": "Done", "global.tipGo": "GO", "global.tipInfo": "Here, you can:", "global.tip1Text1": "Customize route configuration menus, refine permission management, and select icons.", "global.tip2Text1": "Create namespaces and leverage AI to generate apps.", "global.tip3Text1": "Import relational data and Modbus data to automatically generate workflows and dashboards.", "global.tip4Text1": "Monitor the status of MQTT brokers.", "global.tip5Text1": "View and debug GraphQL and PostgreSQL databases online.", "global.tip6Text1": "Realize code storage, CI/CD, and deployment.", "global.userGuide": "Beginner’s Guide", "global.userGuideLabel1": "Data Modeling", "global.userGuideLabel2": "Data Connection", "global.userTips": "Tips", "home.guideText": "This tour will guide you through the key features and functions we offer, ensuring you have a smooth and successful start.", "home.guideUnsTitle": "Build Data Model", "home.guide1Title": "Homepage", "home.guide1Text1": "Homepage is the gateway to all system <strong>functions</strong> and <strong>applications</strong>.", "home.guide1Text2": "Click on the <strong>cards or icons</strong> to quickly access core modules.", "home.guide2Title": "Nav Bar", "home.guide2Text1": "The navigation bar allows you to reach any {appTitle} page.", "home.guide3Title": "Common Operations", "home.guide3Text1": "The common operations include:", "home.guide3Text2": "<strong>Help</strong>: View the guide again.", "home.guide3Text3": "<strong>User</strong>: View and edit the user information.", "home.guide3Text4": "<strong>Edit Navigation</strong>: Customize the navigation bar.", "home.guide3Text5": "<strong>Toggle</strong>: Switch between navigation styles.", "home.guide3Text6": "<strong>Search</strong>: Search for the menu, and click the result for quick access.", "home.guide3Text7": "<strong>To<PERSON></strong>: Go to the To-Do Center.", "home.guide4Title": "System Menu", "home.guide4Text1": "{appTitle} uses a <strong>primary menu + secondary menu</strong> structure for quick searching and switching.", "home.guide4Text2": "<strong>Primary Menu</strong>: Display main function categories.", "home.guide4Text3": "<strong>Secondary Menu</strong>: Detailed function modules.", "home.guide5Title": "Ready to dive deeper?", "home.guide5Text1": "Proceed to the Data Modeling Guide for more insights on organizing and structuring your data effectively.", "home.guide1Next": "Let's go!", "home.guide1Exit": "<PERSON><PERSON>", "notice.notification": "Notification", "notice.batchDelete": "<PERSON><PERSON> Delete", "notice.batchRead": "<PERSON><PERSON>", "notice.read": "Read", "notice.unRead": "Unread", "notice.noticeContent": "Notification Content", "notice.batchDeleteSuccess": "Batch deletion successful!", "notice.batchReadSuccess": "<PERSON><PERSON> read successfully!", "notice.deleteTip": "Are you sure you want to delete this notice?", "notice.deleteMoreTip": "Are you sure you want to delete these notices?", "plugin.installed": "Installed", "plugin.notInstall": "UnInstalled", "plugin.installFail": "Installation Failed", "plugin.install": "Install", "plugin.reInstall": "ReInstall", "plugin.errorTitle": "Load Failed", "plugin.retry": "Retry", "route.flowEditor": "SourceFlow Editor", "route.eventFlowEditor": "EventFlow Editor", "route.dashboardsPreview": "Dashboard Preview", "route.appGUI": "App GUI", "route.appPreview": "App Preview", "route.appIframe": "App <PERSON>e", "rule.required": "Required fields", "rule.characterLimit": "Character length limit", "rule.illegality": "Illegality", "rule.invalidChars": "Please enter Chinese characters, letters, numbers or_-.@&+ symbol", "rule.in": "In", "rule.greaterThanThreshold": "Exceed the preset threshold", "rule.lessThanThreshold": "Less than a preset threshold", "rule.lessEqualThreshold": "Less than or equal to the preset threshold", "rule.greaterEqualThreshold": "Greater than or equal to the preset threshold", "rule.equalThreshold": "Equal to a preset threshold", "rule.noEqualThreshold": "Not equal to the preset threshold", "rule.deal": "Please deal with it in time", "rule.alertCancel": "The alert disappears", "rule.currentValue": "The current value is", "rule.customCharacterLimit": "Character length limit {length}", "rule.phone": "Invalid mobile phone number", "rule.password": "Only English case, numbers, and special characters are supported", "streams.dataSource": "Data Source", "streams.functionType": "Function Type", "streams.windowType": "Window Type", "streams.intervalValue": "Interval Value", "streams.intervalOffset": "Interval Offset", "streams.slidingValue": "Sliding Value", "streams.tolValue": "Tolerance Value", "streams.countValue": "Count Value", "streams.trigger": "<PERSON><PERSON>", "streams.delayTime": "Delay Time", "streams.watermark": "Water Mark", "streams.deleteMark": "Delete Mark", "streams.fillHistory": "Fill History", "streams.ignoreUpdate": "Ignore Update", "streams.ignoreExpired": "Ignore Expired", "streams.startTime": "Start Time", "streams.endTime": "End Time", "streams.yes": "Yes", "streams.no": "No", "streams.whereCondition": "Where Condition", "streams.havingCondition": "Having Condition", "streams.advancedOptions": "Advanced Streaming Options", "streams.referenceField": "Reference Field", "streams.startWith": "Start With", "streams.endWith": "End With", "streams.countSlidingLessThanCountValue": "The sliding value must be less than the count value", "uns.tree": "Model", "uns.treeList": "Data Modeling", "uns.inputText": "Please enter keywords", "uns.addTemplate": "New Template", "uns.importNamespace": "Import Namespace", "uns.export": "Export", "uns.sqlEditor": "SQL Editor", "uns.namespace": "Namespace", "uns.templateDescription": "Template Description", "uns.dataOperation": "Broker&DB Info", "uns.definition": "Definition", "uns.payload": "Payload", "uns.model": "Folder", "uns.MQTTAccessPoint": "MQTT Access Point", "uns.MQTTAccessMethod": "MQTT access method provides devices with the ability to report connections.", "uns.MQTTUrl": "MQTT URL", "uns.MQTTPort": "MQTT Port", "uns.graphqlUrl": "GraphQL URL", "uns.upload": "Broker", "uns.dbInfo": "DB Info", "uns.historyData": "History Data", "uns.realtimeData": "Realtime Data", "uns.queryOffset": "Query offset", "uns.queryLimit": "Query limit", "uns.apiKey": "API Key", "uns.topic": "Topic", "uns.instance": "File", "uns.optional": "Optional", "uns.description": "Description", "uns.databaseType": "Data Type", "uns.timeSeries": "Time Series", "uns.relational": "Relational", "uns.autoFlow": "SourceFlow", "uns.mockData": "<PERSON><PERSON>", "uns.autoDashboard": "Auto-Dashboard", "uns.deleteAutoFlow": "Delete SourceFlow", "uns.deleteAutoDashboard": "Delete Auto-Dashboard", "uns.dashboard": "Dashboard", "uns.type": "Type", "uns.minute": "Minute(s)", "uns.second": "Second(s)", "uns.hour": "Hour(s)", "uns.deleteFile": "Delete this file?", "uns.deleteFolder": "Delete this folder?", "uns.deleteFolderTip": "This operation will delete all subfolders and subfiles!", "uns.front": "Frontend Config", "uns.backend": "Backend Config", "uns.key": "Key", "uns.value": "Value", "uns.newSuccessfullyAdded": "Success!", "uns.theFileFormatOnlySupportsXlsx": "The file format only supports .xlsx and .json!", "uns.pleaseUploadTheFile": "Please upload the file!", "uns.PartialDataImportFailed": "Partial data import failed! Are you sure you want to download the failed data?", "uns.totalModel": "Total Folders", "uns.totalInstance": "Total Files", "uns.allConnections": "Total MQTT Connections", "uns.liveConnections": "Live MQTT Connections", "uns.topologyMap": "Topology Map", "uns.topology": "Topology", "uns.content": "Content", "uns.showLess": "Show Less", "uns.showMore": "Show More", "uns.copyToClipboard": "Copy to clipboard", "uns.pleaseInputName": "Please enter name", "uns.nameFormat": "Only supports Chinese characters, letters, numbers, underscores (_), and hyphens (-)", "uns.pleaseInputNamespace": "Please enter namespace", "uns.modelFormat": "No special characters supported", "uns.pleaseInputKeyName": "Please enter key", "uns.pleaseSelectKeyType": "Please select type", "uns.keyNameFormat": "Starts with a letter, followed by letters or digits or underscores", "uns.pleaseInputValue": "Please enter value", "uns.pleaseSelectUnit": "Please select unit", "uns.importFinished": "Import completed", "uns.index": "Index", "uns.alias": "<PERSON><PERSON>", "uns.pleaseEnterJSON": "Please enter JSON data", "uns.errorInTheSyntaxOfTheJSON": "There is an error in the syntax of the JSON. Please correct it", "uns.mqttServer": "MQTT server address and port.", "uns.mqttClientId": "Client ID ", "uns.mqttTopic": "Topic", "uns.mqttTopicPosted": "Topic Posted", "uns.mqttQos": "Message Quality Service Level", "uns.mqttMessage": "The content of the message to be published", "uns.mqttCreateClient": "Create an MQTT asynchronous client", "uns.mqttSetOptions": "Set Connection Options", "uns.mqttConnect": "Set callback to process received messages", "uns.ConnectToMQTTServer": "Connect to MQTT Server", "uns.mqttPublish": "Publish Message", "uns.mqttDisconnect": "Disconnect after waiting for a period of time.", "uns.calculation": "Calculation", "uns.calculationType": "Calculation Type", "uns.realtime": "Real Time", "uns.thisNodeHasAssociatedComputingNodes": "This node has associated nodes. Are you sure to delete them all?", "uns.realtimeCalculation": "Realtime Calculation", "uns.historicalCalculation": "Historical Calculation", "uns.instanceCountStatistics": "Total Files", "uns.variable": "Variable", "uns.collectionFlowDetail": "SourceFlow Detail", "uns.CollectionFlowName": "SourceFlow Name", "uns.flowTemplate": "Flow Template", "uns.displayName": "Display Name", "uns.remark": "Remark", "uns.position": "Position", "uns.persistence": "Persistence", "uns.eg": "e.g.", "uns.dependent": "Dependent", "uns.true": "true", "uns.false": "false", "uns.forMoreInformationPleaseSearch": "For more information, please search", "uns.aggregation": "Aggregation", "uns.frequency": "Frequency", "uns.aggregationTarget": "Aggregation Target", "uns.searchInstance": "Search Instance", "uns.importDocument": "Import Document", "uns.importDocumentMax": "Max. Document Size: 10 MB", "uns.pleaseSelectTheInstanceToExport": "Please select the file to export", "uns.exportFolderTip": "Export all files in the selected folder.", "uns.exportFileTip": "Can export files separately.", "uns.exportFileToolTip": "Only supports exporting relational and time-series files.", "uns.downloadAll": "Download All", "uns.exportSuccessful": "Success!", "uns.noData": "No data", "uns.pleaseEnterAtLeastOneAttribute": "Please enter at least one attribute", "uns.select100Items": "Select All (up to 100 items)", "uns.editSuccessful": "Success!", "uns.source": "Source", "uns.newFolder": "New Folder", "uns.newFile": "New File", "uns.pasteFolder": "Paste Folder", "uns.pasteFile": "Paste File", "uns.areYouSureToDeleteThisLabel": "Delete?", "uns.newLabel": "New Label", "uns.generationTemplate": "Generate Template", "uns.referenceTemplate": "Reference Template", "uns.sourceTemplate": "Source Template", "uns.referenceDataSource": "Reference Data Source", "uns.hasTemplate": "Template", "uns.hasLabel": "Label", "uns.folderDescription": "Folder Description", "uns.fileDescription": "File Description", "uns.mainKey": "Primary Key", "uns.noTemplate": "No reference template available at the moment.", "uns.noLabel": "No associated labels at the moment.", "uns.last5minutes": "Last 5 minutes", "uns.last30minutes": "Last 30 minutes", "uns.last1hour": "Last 1 hour", "uns.last6hours": "Last 6 hours", "uns.last24hours": "Last 24 hours", "uns.last1week": "Last 1 week", "uns.last6weeks": "Last 6 weeks", "uns.last1year": "Last 1 year", "uns.copyTip": "Please copy the folder or file first!", "uns.attributeGenerationMethod": "Attribute Generation Method", "uns.reverseGeneration": "Reverse Generation", "uns.TableNoFieldsTip": "This table has no fields. Please change it!", "uns.noFieldsTip": "Please proceed to the next step to obtain the key list!", "uns.noTablesTip": "No table data available at the moment.", "uns.batchReverseGeneration": "Batch Reverse Generation", "uns.schemaGenerated": "Schema Generated", "uns.treeNoCheckedTip": "Please check the nodes that require reverse generation!", "uns.topicDuplicateTip": "The selected node name is duplicated. Please modify it!", "uns.otherTopic": "Raw Topics", "uns.clearMsg": "Clearing messages...", "uns.reimport": "Import Again", "uns.reference": "Reference", "uns.referenceTarget": "Reference Target", "uns.fileType": "File Type", "uns.guideVideo1Title": "Start With UNS Model", "uns.guideVideo1Info": "It’s the first step to organizing your data.", "uns.guideVideo2Title": "Access Your Data Source", "uns.guideVideo2Info": "This is where you connect real-world data into your digital workspace.", "uns.editDetails": "Edit Details", "uns.expandedInformation": "Extended Information", "uns.namespaceTooltip": "Topic prefix /file path that groups related files (e.g. /factory/line1 ).", "uns.aliasTooltip": "Auto-generated as <file-name>-<UUID> (e.g.press01-temp-b8f8c7d2). Globally unique, doubles as the database table name. Read-only after save.", "uns.dataTypeTooltip-TimeSeries": "Timestamped sensor/log data.", "uns.dataTypeTooltip-Relational": "Classic rows & columns.", "uns.dataTypeTooltip-Calculation": "Combine attributes from any topic with custom expressions.", "uns.dataTypeTooltip-Aggregation": "Fuse two topics' JSON payloads into a single JSON array.", "uns.dataTypeTooltip-Reference": "Quoting other timeSeries or relational topic.", "uns.attributeGenerationMethodTooltip-Custom": "Define every attribute manually.", "uns.attributeGenerationMethodTooltip-Template": "Start from a predefined schema.", "uns.attributeGenerationMethodTooltip-ReverseGeneration": "Auto-detect fields & types from a JSON sample or an existing DB table.", "uns.keyTooltip": "Click + to add attributes, - to remove.", "uns.labelTooltip": "Single tag value to help you group or search files (e.g. production , test ).", "uns.mockDataTooltip": "Flip on a pre-built Node-RED flow that publishes sample messages to this topic. Configure details in Node-RED later.", "uns.autoDashboardTooltip": "Generate a starter Grafana dashboard wired to this file's data.", "uns.persistenceTooltip": "Store every MOTT message in the backing database table (Alias). Supports retention & indexing.", "uns.variablePickerTooltip": "Select a topic field to turn into a variable. Click + to add more.", "uns.variableChipsTooltip": "By making the selected topic field a variable, it is free to click on it and use it.", "uns.expressionTooltip": "Write math, logic, or string ops here. Use variable names exactly as shown above.Example: avg(Variable1,Variable2)*60.", "uns.functionCalculatorTooltip": "Browse ready-made functions and insert them with one click.", "uns.frequencyTooltip": "Choose how often to run the aggregation (e.g. 1 s, 3 min, 6 hour).", "uns.aggregationTargetTooltip": "Select two or more source files (instances) whose JSON payloads will be merged into an array.", "uns.originalName": "Original Name", "uns.labelMaxLength": "{label} must be up to {length} characters", "uns.duplicateKeyNameTip": "Duplicate key names", "uns.batchPersistence": "Batch Persistence", "uns.batchMockData": "<PERSON><PERSON>", "uns.batchAutoDashboard": "Batch Auto-Dashboard", "uns.timeReferenceTooltip": "Select a file to reference its quality code and timestamp.", "uns.fieldsRequiredTip": "At least 1 attribute (excluding default attributes)", "common.url": "URL", "common.usage": "Usage", "common.example": "Example", "common.notFound": "Not Found", "common.pageNotFound": "Sorry, the page you are looking for does not exist", "common.pageNoPermission": "Sorry, you are not authorized to access this page.", "common.menuList": "Module List", "common.networkFailed": "Network connection failed. Please check your network.", "common.noPermission": "No permission.", "common.interfaceNotExist": "Interface does not exist.", "common.serverBusy": "Server is busy. Please try again later.", "common.prev": "The previous step", "common.next": "Next step", "common.back": "Back", "common.goHome": "Go Home", "common.update": "Update", "common.port": "Port", "common.save": "Save", "common.unSave": "Without saving", "common.unsavedChanges": "The information on this page has not been saved yet. Do you want to save it?", "common.creationTime": "Creation Time", "common.processingTime": "Processing Time", "common.detail": "Detail", "common.name": "Name", "common.document": "Document", "common.cancel": "Cancel", "common.confirm": "OK", "common.edit": "Edit", "common.todo": "To-do", "common.all": "All", "common.toDo": "To-do", "common.completed": "Completed", "common.myCompletedList": "My Completed Tasks", "common.taskCenter": "Task Center", "common.origin": "Source", "common.task": "Task", "common.total": "Total", "common.processTask": "Process Task", "common.ToDoPerson": "Owner", "common.completedPerson": "Processor", "common.recipient": "Recipient", "common.copy": "Copy", "common.viewLabels": "View Labels", "common.viewTemplate": "View Template", "common.paste": "Paste", "common.createNewFolder": "Create New Folder", "common.createNewFile": "Create New File", "common.expandFolder": "Expand Folder", "common.collapseFolder": "Collapse Folder", "common.create": "Create", "common.download": "Download", "common.downloadTemplate": "Download Template", "common.latestUpdate": "Latest Update", "common.deleteConfirm": "Delete?", "common.uninstallConfirm": "Are you sure you want to uninstall?", "common.clearData": "Business data will be cleared!", "common.deleteTemplateConfirm": "Deleting a template will synchronously delete the files associated with it. Are you sure you want to delete it?", "common.copyConfirm": "Success. Do you want to start the Copy Flow?", "common.confirmOpt": "Confirm?", "common.send": "Send", "common.question": "Ask chatbot a question", "common.chatbot": "Hi! 👋 How can I assist you today?", "common.logout": "Logout", "common.settings": "Settings", "common.information": "Information", "common.timezone": "Time Zone", "common.language": "Language", "common.theme": "Theme", "common.themeColor": "Theme color", "common.dark": "Dark", "common.light": "Light", "common.darkChartreuse": "Dark Chartreuse", "common.followSystem": "Follow System", "common.lightChartreuse": "Light Chartreuse", "common.optsuccess": "Success!", "common.fullScreen": "Full Screen", "common.exitFullScreen": "Exit Full Screen", "common.theFileFormatType": "File format only supports {fileType}!", "common.theFileSizeMax": "File size cannot exceed {size}!", "common.status": "Status", "common.operation": "Operation", "common.refresh": "Refresh", "common.refreshSuccessful": "Refresh successfully", "common.close": "Close", "common.closeOther": "Close Others", "common.welcome": "Welcome to {appTitle}!", "common.excellence": "Connect, Analyze, Transform – with IIoT Excellence", "common.delete": "Delete", "common.import": "Import", "common.deleteSuccessfully": "Success!", "common.copySuccess": "Success!", "common.updateRouteSuccess": "Success!", "common.deleteHtmlSuccess": "Success!", "common.settingSuccess": "Setting Success!", "common.UrlLose": "URL Lose", "common.running": "RUNNING", "common.stopped": "STOPPED", "common.pending": "PENDING", "common.draft": "DRAFT", "common.items": "Items", "common.calculator": "Function Calculator", "common.expression": "Expression", "common.conditional": "Conditional", "common.basic": "Basic", "common.searchFunction": "Search Function", "common.open": "Open", "common.search": "Search", "common.noPermissionMessage": "You don't have permission to do this, contact your administrator.", "common.select": "Please Select", "common.fileList": "File List", "common.share": "Share", "common.nodeRed": "Node-Red", "common.apps": "Apps", "common.gui": "GUI", "common.template": "Template", "common.label": "Label", "common.custom": "Custom", "common.confirmInstall": "Install?", "common.confirmUnInstall": "Uninstall?", "common.install": "Get", "common.installed": "Installed", "common.unInstall": "Uninstall", "common.installedSuccess": "Success!", "common.unInstalledSuccess": "Success!", "common.grafanaDesign": "Grafana Design", "common.change": "Change", "common.table": "Table", "common.clearSearchInput": "Clear search input", "common.upload": "Upload", "common.length": "Length", "common.subscribe": "Subscription", "common.reset": "Reset", "common.importProgress": "Import Progress", "common.uploadApp": "Upload APP", "common.pagePermission": "Page permissions", "common.actionPermission": "Button permissions", "common.commonPlaceholder": "Please enter", "common.searchPage": "Query a page", "common.selectTree": "Please select what to view on the right", "common.loadMore": "Load more...", "common.errorInfo": "Error Message", "common.searchPlaceholder": "Please enter a name or description to search", "common.searchPlaceholderUns": "Please enter name/alias/path to search", "common.searchPlaceholderTem": "Please enter a name to search", "common.searchPlaceholderLabel": "Please enter a name to search", "common.unconfirmed": "Pending", "common.confirmed": "Confirmed", "common.confirmAll": "Confirm All", "common.new": "new", "common.design": "Design", "common.overview": "Overview", "common.history": "History", "common.partialFailure": "Partial failure!", "common.prohibitSpacesTip": "Prohibit entering only spaces", "common.password": "Password", "common.connected": "Connected", "common.unconnected": "Unconnected", "common.nextStep": "Next step", "common.clickSourceFlow": "click here to set up data connection in SourceFlow.", "common.dev": "<PERSON>", "common.version": "Version", "common.description": "Description", "common.states": "States", "common.config": "Config"}