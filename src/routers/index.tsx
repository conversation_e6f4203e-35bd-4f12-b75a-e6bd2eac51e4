import { Location, useNavigate } from 'react-router-dom';
import Layout from '@/layout';
import Uns from '@/pages/uns';
import Todo from '@/pages/todo';
import GrafanaDesign from '@/pages/grafana-design';
import AppDisplay from '@/pages/app-management/AppDisplay';
import AppSpace from '@/pages/app-management/AppSpace';
import AppG<PERSON> from '@/pages/app-management/AppGUI';
import AppPreview from '@/pages/app-management/AppPreview';
import AppIframe from '@/pages/app-management/AppIframe';
import NotFoundPage from '@/pages/not-found-Page/NotFoundPage';
import NotPage from '@/pages/not-found-Page';
import CollectionFlow from '@/pages/collection-flow';
import FlowPreview from '@/pages/collection-flow/FlowPreview';
import Dashboards from '@/pages/dashboards';
import DashboardsPreview from '@/pages/dashboards/DashboardsPreview';
import Home from '@/pages/home';
import AccountManagement from '@/pages/account-management';
import AboutUs from '@/pages/aboutus';
import AdvancedUse from '@/pages/advanced-use';
import DevPage from '@/pages/dev-page';
import NoPermission from '@/pages/not-found-Page/NoPermission';
import { LOGIN_URL } from '@/common-types/constans';
import Share from '@/pages/share';
import EventFlow from '@/pages/event-flow';
import EventFlowPreview from '@/pages/event-flow/FlowPreview.tsx';
import PluginManagement from '@/pages/plugin-management';
import qs from 'qs';
import { useEffect } from 'react';
import { useBaseStore } from '@/stores/base';
import { getIntl } from '@/stores/i18n-store.ts';

// 根路径重定向到外部login页

const RootRedirect = () => {
  const { currentUserInfo, systemInfo } = useBaseStore((state) => ({
    currentUserInfo: state.currentUserInfo,
    systemInfo: state.systemInfo,
  }));
  const params = qs.parse(window.location.search, { ignoreQueryPrefix: true });
  useEffect(() => {
    console.warn(params);
    if (params?.isLogin) {
      window.location.href = currentUserInfo?.homePage || '/home';
    } else {
      window.location.href = systemInfo?.loginPath || LOGIN_URL;
    }
  }, [params?.isLogin]);
  return null;
};

export const childrenRoutes = [
  {
    path: '/home',
    Component: Home,
  },
  {
    path: '/uns',
    Component: Uns,
  },
  {
    path: '/todo',
    Component: Todo,
    handle: {
      parentPath: '/_common',
      name: getIntl('common.todo', 'To-do'),
      menuNameKey: 'common.todo',
      type: 'all',
    },
  },
  {
    path: '/grafana-design',
    Component: GrafanaDesign,
    handle: {
      parentPath: '/_common',
      name: getIntl('common.grafanaDesign', 'GrafanaDesign'),
      menuNameKey: 'common.grafanaDesign',
    },
  },
  {
    path: '/app-display',
    Component: AppDisplay,
  },
  {
    path: '/app-iframe',
    Component: AppIframe,
    handle: {
      parentPath: '/app-display',
      name: getIntl('route.appIframe', 'AppIframe'),
      menuNameKey: 'route.appIframe',
    },
  },
  {
    path: '/app-space',
    Component: AppSpace,
  },
  {
    path: '/app-gui',
    Component: AppGUI,
    handle: {
      parentPath: '/app-space',
      name: getIntl('route.appGUI', 'AppGUI'),
      menuNameKey: 'route.appGUI',
    },
  },
  {
    path: '/app-preview',
    Component: AppPreview,
    handle: {
      parentPath: '/app-space',
      name: getIntl('route.appPreview', 'AppPreview'),
      menuNameKey: 'route.appPreview',
    },
  },
  {
    path: '/collection-flow',
    Component: CollectionFlow,
  },
  {
    path: '/collection-flow/flow-editor',
    Component: FlowPreview,
    handle: {
      parentPath: '/collection-flow',
      name: getIntl('route.flowEditor', 'SourceFlow Editor'),
      menuNameKey: 'route.flowEditor',
    },
  },
  {
    path: '/EventFlow',
    Component: EventFlow,
  },
  {
    path: '/EvenFlow/Editor',
    Component: EventFlowPreview,
    handle: {
      parentPath: '/EventFlow',
      name: getIntl('route.eventFlowEditor', 'EventFlow Editor'),
      menuNameKey: 'route.eventFlowEditor',
    },
  },
  {
    path: '/dashboards',
    Component: Dashboards,
  },
  {
    path: '/dashboards/preview',
    Component: DashboardsPreview,
    handle: {
      parentPath: '/dashboards',
      name: getIntl('route.dashboardsPreview', 'DashboardsPreview'),
      menuNameKey: 'route.dashboardsPreview',
    },
  },

  {
    path: '/account-management',
    Component: AccountManagement,
  },
  {
    path: '/aboutus',
    Component: AboutUs,
  },
  {
    path: '/advanced-use',
    Component: AdvancedUse,
  },
  {
    path: '/dev-page',
    Component: DevPage,
    handle: {
      name: 'devPage',
      type: 'dev',
    },
  },
  {
    path: '/plugin-management',
    Component: PluginManagement,
  },
  {
    path: '/403',
    Component: NoPermission,
    handle: {
      parentPath: '/_common',
      name: '403',
      type: 'all',
    },
  },
  {
    path: '/404',
    element: <NotFoundPage />,
    handle: {
      parentPath: '/_common',
      name: '404',
      type: 'all',
    },
  },
];

const routes = [
  {
    path: '/',
    element: <RootRedirect />,
  },
  {
    path: '/',
    element: <Layout />,
    children: childrenRoutes,
  },
  {
    path: '/share',
    Component: Share,
  },
  // {
  //   path: '/403',
  //   Component: NoPermission,
  // },
  {
    path: '*',
    element: <NotPage />,
  },
];

export const useLocationNavigate = () => {
  const navigate = useNavigate();
  return (location: Partial<Location>) => {
    const { pathname, search, state } = location;
    navigate(pathname + (search ?? ''), { state });
  };
};

export default routes;
