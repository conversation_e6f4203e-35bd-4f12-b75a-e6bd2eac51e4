.controlPanel {
  margin-top: 20px;
  display: flex;
  gap: 16px;
  font-size: 12px;

  p {
    font-size: 12px;
  }

  .operationTitle {
    color: var(--supos-text-color);
    font-size: 14px;
    font-weight: 500;
    line-height: 1;
  }

  .operationBox {
    display: flex;
    flex-flow: column wrap;
    flex-shrink: 0;
    gap: 10px;
    width: 116px;

    .operationFlex {
      display: flex;
      flex-shrink: 0;
      gap: 4px;

      .operationName {
        display: flex;
        width: 36px;
        height: 36px;
        justify-content: center;
        align-items: center;
        border-radius: 3px;
        border: 1px solid #c6c6c6;
        background-color: var(--supos-user-color);
        cursor: pointer;
      }
    }
  }

  .functionBox {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .functionContent {
      display: flex;
      gap: 6px;
      height: 186px;
      overflow: hidden;
    }

    .functionSelect {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;

      .fieldList {
        flex: 1;
        overflow: auto;
        padding-right: 6px;

        li.hover {
          border-color: var(--supos-theme-color);
        }

        .fieldLi {
          list-style: none;
          margin: 0 0 5px;
          padding: 0 8px;
          height: 22px;
          border-radius: 3px;
          border: 1px solid #c6c6c6;
          background-color: var(--supos-user-color);
          cursor: pointer;
          display: flex;
          align-items: center;
          justify-content: space-between;

          .fieldType {
            color: #b9bdc3;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            width: 100px;
            text-align: right;
          }

          &:last-child {
            margin-bottom: 0;
          }
        }
      }
    }

    .functionDetail {
      flex: 1;
      height: 100%;
      border-radius: 3px;
      background-color: var(--supos-user-color);
      padding: 4px;
      overflow: auto;
      font-size: 12px;
    }
  }

  .formulaName {
    color: #cb5b2c;
  }

  .formulaField {
    color: #39bb35;
    background: #d3f0d3;
  }

  .highlight {
    color: #ff9633;
  }
}
