.upload-data {
  width: 100%;
  border-radius: 3px;
  border: 1px solid #c6c6c6;
  background: var(--supos-uns-button-color);
  display: flex;
  padding: 12px;
  gap: 10px;

  .upload-data-info {
    flex: 1;
    color: var(--supos-text-color);
    overflow: hidden;

    .info-title {
      font-size: 18px;
      font-weight: 500;
      letter-spacing: 0.16px;
      padding-bottom: 16px;
    }

    .info-description {
      font-size: 14px;
      font-weight: 400;
      letter-spacing: 0.16px;
    }
  }

  .upload-data-code {
    flex: 1;
    color: var(--supos-text-color);
    background: var(--supos-switchwrap-active-bg-color);
    border-radius: 3px;
    border: 1px solid #c9c9c9;
    overflow: hidden;
  }

  .title {
    font-size: 18px;
    font-weight: 500;
  }

  .fieldItem:not(:last-of-type) {
    margin-bottom: 15px;
  }

  .leftFormItems,
  .rightFormItems {
    height: 100%;
    border-radius: 3px;
  }

  .leftFormItems {
    padding: 16px;
    border: 1px solid #c9c9c9;
    background: var(--supos-switchwrap-active-bg-color);
    overflow: auto;
  }

  :global {
    .payload {
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding: 14px 0;
    }

    .ant-tabs-nav {
      background: var(--supos-switchwrap-active-bg-color);
    }

    .demo-table {
      th,
      td {
        padding: 5px 10px;
        border: 1px solid #c6c6c6;
      }
    }
  }
}
