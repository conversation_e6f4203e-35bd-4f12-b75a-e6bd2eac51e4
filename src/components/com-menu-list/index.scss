.menuItem {
  width: 317px;
  flex-shrink: 0;
  border-radius: 3px;
  background: var(--supos-uns-button-color);
  padding: 24px;

  .item-content {
    flex: 1;
    padding: 0 22px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    overflow: hidden;
    height: 100%;
  }

  .label {
    color: var(--supos-table-first-color);
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
  }

  .version {
    color: var(--supos-table-first-color);
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    line-height: 18px;
    letter-spacing: 0.16px;
    margin-bottom: 10px;
  }

  .description {
    color: var(--supos-table-first-color);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 18.2px;
    letter-spacing: 0.16px;
    flex: 1;
    position: relative;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    text-overflow: ellipsis;
  }
}
