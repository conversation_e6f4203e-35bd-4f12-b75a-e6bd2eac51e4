.com-layout {
  display: flex;
  height: 100%;
  position: relative;
}

.com-left {
  flex-shrink: 0;
  width: 300px;
  border-right: 1px solid #c6c6c6;
  display: flex;
  flex-direction: column;
  user-select: none;
  position: relative;

  :global {
    .title {
      border-bottom: 1px solid #c6c6c6;
      background: var(--supos-user-color);
      height: 48px;
      color: #161616;
      font-size: 16px;
      font-weight: 700;
      padding: 0 20px;
    }

    .col-resize-wrap {
      height: 100%;
      width: 7px;
      position: absolute;
      top: 0;
      right: -4px;
      z-index: 11;
      background-color: transparent;
      cursor: col-resize;
    }
  }
}

.resize-focus {
  border-right-color: var(--supos-theme-color);
}

.com-right {
  height: 100%;
  width: 350px;
  position: relative;
  border-left: 1px solid #c6c6c6;

  :global {
    .close {
      height: 70px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-right: 40px;
    }
  }
}

.com-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: 200ms;
  overflow: auto;

  :global {
    .title {
      overflow: auto;
      border-bottom: 1px solid #c6c6c6;
      height: 48px;
      padding: 0 20px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 8px;
      font-size: 18px;
      font-weight: 700;
      background-color: var(--supos-user-color);
    }

    .content {
      flex: 1;
      width: 100%;
      overflow: auto;
    }
  }
}

@media (width <= 640px) {
  .com-left {
    position: fixed;
    inset: 0;
    width: 100%;
    height: 100%;
    background-color: var(--supos-user-color);
    z-index: 999;
    overflow: auto;
    box-shadow: 0 4px 8px rgb(0 0 0 / 20%);
    border-radius: 8px;

    :global {
      .title-close {
        position: absolute;
        right: 12px;
        top: 12px;
        cursor: pointer;
      }
    }
  }

  .floating {
    display: none;
  }

  .floating-btn {
    position: fixed;
    bottom: 16px;
    left: 16px;
    cursor: pointer;
    z-index: 999;
    padding: 10px;
    background-color: var(--supos-copilot-color);
  }
}
