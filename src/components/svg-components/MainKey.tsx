const MainKey = () => {
  return (
    <svg width="16" height="17" viewBox="0 0 16 17" fill="none" xmlns="http://www.w3.org/2000/svg" className="MainKey">
      <g clipPath="url(#clip0_5494_44930)">
        <path
          d="M7.99487 7.65381C8.55043 7.65381 9.02265 7.45936 9.41154 7.07048C9.80043 6.68159 9.99487 6.20936 9.99487 5.65381C9.99487 5.09825 9.80043 4.62603 9.41154 4.23714C9.02265 3.84825 8.55043 3.65381 7.99487 3.65381C7.43932 3.65381 6.9671 3.84825 6.57821 4.23714C6.18932 4.62603 5.99487 5.09825 5.99487 5.65381C5.99487 6.20936 6.18932 6.68159 6.57821 7.07048C6.9671 7.45936 7.43932 7.65381 7.99487 7.65381ZM8.01154 16.0705C7.92265 16.0705 7.83932 16.0566 7.76154 16.0288C7.68376 16.001 7.61154 15.9594 7.54487 15.9038L5.82821 14.4038C5.76154 14.3483 5.70876 14.2844 5.66987 14.2121C5.63098 14.1399 5.60598 14.0594 5.59487 13.9705C5.58376 13.8816 5.5921 13.7955 5.61987 13.7121C5.64765 13.6288 5.68932 13.5538 5.74487 13.4871L6.66154 12.3205L5.79487 11.4538C5.72821 11.3871 5.68098 11.3149 5.65321 11.2371C5.62543 11.1594 5.61154 11.076 5.61154 10.9871C5.61154 10.8983 5.62543 10.8149 5.65321 10.7371C5.68098 10.6594 5.72821 10.5871 5.79487 10.5205L6.66154 9.65381V9.42048C5.86154 9.1427 5.2171 8.65936 4.72821 7.97048C4.23932 7.28159 3.99487 6.50936 3.99487 5.65381C3.99487 4.5427 4.38376 3.59825 5.16154 2.82048C5.93932 2.0427 6.88376 1.65381 7.99487 1.65381C9.10598 1.65381 10.0504 2.0427 10.8282 2.82048C11.606 3.59825 11.9949 4.5427 11.9949 5.65381C11.9949 6.55381 11.7393 7.33992 11.2282 8.01214C10.7171 8.68436 10.0838 9.15381 9.32821 9.42048V14.7205C9.32821 14.8094 9.31154 14.8955 9.27821 14.9788C9.24487 15.0621 9.19487 15.1371 9.12821 15.2038L8.44487 15.8871C8.38932 15.9427 8.32543 15.9871 8.25321 16.0205C8.18098 16.0538 8.10043 16.0705 8.01154 16.0705Z"
          fill="currentColor"
        />
      </g>
      <defs>
        <clipPath id="clip0_5494_44930">
          <rect width="16" height="16" fill="white" transform="translate(-0.00512695 0.987305)" />
        </clipPath>
      </defs>
    </svg>
  );
};
export default MainKey;
