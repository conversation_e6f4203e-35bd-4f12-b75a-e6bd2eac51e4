const TagAdd = () => {
  return (
    <svg width="17" height="17" viewBox="0 0 17 17" fill="none" xmlns="http://www.w3.org/2000/svg">
      <rect
        width="16"
        height="16"
        transform="matrix(-1 0 0 1 16.9062 0.616211)"
        fill="white"
        fillOpacity="0.01"
        // style="mix-blend-mode:multiply"
      />
      <path
        d="M6.31055 13.837C6.65745 13.8367 6.99003 13.6986 7.23517 13.4532L13.1318 7.5609C13.3773 7.31576 13.5153 6.98318 13.5156 6.63628V2.93343C13.5156 2.58641 13.3778 2.25361 13.1324 2.00823C12.887 1.76285 12.5542 1.625 12.2072 1.625H8.50435C8.15745 1.62531 7.82487 1.76336 7.57972 2.00881L1.68744 7.90109C1.56545 8.02265 1.46867 8.16709 1.40263 8.32613C1.33659 8.48518 1.3026 8.65569 1.3026 8.8279C1.3026 9.0001 1.33659 9.17062 1.40263 9.32966C1.46867 9.4887 1.56545 9.63314 1.68744 9.7547L5.38593 13.4532C5.63107 13.6986 5.96365 13.8367 6.31055 13.837ZM12.2072 2.49729C12.3229 2.49729 12.4338 2.54324 12.5156 2.62503C12.5974 2.70682 12.6433 2.81776 12.6433 2.93343V6.63628C12.6431 6.69398 12.6314 6.75107 12.609 6.80422C12.5865 6.85738 12.5537 6.90555 12.5125 6.94594L6.62021 12.8382C6.53849 12.9195 6.42795 12.9651 6.31273 12.9651C6.19751 12.9651 6.08697 12.9195 6.00525 12.8382L2.3024 9.13538C2.22116 9.05366 2.17557 8.94312 2.17557 8.8279C2.17557 8.71267 2.22116 8.60213 2.3024 8.52042L8.19468 2.62813C8.23508 2.58692 8.28325 2.55413 8.3364 2.53167C8.38956 2.50921 8.44664 2.49752 8.50435 2.49729H12.2072Z"
        fill="currentColor"
      />
      <path
        d="M10.0265 6.85871C10.3715 6.85871 10.7088 6.75639 10.9957 6.5647C11.2826 6.373 11.5062 6.10054 11.6383 5.78176C11.7703 5.46298 11.8048 5.11221 11.7375 4.77379C11.6702 4.43538 11.5041 4.12453 11.2601 3.88054C11.0161 3.63656 10.7052 3.47041 10.3668 3.40309C10.0284 3.33578 9.67764 3.37033 9.35887 3.50237C9.04009 3.63441 8.76762 3.85802 8.57593 4.14491C8.38423 4.4318 8.28191 4.7691 8.28191 5.11414C8.28191 5.57683 8.46572 6.02057 8.79289 6.34774C9.12006 6.67491 9.56379 6.85871 10.0265 6.85871ZM10.0265 4.24186C10.199 4.24186 10.3677 4.29301 10.5111 4.38886C10.6545 4.48471 10.7663 4.62094 10.8324 4.78033C10.8984 4.93972 10.9157 5.11511 10.882 5.28432C10.8484 5.45352 10.7653 5.60895 10.6433 5.73094C10.5213 5.85293 10.3659 5.93601 10.1967 5.96967C10.0275 6.00332 9.85206 5.98605 9.69267 5.92003C9.53329 5.85401 9.39705 5.7422 9.3012 5.59876C9.20536 5.45531 9.1542 5.28666 9.1542 5.11414C9.1542 4.8828 9.2461 4.66093 9.40968 4.49734C9.57327 4.33376 9.79514 4.24186 10.0265 4.24186Z"
        fill="currentColor"
      />
      <path
        d="M13.9434 12.1846V10.4717H13.0869V12.1846H11.374V13.041H13.0869V14.7539H13.9434V13.041H15.6562V12.1846H13.9434Z"
        fill="currentColor"
      />
    </svg>
  );
};
export default TagAdd;
