const FileEdit = () => {
  return (
    <svg width="1em" height="1em" viewBox="0 0 17 16" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path
        d="M2.69141 9.00016V2.66683C2.69141 2.31321 2.83188 1.97407 3.08193 1.72402C3.33198 1.47397 3.67112 1.3335 4.02474 1.3335H9.69141L13.3581 5.00016V13.3335C13.3581 13.6871 13.2176 14.0263 12.9675 14.2763C12.7175 14.5264 12.3784 14.6668 12.0247 14.6668H8.35807"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path d="M9.35938 1.3335V5.3335H13.3594" stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" />
      <path
        d="M6.97141 8.40665C7.10141 8.27664 7.25576 8.17352 7.42562 8.10316C7.59549 8.0328 7.77755 7.99658 7.96141 7.99658C8.14527 7.99658 8.32733 8.0328 8.49719 8.10316C8.66705 8.17352 8.8214 8.27664 8.95141 8.40665C9.08141 8.53666 9.18454 8.691 9.2549 8.86087C9.32526 9.03073 9.36148 9.21279 9.36148 9.39665C9.36148 9.58051 9.32526 9.76257 9.2549 9.93244C9.18454 10.1023 9.08141 10.2566 8.95141 10.3867L5.32474 14L2.69141 14.6667L3.35141 12.0333L6.97141 8.40665Z"
        stroke="currentColor"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export default FileEdit;
