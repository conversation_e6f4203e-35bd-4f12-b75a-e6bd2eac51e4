.custom-search {
  position: relative;
  display: flex;
  width: 100%;

  .custom-search-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
  }

  .custom-search-input {
    width: 100%;
    height: 100%;
    padding: 0 32px;
    border: none;
    border-bottom: 1px solid #c6c6c6;
    background: var(--supos-fill-tertiary);
    color: var(--supos-text-color);

    &:focus {
      outline: 2px solid var(--supos-theme-color);
      outline-offset: -2px;
    }
  }

  .custom-search-clear {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    cursor: pointer;
    border: none;
    background-color: transparent;
    color: var(--supos-text-color);
    transition:
      opacity 0.11s cubic-bezier(0.2, 0, 0.38, 0.9),
      background-color 0.11s cubic-bezier(0.2, 0, 0.38, 0.9),
      outline 0.11s cubic-bezier(0.2, 0, 0.38, 0.9),
      border 0.11s cubic-bezier(0.2, 0, 0.38, 0.9);

    &:hover {
      background: var(--supos-fill-secondary);
    }

    &:focus {
      outline: 2px solid var(--supos-theme-color);
      outline-offset: -2px;
    }
  }
}

.custom-search-sm {
  height: 32px;

  .custom-search-icon {
    left: 8px;
  }

  .custom-search-input {
    padding: 0 32px;
  }

  .custom-search-clear {
    width: 32px;
  }
}

.custom-search-md {
  height: 40px;

  .custom-search-icon {
    left: 12px;
  }

  .custom-search-input {
    padding: 0 40px;
  }

  .custom-search-clear {
    width: 40px;
  }
}

.custom-search-lg {
  height: 48px;

  .custom-search-icon {
    left: 16px;
  }

  .custom-search-input {
    padding: 0 48px;
  }

  .custom-search-clear {
    width: 48px;
  }
}
