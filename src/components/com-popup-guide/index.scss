.com-popup-guide {
  .ant-progress-bg-outer.ant-progress-bg {
    background-color: #0f62fe;
  }

  .ant-tooltip-inner {
    min-width: 200px;
    max-width: 300px;
    min-height: 100px;
    background-color: #fff;
    border-radius: 4px;
    border: 2px solid #0f62fe;
    display: flex;

    .title {
      flex: 1;
      padding: 8px;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      justify-content: space-between;
      color: #000;
    }
  }
}

.com-popup-guide-wrapper {
  //outline: 2px solid deeppink;
  position: relative;

  &::after {
    content: ' ';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgb(255 165 0 / 50%);
    z-index: 9998;
  }
}
