.pro-table,
.resizable-table {
  .ant-table-thead {
    .ant-table-cell {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      .react-resizable-line {
        background-color: transparent;
        position: absolute;
        inset-inline-end: -3px;
        top: 0;
        z-index: 2;
        width: 6px;
        height: 100%;
        cursor: col-resize;
        transition:
          background-color 0.2s,
          width 0.2s;

        &:hover {
          background-color: var(--supos-theme-color);
        }
      }
    }
  }

  .ant-table-tbody {
    > tr > td {
      height: 40px;
      padding: 8px 16px;
    }
  }

  .ant-table {
    scrollbar-color: auto; /* 重置默认颜色控制 */
  }

  /* 针对Antd5的Table组件 */
  .ant-table-content .ant-table-body::-webkit-scrollbar {
    width: 8px;
    height: 8px;
    background: transparent;
  }

  .ant-table-content .ant-table-body::-webkit-scrollbar-thumb {
    border-radius: 8px;
    background: #d3d3d3;
  }

  .ant-table-content .ant-table-body::-webkit-scrollbar-track {
    margin: 4px 0;
    border-radius: 8px;
  }

  table {
    width: 100% !important;
  }

  .ant-pagination-item,
  .ant-pagination-options-quick-jumper,
  .ant-pagination-options-quick-jumper input {
    background-color: var(--supos-header-bg-color) !important;
  }

  .ant-pagination-item-active,
  .ant-pagination-options-quick-jumper input:focus-within {
    border-color: var(--supos-theme-color) !important;
  }

  .ant-pagination-item-active a {
    color: var(--supos-theme-color) !important;
  }
}

.ant-table-cell.ant-table-cell-scrollbar .react-resizable-line {
  display: none !important;
}

.hidden-empty {
  .ant-table-placeholder {
    display: none;
  }
}

.fixed-pagination-bottom {
  &,
  .ant-spin-nested-loading {
    height: 100%;

    .ant-table {
      height: calc(100% - 76px);
    }
  }
}
