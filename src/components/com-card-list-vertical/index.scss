.com-card-list-vertical {
  overflow: auto;
  display: flex;
  flex-direction: column;
  place-content: flex-start flex-start;
  align-items: flex-start;
  gap: 20px;
  padding: 30px;

  .com-card {
    width: 100%;
    height: 146px;
    padding: 20px 30px;
    border-radius: 3px;
    border: 1px solid #e2e2e2;
    background-color: var(--supos-uns-button-color);
  }

  .com-item-card {
    display: flex;
    justify-content: space-between;
    gap: 160px;

    .com-card-left {
      flex: 1;
      display: flex;
      align-items: center;
      gap: 30px;
      min-width: 0;

      .com-card-info {
        flex: 1;
        min-width: 0;

        .com-card-header {
          width: 100%;
          display: flex;
          align-items: center;
          gap: 10px;
          overflow: hidden;

          .name {
            flex: 1;
            color: var(--supos-text-color);
            font-size: 20px;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

          .edit-icon {
            height: 20px;
            color: var(--supos-text-color);
            background: var(--supos-bg-color);
            border: 1px solid #c6c6c6;
            padding: 1px;
            border-radius: 3px;
            cursor: pointer;
          }
        }

        .com-card-content {
          flex: 1;
        }
      }
    }

    .com-card-right {
      display: flex;
      align-items: center;
    }

    .com-card-footer {
      height: 37px;
      width: 100%;
      display: flex;
      border-top: 1px solid var(--crl-border-color);
    }

    &:hover {
      background-color: var(--supos-card-hover-color);
      color: var(--supos-theme-color);
    }
  }
}

.view-list-flex-start {
  margin-top: 10px;
  display: flex;
  gap: 30px;

  .view-list-item {
    min-width: 0;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    color: var(--supos-text-color);
    gap: 10px;
    line-height: 18px;

    &.item-right {
      flex: 1;
    }

    .text {
      color: var(--supos-view-item);
      overflow: hidden;
      text-overflow: ellipsis;
      word-break: break-all;
      flex: 1;
      white-space: nowrap;
      max-width: 100%;
    }
  }
}

.right-operation {
  display: flex;
  width: 100%;
  gap: 10px;

  .button-item {
    min-width: 66px;
    padding: 0 6px;
    gap: 3px;
  }
}
