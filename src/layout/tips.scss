.tips-guide-classes.shepherd-element {
  max-width: 550px;

  .shepherd-title {
    justify-content: center;
    padding: 10px 0 6px;
  }

  .shepherd-cancel-icon {
    position: absolute;
    top: 16px;
  }

  .tips-info {
    text-align: center;
    font-size: 14px;
    margin: 0 0 10px;
  }

  .tips-card {
    width: 100%;
    height: 100px;
    // background: url('../assets/guide/card-bg.jpg') no-repeat center center;
    // background-size: cover;
    background-color: #f4f4f4;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 25px;
    gap: 20px;
    border-radius: 3px;

    .tips-card-icon-wrap {
      width: 50px;
      height: 50px;
      border-radius: 3px;
      display: flex;
      justify-content: center;
      align-items: center;
      // background-color: #fff;

      .tips-card-icon {
        width: 48px;
        height: 48px;
        background: url('../assets/guide/card_icon_1.svg') no-repeat center center;
        background-size: cover;
      }

      .card-icon-1 {
        background-image: url('../assets/guide/card_icon_1.svg');
      }

      .card-icon-2 {
        background-image: url('../assets/guide/card_icon_2.svg');
      }

      .card-icon-3 {
        background-image: url('../assets/guide/card_icon_3.svg');
      }

      .card-icon-4 {
        background-image: url('../assets/guide/card_icon_4.svg');
      }

      .card-icon-5 {
        background-image: url('../assets/guide/card_icon_5.svg');
      }

      .card-icon-6 {
        background-image: url('../assets/guide/card_icon_6.svg');
      }
    }

    .tips-card-content {
      flex: 1;
    }
  }
}
