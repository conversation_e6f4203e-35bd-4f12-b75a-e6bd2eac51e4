.app-space-list {
  margin-top: 4px;
  height: calc(100% - 44px);
  overflow: auto;

  :global {
    .common-card {
      display: flex;
      height: 32px;
      padding: 7px 16px 7px 40px;
      align-items: center;
      align-self: stretch;
      overflow: hidden;
      width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      color: #2f343b;
      color: var(--supos-common-card-color);
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      letter-spacing: 0.16px;
      justify-content: space-between;
    }

    .can-select-card {
      cursor: pointer;
      margin-top: 2px;

      &:hover {
        background: var(--supos-hover-card-color);
      }
    }

    .select-card {
      cursor: pointer;
      margin-top: 2px;
      background: var(--supos-select-card-color);
    }
  }
}
