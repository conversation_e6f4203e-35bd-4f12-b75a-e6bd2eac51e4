.unsTopologyTitle {
  font-size: 22px;
  line-height: 60px;
  padding-left: 20px;
  font-weight: bold;
}

.unsTopologyWrap {
  display: flex;
  width: 100%;
  padding: 0 20px 20px;
  overflow: auto;

  .unsTopologyContent {
    flex: 1;
    min-height: 400px;

    .common-node {
      width: 100%;
      height: 100%;
      background-color: var(--supos-bg-color);
      border-radius: 3px;
      border-left: 4px solid var(--supos-mode-color, var(--supos-theme-color));
      color: var(--supos-text-color);
      display: flex;
      align-items: center;
      padding-left: 10px;
      gap: 10px;

      .node-count {
        display: flex;
        align-items: center;

        span {
          background-color: var(--supos-status-color);
          height: 5px;
          width: 5px;
          border-radius: 50%;
          display: inline-block;
          margin-right: 4px;
          margin-left: 10px;
        }
      }
    }

    .common-node-hover {
      &:hover {
        background-color: var(--supos-active-bg-color);
        cursor: pointer;
      }
    }
  }
}
