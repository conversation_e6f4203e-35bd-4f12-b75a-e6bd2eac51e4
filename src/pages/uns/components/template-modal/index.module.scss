.template-modal {
  :global {
    .ant-drawer-header {
      border: none;
      padding-left: 40px;
    }

    .ant-drawer-body {
      padding: 40px;
    }

    .ant-form-item-no-colon {
      font-weight: 700;
    }
  }
}

.tree-select {
  :global {
    .ant-select-selection-overflow .ant-select-selection-overflow-item .ant-select-selection-item {
      background-color: var(--supos-tag-color);
    }
  }
}

.tree-select-popup {
  :global {
    .ant-select-tree-treenode {
      margin-bottom: 0 !important;
    }
  }
}

.key-title {
  margin-bottom: 10px;
  font-weight: 700;
}
