.treeWrap {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;

  .noSwitcherNoopTree {
    .ant-tree-treenode {
      .ant-tree-switcher {
        display: none;
      }
    }
  }

  .treeOperateIconWrap {
    height: 32px;
    width: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;

    span {
      height: 16px;
      line-height: 16px;
    }

    svg {
      cursor: pointer;
    }
  }

  .unsTreeRightMenuMask {
    .unsTreeRightMenuWrap {
      width: 160px;
      position: absolute;
      border-radius: 2px;
      box-shadow: 0 2px 6px 0 rgb(0 0 0 / 30%);
      background: var(--supos-charttop-bg-color);

      & > div {
        line-height: 32px;
        padding: 0 16px;
        cursor: pointer;

        &:hover {
          background: var(--supos-layer-selecter-hover);
        }
      }

      .rightKeyDelete {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        border-top: 1px solid #c6c6c6;
      }
    }
  }
}
