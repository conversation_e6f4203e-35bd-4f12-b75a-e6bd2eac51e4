.keyIndexBtn {
  .<PERSON><PERSON><PERSON> {
    color: #8d8d8d;
  }

  &:hover {
    .<PERSON><PERSON><PERSON> {
      color: var(--supos-theme-color);
    }
  }

  &:disabled {
    .<PERSON><PERSON><PERSON> {
      color: #8d8d8d;
    }
  }
}

.activeKeyIndexBtn {
  background: var(--supos-select-active-color) !important;

  .<PERSON><PERSON><PERSON> {
    color: var(--supos-theme-color);
  }

  &:hover {
    .<PERSON><PERSON><PERSON> {
      color: #8d8d8d;
    }
  }
}
