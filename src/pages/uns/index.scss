.unsContainer {
  .ant-table-cell {
    line-height: 24px;
    padding: 8px 16px !important;
  }

  .treemapTitle {
    height: 48px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 16px;
    font-weight: bold;
    // background: #f4f4f4;
    cursor: pointer;
    border-bottom: 1px solid var(--supos-table-tr-color);
    background-color: var(--supos-charttop-bg-color);
  }

  .chartWrap {
    transition: 200ms;
    height: 100%;
    display: flex;
    flex-direction: column;

    .chartTop {
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      border-bottom: 1px solid var(--supos-table-tr-color);
      // background: #f4f4f4;
      background: var(--supos-charttop-bg-color);
      flex-shrink: 0;

      .chartTopL {
        display: flex;
        flex: 1;
        align-items: center;

        .copyBox {
          cursor: pointer;
          display: flex;
          align-items: center;
          padding-right: 8px;
        }
      }

      .chartTopR {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }

    .ant-table-tbody > tr > td {
      overflow-wrap: anywhere;
    }

    .topicDetailWrap {
      flex: 1;
      // height: calc(100% - 48px);
      overflow: hidden;
      position: relative;

      .graphiql-container {
        background-color: var(--supos-charttop-bg-color);
        border: 1px solid var(--supos-table-tr-color);
        border-radius: 3px;
        padding: 8px;

        .graphiql-sidebar {
          border: 1px solid var(--supos-table-tr-color);
          border-radius: 3px;
          background-color: hsl(var(--color-base));
          margin-right: 8px;
          display: none;
          width: 0;
          height: 0;
        }

        .graphiql-plugin {
          border: 1px solid var(--supos-table-tr-color);
          border-radius: 3px;
          background-color: var(--supos-bg-color);

          .docExplorerWrap {
            input {
              padding: 0 !important;
            }

            option {
              font-size: 1em !important;
            }
          }
        }

        .graphiql-horizontal-drag-bar {
          width: 8px;
        }

        .graphiql-sessions {
          background-color: transparent;
          border-radius: 0;
          margin: 0;

          .graphiql-session-header {
            display: none;
            width: 0;
            height: 0;

            .graphiql-tab {
              border-radius: 3px;

              .graphiql-tab-button {
                border-radius: 3px;
              }

              :is(.graphiql-un-styled, button.graphiql-un-styled):focus {
                outline: none;
              }
            }

            .graphiql-tab.graphiql-tab-active {
              background-color: #e0e0e0;
            }
          }

          .graphiql-session {
            padding: 0;
          }

          .graphiql-editors {
            border: 1px solid var(--supos-table-tr-color);
            border-radius: 3px;
            box-shadow: none;
            margin: 0;
            background-color: var(--supos-bg-color);

            .graphiql-editor-tools {
              display: none;
              width: 0;
              height: 0;
            }

            .graphiql-query-editor {
              padding: 0;
              gap: 0;

              .graphiql-editor {
                --editor-background: var(--supos-bg-color);
                // padding: 8px;
                .CodeMirror {
                  .CodeMirror-scroll {
                    padding-top: 8px;
                  }
                }
              }

              .graphiql-toolbar {
                // background-color: #e0e0e0;
                padding: 8px;
                border-left: 1px solid var(--supos-table-tr-color);
                border-radius: 3px;
                width: auto;

                .graphiql-execute-button {
                  background-color: var(--supos-theme-color);
                  border-radius: 3px;
                }

                button.graphiql-execute-button:focus {
                  outline: var(--supos-theme-color);
                }
              }
            }
          }

          .graphiql-response {
            border: 1px solid var(--supos-table-tr-color);
            border-radius: 3px;
            background-color: var(--supos-bg-color);
          }
        }
      }

      .topicDetailContent {
        padding: 20px 5px;
        height: 100%;
        overflow-y: auto;

        .detailTitle {
          min-height: 50px;
          line-height: 50px;
          font-size: 30px;
          font-weight: bold;
          padding-left: 16px;
        }

        .tableWrap {
          margin-top: 20px;

          .ant-collapse {
            background-color: transparent !important;

            .ant-collapse-header {
              align-items: center;
              cursor: inherit;

              .ant-collapse-expand-icon {
                padding-inline-end: 3px;
                cursor: pointer;
              }

              .ant-collapse-header-text {
                font-size: 20px;
                font-weight: bold;
                flex: none;
                cursor: pointer;
              }
            }
          }

          .tableTitle {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            margin-top: 40px;
          }

          .detailItem {
            display: flex;
            min-height: 40px;
            align-items: center;
            border-bottom: 1px solid var(--supos-table-tr-color);
            width: 100%;
            line-height: 30px;

            .detailKey {
              width: 30%;
              font-weight: bold;
              flex-shrink: 0;
              word-break: break-word;
            }

            div:last-child {
              padding: 5px 0;
              width: 70%;
              word-break: break-word;
            }
          }

          .switchWrap {
            width: max-content;
            display: flex;
            align-items: center;
            // background: #f4f4f4;
            background: var(--supos-switchwrap-bg-color);
            border-radius: 4px;
            padding: 6px;
            gap: 6px;
            position: relative;
            font-weight: bold;
            color: #585c62;

            .selectedBg {
              position: absolute;
            }

            div {
              width: 100px;
              height: 38px;
              text-align: center;
              line-height: 38px;
              cursor: pointer;
              border-radius: 4px;
            }

            .active {
              background: #fff;
              background: var(--supos-switchwrap-active-bg-color);
              // color: #050b14;
              color: var(--supos-switchwrap-active-text-color);
            }

            margin-bottom: 20px;
          }

          .payload-table .ant-table-tbody td:nth-child(1) {
            background-color: var(--supos-payloadfirsttd-color);
          }

          .no-border-td {
            border: none !important;
            background-color: transparent !important;
          }

          .no-border-td-button {
            border: none !important;
            background: none !important;
            box-shadow: none !important;
          }

          .button-add {
            cursor: pointer;
            margin-top: 20px;
            width: 94%;
            border: 1px solid var(--supos-table-tr-color);
            border-radius: 2px;
            height: 30px;
          }

          .customTable {
            width: 100%;

            // td {
            //   min-height: 40px;
            //   line-height: 24px;
            //   padding: 8px 20px;
            //   word-break: break-all;
            // }

            // thead > tr > td {
            //   // background-color: #e8e8e8;
            //   background-color: var(--supos-table-head-color);
            //   color: var(--supos-text-color);
            //   font-weight: bold;
            // }

            // tbody > tr > td {
            //   color: var(--supos-theme-color);

            //   &:first-child {
            //     color: var(--supos-table-first-color);
            //     font-weight: bold;
            //   }
            // }

            .payloadFirstTd {
              background-color: var(--supos-payloadfirsttd-color);
            }
          }
        }

        .codeViewWrap {
          max-inline-size: none;
          z-index: 1;
          font-size: 12px !important;

          .code-snippet-container {
            width: 100%;
          }
        }

        .deleteBtnWrap {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          margin-top: 20px;
          padding-right: 17px;
        }
      }
    }

    .unsDashboardWrap {
      flex: 1;
      // height: calc(100% - 48px);
      display: flex;
      flex-direction: column;
      overflow: auto;

      .unsDashboardTop {
        padding: 20px;
        border-bottom: 1px solid var(--supos-table-tr-color);

        .overviewTitle {
          font-size: 30px;
          font-weight: bold;
        }

        .overviewWrap {
          display: flex;
          gap: 20px;

          .overviewItem {
            // flex: 1;
            width: 25%;
            // background-color: #f4f4f4;
            background-color: var(--supos-switchwrap-bg-color);
            padding: 30px;

            .overviewItemTop {
              display: flex;
              align-items: center;
              justify-content: space-between;
              margin-bottom: 40px;

              .overviewLabel {
                font-weight: bold;
                font-size: 16px;
              }
            }

            .overviewValue {
              font-size: 50px;
              color: var(--supos-theme-color);
              font-weight: 300;
            }
          }
        }
      }

      .unsDashboardBottom {
        flex: 1;
      }
    }
  }

  .unusedTopicTree-collapsible {
    height: 48px;
    padding: 0 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: var(--supos-bg-color);
    border-bottom: 1px solid var(--supos-home-border-color);
    cursor: pointer;

    &:hover {
      background: var(--supos-menu-hover-color);
    }
  }

  .unusedTopicTree-Splitter {
    &.ant-splitter > .ant-splitter-bar .ant-splitter-bar-dragger {
      &:hover {
        &::before {
          background-color: var(--supos-theme-color);
        }
      }

      &::before {
        background-color: var(--supos-home-border-color);
        height: 1px;
        transform: none;
        left: 0;
      }

      &::after {
        display: none;
      }
    }
  }

  .unsRealTimeWrap {
    flex: 1;
    min-height: 0;
    display: flex;
    flex-direction: column;

    .realTimeData {
      overflow: auto;
      display: flex;
      flex-direction: column;
      gap: 28px;
      padding: 0 20px 20px;
    }

    .realTimeDataPanel {
      background: var(--supos-tag-color);
      border: 1px solid var(--supos-home-border-color);
      border-radius: 3px;
      line-height: 16px;
      border-left: 4px solid var(--supos-theme-color);
      max-height: 400px;
      position: relative;

      .realTimeList-copy-overlap {
        position: absolute;
        right: 8px;
        top: 8px;
      }

      .realTimeList-copy {
        padding: 8px;
        cursor: pointer;
        background-color: var(--supos-tag-color);
        display: flex;

        &:hover {
          background-color: var(--supos-table-head-color);
        }
      }
    }
  }
}

@media (width <= 640px) {
  .overviewWrap {
    flex-wrap: wrap;
  }

  .overviewItem {
    width: 100% !important;
  }
}

@media (640px < width <= 1500px) {
  .overviewWrap {
    flex-wrap: wrap;
  }

  .overviewItem {
    width: calc(50% - 10px) !important;
  }
}

.butterfly-tooltip-container {
  z-index: 1001;
}

// .ant-input-outlined[disabled] {
//   color: var(--supos-text-color) !important;
// }

.ant-form-item-no-colon {
  color: var(--supos-text-color) !important;
}

.ant-tree-list-scrollbar-thumb {
  background-color: #d3d3d3 !important;

  &:hover {
    background: #a5a5a5 !important;
  }
}

.ant-picker-dropdown {
  .ant-picker-cell-in-view.ant-picker-cell-selected:not(.ant-picker-cell-disabled) .ant-picker-cell-inner {
    background: var(--supos-theme-color);
  }

  .ant-picker-cell-in-view.ant-picker-cell-today .ant-picker-cell-inner::before {
    border: 1px solid var(--supos-theme-color);
  }

  .ant-picker-now-btn {
    color: var(--supos-theme-color);
  }
}

@keyframes status-dot-breath {
  0% {
    opacity: 0.8;
    transform: scale(0.95);
  }

  50% {
    opacity: 1;
    transform: scale(1.1);
  }

  100% {
    opacity: 0.8;
    transform: scale(0.95);
  }
}

.treemap-drawer-root {
  padding: 0 !important;

  .ant-drawer-header {
    display: none;
  }

  .ant-drawer-mask {
    background-color: transparent;
  }

  .ant-drawer-body {
    padding: 0 !important;
  }
}

.ant-form-item-explain-error {
  color: #ff4d4f;
  margin-top: 5px;
}
