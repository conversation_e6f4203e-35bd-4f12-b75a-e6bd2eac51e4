// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.1
// Source: uns.proto

package namespace

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CompareInt64                     = uns.CompareInt64
	CompareString                    = uns.CompareString
	ConnectNodeInfo                  = uns.ConnectNodeInfo
	ConnectNodeInfoGetOneReq         = uns.ConnectNodeInfoGetOneReq
	ConnectNodeInfoListReq           = uns.ConnectNodeInfoListReq
	ConnectNodeInfoListResp          = uns.ConnectNodeInfoListResp
	DashboardItem                    = uns.DashboardItem
	DashboardReq                     = uns.DashboardReq
	DashboardResp                    = uns.DashboardResp
	DateRange                        = uns.DateRange
	Empty                            = uns.Empty
	NamespaceJson2FsFields           = uns.NamespaceJson2FsFields
	NamespaceJson2FsItems            = uns.NamespaceJson2FsItems
	NamespaceJson2FsReq              = uns.NamespaceJson2FsReq
	NamespaceJson2FsResp             = uns.NamespaceJson2FsResp
	NamespaceLabelInfo               = uns.NamespaceLabelInfo
	NamespaceLabelInfoGetListReq     = uns.NamespaceLabelInfoGetListReq
	NamespaceLabelInfoGetListResp    = uns.NamespaceLabelInfoGetListResp
	NamespaceLabelNodeID             = uns.NamespaceLabelNodeID
	NamespaceNodeAttachment          = uns.NamespaceNodeAttachment
	NamespaceNodeInfo                = uns.NamespaceNodeInfo
	NamespaceNodeInfoDefinition      = uns.NamespaceNodeInfoDefinition
	NamespaceNodeInfoGetFileListReq  = uns.NamespaceNodeInfoGetFileListReq
	NamespaceNodeInfoGetFileListResp = uns.NamespaceNodeInfoGetFileListResp
	NamespaceNodeInfoGetListReq      = uns.NamespaceNodeInfoGetListReq
	NamespaceNodeInfoGetListResp     = uns.NamespaceNodeInfoGetListResp
	NamespaceNodeInfoGetOneReq       = uns.NamespaceNodeInfoGetOneReq
	NamespaceTemplateInfo            = uns.NamespaceTemplateInfo
	NamespaceTemplateInfoGetListReq  = uns.NamespaceTemplateInfoGetListReq
	NamespaceTemplateInfoGetListResp = uns.NamespaceTemplateInfoGetListResp
	PageInfo                         = uns.PageInfo
	PageInfo_OrderBy                 = uns.PageInfo_OrderBy
	Request                          = uns.Request
	Response                         = uns.Response
	TimeRange                        = uns.TimeRange
	WithAppCodeID                    = uns.WithAppCodeID
	WithCode                         = uns.WithCode
	WithID                           = uns.WithID
	WithIDCode                       = uns.WithIDCode

	Namespace interface {
		NodeInfoCreate(ctx context.Context, in *NamespaceNodeInfo, opts ...grpc.CallOption) (*WithID, error)
		NodeInfoUpdate(ctx context.Context, in *NamespaceNodeInfo, opts ...grpc.CallOption) (*Empty, error)
		NodeInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
		NodeInfoGetOne(ctx context.Context, in *NamespaceNodeInfoGetOneReq, opts ...grpc.CallOption) (*NamespaceNodeInfo, error)
		NodeInfoGetList(ctx context.Context, in *NamespaceNodeInfoGetListReq, opts ...grpc.CallOption) (*NamespaceNodeInfoGetListResp, error)
		// 获取指定命名空间下的文件列表
		NodeInfoGetFileList(ctx context.Context, in *NamespaceNodeInfoGetFileListReq, opts ...grpc.CallOption) (*NamespaceNodeInfoGetFileListResp, error)
		// 回收站撤销
		NodeInfoRecycleUndo(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
		// 回收站删除
		NodeInfoRecycleDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
		TemplateInfoCreate(ctx context.Context, in *NamespaceTemplateInfo, opts ...grpc.CallOption) (*WithID, error)
		TemplateInfoUpdate(ctx context.Context, in *NamespaceTemplateInfo, opts ...grpc.CallOption) (*Empty, error)
		TemplateInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
		TemplateInfoGetOne(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*NamespaceTemplateInfo, error)
		TemplateInfoGetList(ctx context.Context, in *NamespaceTemplateInfoGetListReq, opts ...grpc.CallOption) (*NamespaceTemplateInfoGetListResp, error)
		LabelInfoCreate(ctx context.Context, in *NamespaceLabelInfo, opts ...grpc.CallOption) (*Empty, error)
		LabelInfoUpdate(ctx context.Context, in *NamespaceLabelInfo, opts ...grpc.CallOption) (*Empty, error)
		LabelInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
		LabelInfoGetOne(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*NamespaceLabelInfo, error)
		LabelInfoGetList(ctx context.Context, in *NamespaceLabelInfoGetListReq, opts ...grpc.CallOption) (*NamespaceLabelInfoGetListResp, error)
		LabelNodeIDCreate(ctx context.Context, in *NamespaceLabelNodeID, opts ...grpc.CallOption) (*Empty, error)
		// 已弃用,用文件列表传labelid方式获取
		LabelNodeIDDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
		// json转definition
		Json2Fs(ctx context.Context, in *NamespaceJson2FsReq, opts ...grpc.CallOption) (*NamespaceJson2FsResp, error)
		// dashboard
		Dashboard(ctx context.Context, in *DashboardReq, opts ...grpc.CallOption) (*DashboardResp, error)
	}

	defaultNamespace struct {
		cli zrpc.Client
	}

	directNamespace struct {
		svcCtx *svc.ServiceContext
		svr    uns.NamespaceServer
	}
)

func NewNamespace(cli zrpc.Client) Namespace {
	return &defaultNamespace{
		cli: cli,
	}
}

func NewDirectNamespace(svcCtx *svc.ServiceContext, svr uns.NamespaceServer) Namespace {
	return &directNamespace{
		svr:    svr,
		svcCtx: svcCtx,
	}
}

func (m *defaultNamespace) NodeInfoCreate(ctx context.Context, in *NamespaceNodeInfo, opts ...grpc.CallOption) (*WithID, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.NodeInfoCreate(ctx, in, opts...)
}

func (d *directNamespace) NodeInfoCreate(ctx context.Context, in *NamespaceNodeInfo, opts ...grpc.CallOption) (*WithID, error) {
	return d.svr.NodeInfoCreate(ctx, in)
}

func (m *defaultNamespace) NodeInfoUpdate(ctx context.Context, in *NamespaceNodeInfo, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.NodeInfoUpdate(ctx, in, opts...)
}

func (d *directNamespace) NodeInfoUpdate(ctx context.Context, in *NamespaceNodeInfo, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.NodeInfoUpdate(ctx, in)
}

func (m *defaultNamespace) NodeInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.NodeInfoDelete(ctx, in, opts...)
}

func (d *directNamespace) NodeInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.NodeInfoDelete(ctx, in)
}

func (m *defaultNamespace) NodeInfoGetOne(ctx context.Context, in *NamespaceNodeInfoGetOneReq, opts ...grpc.CallOption) (*NamespaceNodeInfo, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.NodeInfoGetOne(ctx, in, opts...)
}

func (d *directNamespace) NodeInfoGetOne(ctx context.Context, in *NamespaceNodeInfoGetOneReq, opts ...grpc.CallOption) (*NamespaceNodeInfo, error) {
	return d.svr.NodeInfoGetOne(ctx, in)
}

func (m *defaultNamespace) NodeInfoGetList(ctx context.Context, in *NamespaceNodeInfoGetListReq, opts ...grpc.CallOption) (*NamespaceNodeInfoGetListResp, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.NodeInfoGetList(ctx, in, opts...)
}

func (d *directNamespace) NodeInfoGetList(ctx context.Context, in *NamespaceNodeInfoGetListReq, opts ...grpc.CallOption) (*NamespaceNodeInfoGetListResp, error) {
	return d.svr.NodeInfoGetList(ctx, in)
}

// 获取指定命名空间下的文件列表
func (m *defaultNamespace) NodeInfoGetFileList(ctx context.Context, in *NamespaceNodeInfoGetFileListReq, opts ...grpc.CallOption) (*NamespaceNodeInfoGetFileListResp, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.NodeInfoGetFileList(ctx, in, opts...)
}

// 获取指定命名空间下的文件列表
func (d *directNamespace) NodeInfoGetFileList(ctx context.Context, in *NamespaceNodeInfoGetFileListReq, opts ...grpc.CallOption) (*NamespaceNodeInfoGetFileListResp, error) {
	return d.svr.NodeInfoGetFileList(ctx, in)
}

// 回收站撤销
func (m *defaultNamespace) NodeInfoRecycleUndo(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.NodeInfoRecycleUndo(ctx, in, opts...)
}

// 回收站撤销
func (d *directNamespace) NodeInfoRecycleUndo(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.NodeInfoRecycleUndo(ctx, in)
}

// 回收站删除
func (m *defaultNamespace) NodeInfoRecycleDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.NodeInfoRecycleDelete(ctx, in, opts...)
}

// 回收站删除
func (d *directNamespace) NodeInfoRecycleDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.NodeInfoRecycleDelete(ctx, in)
}

func (m *defaultNamespace) TemplateInfoCreate(ctx context.Context, in *NamespaceTemplateInfo, opts ...grpc.CallOption) (*WithID, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.TemplateInfoCreate(ctx, in, opts...)
}

func (d *directNamespace) TemplateInfoCreate(ctx context.Context, in *NamespaceTemplateInfo, opts ...grpc.CallOption) (*WithID, error) {
	return d.svr.TemplateInfoCreate(ctx, in)
}

func (m *defaultNamespace) TemplateInfoUpdate(ctx context.Context, in *NamespaceTemplateInfo, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.TemplateInfoUpdate(ctx, in, opts...)
}

func (d *directNamespace) TemplateInfoUpdate(ctx context.Context, in *NamespaceTemplateInfo, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.TemplateInfoUpdate(ctx, in)
}

func (m *defaultNamespace) TemplateInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.TemplateInfoDelete(ctx, in, opts...)
}

func (d *directNamespace) TemplateInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.TemplateInfoDelete(ctx, in)
}

func (m *defaultNamespace) TemplateInfoGetOne(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*NamespaceTemplateInfo, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.TemplateInfoGetOne(ctx, in, opts...)
}

func (d *directNamespace) TemplateInfoGetOne(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*NamespaceTemplateInfo, error) {
	return d.svr.TemplateInfoGetOne(ctx, in)
}

func (m *defaultNamespace) TemplateInfoGetList(ctx context.Context, in *NamespaceTemplateInfoGetListReq, opts ...grpc.CallOption) (*NamespaceTemplateInfoGetListResp, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.TemplateInfoGetList(ctx, in, opts...)
}

func (d *directNamespace) TemplateInfoGetList(ctx context.Context, in *NamespaceTemplateInfoGetListReq, opts ...grpc.CallOption) (*NamespaceTemplateInfoGetListResp, error) {
	return d.svr.TemplateInfoGetList(ctx, in)
}

func (m *defaultNamespace) LabelInfoCreate(ctx context.Context, in *NamespaceLabelInfo, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.LabelInfoCreate(ctx, in, opts...)
}

func (d *directNamespace) LabelInfoCreate(ctx context.Context, in *NamespaceLabelInfo, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.LabelInfoCreate(ctx, in)
}

func (m *defaultNamespace) LabelInfoUpdate(ctx context.Context, in *NamespaceLabelInfo, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.LabelInfoUpdate(ctx, in, opts...)
}

func (d *directNamespace) LabelInfoUpdate(ctx context.Context, in *NamespaceLabelInfo, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.LabelInfoUpdate(ctx, in)
}

func (m *defaultNamespace) LabelInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.LabelInfoDelete(ctx, in, opts...)
}

func (d *directNamespace) LabelInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.LabelInfoDelete(ctx, in)
}

func (m *defaultNamespace) LabelInfoGetOne(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*NamespaceLabelInfo, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.LabelInfoGetOne(ctx, in, opts...)
}

func (d *directNamespace) LabelInfoGetOne(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*NamespaceLabelInfo, error) {
	return d.svr.LabelInfoGetOne(ctx, in)
}

func (m *defaultNamespace) LabelInfoGetList(ctx context.Context, in *NamespaceLabelInfoGetListReq, opts ...grpc.CallOption) (*NamespaceLabelInfoGetListResp, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.LabelInfoGetList(ctx, in, opts...)
}

func (d *directNamespace) LabelInfoGetList(ctx context.Context, in *NamespaceLabelInfoGetListReq, opts ...grpc.CallOption) (*NamespaceLabelInfoGetListResp, error) {
	return d.svr.LabelInfoGetList(ctx, in)
}

func (m *defaultNamespace) LabelNodeIDCreate(ctx context.Context, in *NamespaceLabelNodeID, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.LabelNodeIDCreate(ctx, in, opts...)
}

func (d *directNamespace) LabelNodeIDCreate(ctx context.Context, in *NamespaceLabelNodeID, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.LabelNodeIDCreate(ctx, in)
}

// 已弃用,用文件列表传labelid方式获取
func (m *defaultNamespace) LabelNodeIDDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.LabelNodeIDDelete(ctx, in, opts...)
}

// 已弃用,用文件列表传labelid方式获取
func (d *directNamespace) LabelNodeIDDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.LabelNodeIDDelete(ctx, in)
}

// json转definition
func (m *defaultNamespace) Json2Fs(ctx context.Context, in *NamespaceJson2FsReq, opts ...grpc.CallOption) (*NamespaceJson2FsResp, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.Json2Fs(ctx, in, opts...)
}

// json转definition
func (d *directNamespace) Json2Fs(ctx context.Context, in *NamespaceJson2FsReq, opts ...grpc.CallOption) (*NamespaceJson2FsResp, error) {
	return d.svr.Json2Fs(ctx, in)
}

// dashboard
func (m *defaultNamespace) Dashboard(ctx context.Context, in *DashboardReq, opts ...grpc.CallOption) (*DashboardResp, error) {
	client := uns.NewNamespaceClient(m.cli.Conn())
	return client.Dashboard(ctx, in, opts...)
}

// dashboard
func (d *directNamespace) Dashboard(ctx context.Context, in *DashboardReq, opts ...grpc.CallOption) (*DashboardResp, error) {
	return d.svr.Dashboard(ctx, in)
}
