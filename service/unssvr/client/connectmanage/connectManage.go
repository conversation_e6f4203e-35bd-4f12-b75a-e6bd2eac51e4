// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.1
// Source: uns.proto

package connectmanage

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CompareInt64                     = uns.CompareInt64
	CompareString                    = uns.CompareString
	ConnectNodeInfo                  = uns.ConnectNodeInfo
	ConnectNodeInfoGetOneReq         = uns.ConnectNodeInfoGetOneReq
	ConnectNodeInfoListReq           = uns.ConnectNodeInfoListReq
	ConnectNodeInfoListResp          = uns.ConnectNodeInfoListResp
	DashboardItem                    = uns.DashboardItem
	DashboardReq                     = uns.DashboardReq
	DashboardResp                    = uns.DashboardResp
	DateRange                        = uns.DateRange
	Empty                            = uns.Empty
	NamespaceJson2FsFields           = uns.NamespaceJson2FsFields
	NamespaceJson2FsItems            = uns.NamespaceJson2FsItems
	NamespaceJson2FsReq              = uns.NamespaceJson2FsReq
	NamespaceJson2FsResp             = uns.NamespaceJson2FsResp
	NamespaceLabelInfo               = uns.NamespaceLabelInfo
	NamespaceLabelInfoGetListReq     = uns.NamespaceLabelInfoGetListReq
	NamespaceLabelInfoGetListResp    = uns.NamespaceLabelInfoGetListResp
	NamespaceLabelNodeID             = uns.NamespaceLabelNodeID
	NamespaceNodeAttachment          = uns.NamespaceNodeAttachment
	NamespaceNodeInfo                = uns.NamespaceNodeInfo
	NamespaceNodeInfoDefinition      = uns.NamespaceNodeInfoDefinition
	NamespaceNodeInfoGetFileListReq  = uns.NamespaceNodeInfoGetFileListReq
	NamespaceNodeInfoGetFileListResp = uns.NamespaceNodeInfoGetFileListResp
	NamespaceNodeInfoGetListReq      = uns.NamespaceNodeInfoGetListReq
	NamespaceNodeInfoGetListResp     = uns.NamespaceNodeInfoGetListResp
	NamespaceNodeInfoGetOneReq       = uns.NamespaceNodeInfoGetOneReq
	NamespaceTemplateInfo            = uns.NamespaceTemplateInfo
	NamespaceTemplateInfoGetListReq  = uns.NamespaceTemplateInfoGetListReq
	NamespaceTemplateInfoGetListResp = uns.NamespaceTemplateInfoGetListResp
	PageInfo                         = uns.PageInfo
	PageInfo_OrderBy                 = uns.PageInfo_OrderBy
	Request                          = uns.Request
	Response                         = uns.Response
	TimeRange                        = uns.TimeRange
	WithAppCodeID                    = uns.WithAppCodeID
	WithCode                         = uns.WithCode
	WithID                           = uns.WithID
	WithIDCode                       = uns.WithIDCode

	ConnectManage interface {
		NodeInfoCreate(ctx context.Context, in *ConnectNodeInfo, opts ...grpc.CallOption) (*WithID, error)
		NodeInfoUpdate(ctx context.Context, in *ConnectNodeInfo, opts ...grpc.CallOption) (*Empty, error)
		NodeInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
		NodeInfoGetOne(ctx context.Context, in *ConnectNodeInfoGetOneReq, opts ...grpc.CallOption) (*ConnectNodeInfo, error)
		NodeInfoGetList(ctx context.Context, in *ConnectNodeInfoListReq, opts ...grpc.CallOption) (*ConnectNodeInfoListResp, error)
		// 先不做
		MappingInfoUpdate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
		// 全部挂载在根节点1下面
		MappingInfoGetList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
		// 映射操作日志获取
		MappingOperationLogGetList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
		// 从边缘节点同步节点
		MappingSyncUns(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
		// 部署到边缘节点
		MappingDeployToEdge(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	}

	defaultConnectManage struct {
		cli zrpc.Client
	}

	directConnectManage struct {
		svcCtx *svc.ServiceContext
		svr    uns.ConnectManageServer
	}
)

func NewConnectManage(cli zrpc.Client) ConnectManage {
	return &defaultConnectManage{
		cli: cli,
	}
}

func NewDirectConnectManage(svcCtx *svc.ServiceContext, svr uns.ConnectManageServer) ConnectManage {
	return &directConnectManage{
		svr:    svr,
		svcCtx: svcCtx,
	}
}

func (m *defaultConnectManage) NodeInfoCreate(ctx context.Context, in *ConnectNodeInfo, opts ...grpc.CallOption) (*WithID, error) {
	client := uns.NewConnectManageClient(m.cli.Conn())
	return client.NodeInfoCreate(ctx, in, opts...)
}

func (d *directConnectManage) NodeInfoCreate(ctx context.Context, in *ConnectNodeInfo, opts ...grpc.CallOption) (*WithID, error) {
	return d.svr.NodeInfoCreate(ctx, in)
}

func (m *defaultConnectManage) NodeInfoUpdate(ctx context.Context, in *ConnectNodeInfo, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewConnectManageClient(m.cli.Conn())
	return client.NodeInfoUpdate(ctx, in, opts...)
}

func (d *directConnectManage) NodeInfoUpdate(ctx context.Context, in *ConnectNodeInfo, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.NodeInfoUpdate(ctx, in)
}

func (m *defaultConnectManage) NodeInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewConnectManageClient(m.cli.Conn())
	return client.NodeInfoDelete(ctx, in, opts...)
}

func (d *directConnectManage) NodeInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.NodeInfoDelete(ctx, in)
}

func (m *defaultConnectManage) NodeInfoGetOne(ctx context.Context, in *ConnectNodeInfoGetOneReq, opts ...grpc.CallOption) (*ConnectNodeInfo, error) {
	client := uns.NewConnectManageClient(m.cli.Conn())
	return client.NodeInfoGetOne(ctx, in, opts...)
}

func (d *directConnectManage) NodeInfoGetOne(ctx context.Context, in *ConnectNodeInfoGetOneReq, opts ...grpc.CallOption) (*ConnectNodeInfo, error) {
	return d.svr.NodeInfoGetOne(ctx, in)
}

func (m *defaultConnectManage) NodeInfoGetList(ctx context.Context, in *ConnectNodeInfoListReq, opts ...grpc.CallOption) (*ConnectNodeInfoListResp, error) {
	client := uns.NewConnectManageClient(m.cli.Conn())
	return client.NodeInfoGetList(ctx, in, opts...)
}

func (d *directConnectManage) NodeInfoGetList(ctx context.Context, in *ConnectNodeInfoListReq, opts ...grpc.CallOption) (*ConnectNodeInfoListResp, error) {
	return d.svr.NodeInfoGetList(ctx, in)
}

// 先不做
func (m *defaultConnectManage) MappingInfoUpdate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewConnectManageClient(m.cli.Conn())
	return client.MappingInfoUpdate(ctx, in, opts...)
}

// 先不做
func (d *directConnectManage) MappingInfoUpdate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.MappingInfoUpdate(ctx, in)
}

// 全部挂载在根节点1下面
func (m *defaultConnectManage) MappingInfoGetList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewConnectManageClient(m.cli.Conn())
	return client.MappingInfoGetList(ctx, in, opts...)
}

// 全部挂载在根节点1下面
func (d *directConnectManage) MappingInfoGetList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.MappingInfoGetList(ctx, in)
}

// 映射操作日志获取
func (m *defaultConnectManage) MappingOperationLogGetList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewConnectManageClient(m.cli.Conn())
	return client.MappingOperationLogGetList(ctx, in, opts...)
}

// 映射操作日志获取
func (d *directConnectManage) MappingOperationLogGetList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.MappingOperationLogGetList(ctx, in)
}

// 从边缘节点同步节点
func (m *defaultConnectManage) MappingSyncUns(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewConnectManageClient(m.cli.Conn())
	return client.MappingSyncUns(ctx, in, opts...)
}

// 从边缘节点同步节点
func (d *directConnectManage) MappingSyncUns(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.MappingSyncUns(ctx, in)
}

// 部署到边缘节点
func (m *defaultConnectManage) MappingDeployToEdge(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	client := uns.NewConnectManageClient(m.cli.Conn())
	return client.MappingDeployToEdge(ctx, in, opts...)
}

// 部署到边缘节点
func (d *directConnectManage) MappingDeployToEdge(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.MappingDeployToEdge(ctx, in)
}
