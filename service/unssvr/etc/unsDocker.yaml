Name: uns.rpc
ListenOn: 0.0.0.0:9623
CpuThreshold: 0
Etcd:
  Hosts:
  - etcd:2379
  Key: uns.rpc
Database:
  DBType: pgsql
  DSN: ******************************************/postgres
CacheRedis:
  - Host: redis:6379
    Pass:
    Type: node
DevLink:
  Mode: mqtt
  SubMode: emq
  Mqtt:
    ClientID: uns.rpc
    Pass: uns
    User: unssvr
    Brokers:
      - tcp://emqx:1883
    OpenApi:
      Host: http://emqx:18083
      ApiKey: 69b84915bc0d50db
      SecretKey: nzREWI29BQjrY9A1gsgczHEUhDr3kJXonkUE9A7hueCB4G
OssConf:
  OssType: aws # 如果不需要minio,可以填写local,默认存储路径为 ../oss
  PublicBucketName: tier0-upload-public
  PrivateBucketName: tier0-upload
  TemporaryBucketName: tier0-upload-temp
  AccessKeyID: ${ossAk}
  AccessKeySecret: ${ossSk}
  Location: s3.ap-southeast-1.amazonaws.com
  Region: ap-southeast-1
AwsS3tables:
  AccessKeyID: ${AWS_ACCESS_KEY_ID}
  SecretAccessKey: ${AWS_SECRET_ACCESS_KEY}
  Bucket: tier0-emqx-tablebucket
  Region: ap-southeast-1