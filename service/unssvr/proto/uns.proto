syntax = "proto3";

package uns;
option go_package="pb/uns";

message Request {
  string ping = 1;
}

message Response {
  string pong = 1;
}
//todo NodeInfo 的是示例 其他几个参考定义改造
service Namespace {
  rpc nodeInfoCreate(NamespaceNodeInfo) returns(WithID);
  rpc nodeInfoUpdate(NamespaceNodeInfo) returns(Empty);
  rpc nodeInfoDelete(WithID) returns(Empty);
  rpc nodeInfoGetOne(NamespaceNodeInfoGetOneReq) returns(NamespaceNodeInfo);
  rpc nodeInfoGetList(NamespaceNodeInfoGetListReq) returns(NamespaceNodeInfoGetListResp);
  // 获取指定命名空间下的文件列表
  rpc nodeInfoGetFileList(NamespaceNodeInfoGetFileListReq) returns(NamespaceNodeInfoGetFileListResp);
  // 回收站撤销
  rpc nodeInfoRecycleUndo(WithID) returns(Empty);
  // 回收站删除
  rpc nodeInfoRecycleDelete(WithID) returns(Empty);

  rpc templateInfoCreate(NamespaceTemplateInfo) returns(WithID);
  rpc templateInfoUpdate(NamespaceTemplateInfo) returns(Empty);
  rpc templateInfoDelete(WithID) returns(Empty);
  rpc templateInfoGetOne(WithID) returns(NamespaceTemplateInfo);
  rpc templateInfoGetList(NamespaceTemplateInfoGetListReq) returns(NamespaceTemplateInfoGetListResp);

  rpc labelInfoCreate(NamespaceLabelInfo) returns(Empty);
  rpc labelInfoUpdate(NamespaceLabelInfo) returns(Empty);
  rpc labelInfoDelete(WithID) returns(Empty);
  rpc labelInfoGetOne(WithID) returns(NamespaceLabelInfo);
  rpc labelInfoGetList(NamespaceLabelInfoGetListReq) returns(NamespaceLabelInfoGetListResp);

  rpc labelNodeIDCreate(NamespaceLabelNodeID) returns(Empty);
  // 已弃用,用文件列表传labelid方式获取
  // rpc labelNodeIDGetList(NamespaceLabelNodeIDGetListReq) returns(NamespaceLabelNodeIDGetListResp);
  rpc labelNodeIDDelete(WithID) returns(Empty);

  // json转definition
  rpc json2fs(NamespaceJson2fsReq) returns(NamespaceJson2fsResp);

  // dashboard
  rpc dashboard(DashboardReq) returns(DashboardResp);

}

service ConnectManage {
  rpc nodeInfoCreate(ConnectNodeInfo) returns (WithID);
  rpc nodeInfoUpdate(ConnectNodeInfo) returns (Empty);
  rpc nodeInfoDelete(WithID) returns (Empty);
  rpc nodeInfoGetOne(ConnectNodeInfoGetOneReq) returns (ConnectNodeInfo);
  rpc nodeInfoGetList(ConnectNodeInfoListReq) returns (ConnectNodeInfoListResp);

  //先不做
  //更新连接映射信息,节点创建的时候后端需要自动创建对应的表,节点删除的时候也需要删除映射信息
  rpc mappingInfoUpdate(Empty)returns (Empty);
  //全部挂载在根节点1下面
  rpc mappingInfoGetList(Empty)returns (Empty);
  //映射操作日志获取
  rpc mappingOperationLogGetList(Empty)returns (Empty);


  //从边缘节点同步节点
  rpc mappingSyncUns(Empty)returns (Empty);
  //部署到边缘节点
  rpc mappingDeployToEdge(Empty)returns (Empty);


}

message DateRange{
  string start = 1;
  string end = 2;
}

message TimeRange{
  int64 start = 1;
  int64 end = 2;
}

message Empty {
}

message PageInfo {
  int64 page = 1;
  int64 size = 2;
  //排序信息
  repeated OrderBy orders = 3;
  message OrderBy {
    //排序的字段名
    string field = 1;
    //排序方式：0 aes, 1 desc
    int64 sort = 2;
  }
}

message CompareString{
  string CmpType =1;//"=":相等 "!=":不相等 ">":大于">=":大于等于"<":小于"<=":小于等于 "like":模糊查询
  string value =2;//值
}
message CompareInt64{
  string CmpType =1;//"=":相等 "!=":不相等 ">":大于">=":大于等于"<":小于"<=":小于等于 "like":模糊查询
  int64 value =2;//值
}

message WithID{
  int64 id = 1;
}

message WithIDCode{
  int64 id = 1;
  string code=2;
}

message WithCode{
  string code=1;
}

message WithAppCodeID{
  int64 id = 1;
  string appCode=2;
  string code =3;//租户code
}


// NamespaceNodeInfo 用于表示命名空间节点信息
// 该消息类型包含了节点的基本信息、类型、描述、扩展属性
// 以及与节点相关的其他信息，如数据类型、聚合频率、持久化等
// 节点类型：0-文件夹, 2-文件(
// 是否的字段,传1和2，1:是2:否
message NamespaceNodeInfo{
  int64 id =1;//用雪花算法生成
  string idPath = 2; // 路径path
  int64 parentID = 3; // 父节点ID,根节点传0
  string name = 4; // 节点名称
  string namespace = 5; // 命名空间,格式为: namespace,namepath
  int64 nodeType = 6; // 节点类型: 1-文件夹, 2-文件(文件夹时只用传NamespaceNodeInfoCreateDir)
  string description = 7; // 节点描述
  string displayName = 8; // 节点显示名称
  map<string, string> extendProperties = 9;//扩展属性
  int64 templateID = 10; // 模板id
  int64 generateTemplate = 11;// 是否生成模板(只有创建文件夹时才有用)
  int64 subscribe = 12;// 是否订阅
  repeated NamespaceNodeInfoDefinition definition= 13; // 定义

  // 文件相关
  DataTypeEnum dataType = 23; // 数据类型
  string aggregationFrequency = 24;// 聚合频率
  repeated int64 aggregationTarget = 25;// 聚合文件id
  int64 persistence = 26; // 是否持久化
  int64 dashboard = 28;// 是否生成仪表盘
  int64 mockData = 29;// 是否生成模拟数据
  int64  createdTime = 30;       //插入时间
  int64  updatedTime = 31;       //更新时间
  repeated NamespaceLabelInfo labels = 32; // 标签对象列表,详情才返回

  repeated NamespaceNodeInfo children =40;//只读,当 withChildren传true时会返回,如果要获取全部树,id传1即可
  repeated NamespaceNodeAttachment attachments =41;//当 withAttachments传true时会返回
}

enum DataTypeEnum {
  DataTypeUnknown = 0; // 未知类型
  DataTypeState = 1; // 状态数据
  DataTypeAction = 2; // 行为数据
}

message NamespaceNodeInfoDefinition {
  string name = 1;           // 字段名
  string type = 2;           // 字段类型: INTEGER | LONG | FLOAT | DOUBLE | BOOLEAN | DATETIME | STRING | BLOB | LBLOB
  int64 maxLen = 3;          // 最大长度
  string remark = 4;         // 字段描述
  string displayName = 5;    // 显示名
  int64 systemField = 6;      // 是否系统
  string unit = 7;            // 单位
}

message NamespaceNodeInfoGetOneReq{
  int64 id = 1;
  bool withChildren=2;
  bool withAttachments =3;
}

message NamespaceNodeInfoGetListReq{
  bool recycle = 1;//回收站,只显示软删除的
}

message NamespaceNodeInfoGetListResp{
  int64 total =1;
  repeated NamespaceNodeInfo list=2;
}

// NamespaceNodeInfoGetFileListReq 用于获取文件列表,不会返回树结构
// 获取所有文件列表时传nodeType=2和page.size=0
// 获取模板的文件列表时传templateID和nodeType=2
// 获取标签关联的文件列表时传labelID和nodeType=0
message NamespaceNodeInfoGetFileListReq{
  PageInfo page       = 1; //分页信息 只获取一个则不填
  int64 nodeType = 2; // 节点类型: 1-文件夹, 2-文件 获取文件传2
  int64 templateID = 3; // 模板id,模板详情里的文件列表查询用
  string name = 4;
  int64 labelID = 5; // labelID,获取该标签关联的文件列表
}

message NamespaceNodeInfoGetFileListResp{
  int64 total = 1;
  repeated NamespaceNodeInfo list = 2;
}

message NamespaceNodeAttachment{
  int64 id = 1; // 附件ID,前端自己生成,不重复即可
  string fileName = 3; // 只读:文件名
  string fileUrl = 5; // 只读:访问url
  string filePath = 6;//更新的时候传
}

message NamespaceTemplateInfo{
  int64 id = 1; // 模板ID
  string name = 2; // 模板名称
  int64 nodeID = 3; // 关联的unsid
  string description = 4; // 模板描述
  int64  createdTime = 5;       //插入时间
  int64  updatedTime = 6;       //更新时间
  
  repeated NamespaceNodeInfoDefinition definition= 13; // 定义
}

message NamespaceTemplateInfoGetListReq{
  PageInfo page       = 1; //分页信息 只获取一个则不填
  string name = 2; // 模板名称
}

message NamespaceTemplateInfoGetListResp{
  int64 total =1;
  repeated NamespaceTemplateInfo list=2;
}

message NamespaceLabelInfo{
  int64 id = 1; // 标签ID
  string name = 2; // 标签名称
  int64  createdTime = 3;       //插入时间
  int64  updatedTime = 4;       //更新时间
}

message NamespaceLabelInfoGetListReq{
  PageInfo page = 1; //分页信息 只获取一个则不填
  string name = 2; // 标签名称
}

message NamespaceLabelInfoGetListResp{
  int64 total =1;
  repeated NamespaceLabelInfo list=2;
}

message NamespaceLabelNodeID{
  int64 id = 1; // 标签ID
  int64 nodeID = 2; // 关联的nodeID
  string name = 3; // uns名称，创建不用传
  string namespace = 4; // uns命名空间，创建不用传
}

message NamespaceJson2fsReq {
  string json = 1; // JSON字符串
}

message NamespaceJson2fsResp {
  repeated NamespaceJson2fsItems data = 1;
}

message NamespaceJson2fsItems {
  string dataPath = 1;
  repeated NamespaceJson2fsFields fields = 2; // 字段列表
}

message NamespaceJson2fsFields {
  string name = 1;
  string type = 2;
  bool systemField = 3; // 是否系统字段
}

message ConnectNodeInfo{
  int64 id = 1; 
  string name = 2; // 节点名称
  string description = 3; // 节点描述
  int64 authMethod = 4; // 认证方式:   1-密钥认证,2-自定义
  string secretKey = 5; // 密钥 
  string username = 6; // 用户名
  string password = 7; // 密码
  float cpu = 8; // CPU信息
  float memory = 9; // 内存信息
  int64 msg = 10; // 消息数量 
  string version = 11; // 版本信息
}

message ConnectNodeInfoListReq {
  PageInfo page = 1; // 分页信息 只获取一个则不填
  string name = 2; // 节点名称
}

message ConnectNodeInfoListResp {
    int64 total = 1;
    repeated ConnectNodeInfo list = 2;
}

message ConnectNodeInfoGetOneReq {
  int64 id = 1; // 节点ID
  string name = 2; // 节点名称
}

message DashboardReq {
  int64 nodeID =1;
  int64 timeStart=2;
  int64 timeEnd=3;
}

message DashboardResp {
    repeated NamespaceNodeInfoDefinition fields =1;         // "fields" 是一个 map，其中 key 是 string，value 是字段类型= 1;      // "filed" 是一个字符串数组
    repeated DashboardItem list  = 2;           // "y" 是一个 map，其中 key 是 string，value 是 Y 类型
}

message DashboardItem {
  int64 timestamp = 1;
  string payload = 2;
}