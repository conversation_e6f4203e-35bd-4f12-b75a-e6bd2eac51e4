package main

import (
	"context"
	"fmt"

	"github.com/FREEZONEX/Tier0-Backend/share/interceptors"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/config"
	connectmanageServer "github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/server/connectmanage"
	namespaceServer "github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/server/namespace"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/startup"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"gitee.com/unitedrhino/share/utils"
	"github.com/zeromicro/go-zero/core/service"
	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
)

func main() {
	defer utils.Recover(context.Background())
	var c config.Config
	utils.ConfMustLoad("etc/uns.yaml", &c)
	svcCtx := svc.NewServiceContext(c)
	startup.Init(svcCtx)
	s := zrpc.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		uns.RegisterNamespaceServer(grpcServer, namespaceServer.NewNamespaceServer(svcCtx))
		uns.RegisterConnectManageServer(grpcServer, connectmanageServer.NewConnectManageServer(svcCtx))
		if c.Mode == service.DevMode || c.Mode == service.TestMode {
			reflection.Register(grpcServer)
		}
	})
	defer s.Stop()
	s.AddUnaryInterceptors(interceptors.Ctxs, interceptors.Error)

	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
