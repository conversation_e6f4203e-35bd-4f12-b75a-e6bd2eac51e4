// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.4
// source: proto/uns.proto

package uns

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DataTypeEnum int32

const (
	DataTypeEnum_DataTypeUnknown DataTypeEnum = 0 // 未知类型
	DataTypeEnum_DataTypeState   DataTypeEnum = 1 // 状态数据
	DataTypeEnum_DataTypeAction  DataTypeEnum = 2 // 行为数据
)

// Enum value maps for DataTypeEnum.
var (
	DataTypeEnum_name = map[int32]string{
		0: "DataTypeUnknown",
		1: "DataTypeState",
		2: "DataTypeAction",
	}
	DataTypeEnum_value = map[string]int32{
		"DataTypeUnknown": 0,
		"DataTypeState":   1,
		"DataTypeAction":  2,
	}
)

func (x DataTypeEnum) Enum() *DataTypeEnum {
	p := new(DataTypeEnum)
	*p = x
	return p
}

func (x DataTypeEnum) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DataTypeEnum) Descriptor() protoreflect.EnumDescriptor {
	return file_proto_uns_proto_enumTypes[0].Descriptor()
}

func (DataTypeEnum) Type() protoreflect.EnumType {
	return &file_proto_uns_proto_enumTypes[0]
}

func (x DataTypeEnum) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DataTypeEnum.Descriptor instead.
func (DataTypeEnum) EnumDescriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{0}
}

type Request struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Ping string `protobuf:"bytes,1,opt,name=ping,proto3" json:"ping,omitempty"`
}

func (x *Request) Reset() {
	*x = Request{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Request) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Request) ProtoMessage() {}

func (x *Request) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Request.ProtoReflect.Descriptor instead.
func (*Request) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{0}
}

func (x *Request) GetPing() string {
	if x != nil {
		return x.Ping
	}
	return ""
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Pong string `protobuf:"bytes,1,opt,name=pong,proto3" json:"pong,omitempty"`
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{1}
}

func (x *Response) GetPong() string {
	if x != nil {
		return x.Pong
	}
	return ""
}

type DateRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start string `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	End   string `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *DateRange) Reset() {
	*x = DateRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateRange) ProtoMessage() {}

func (x *DateRange) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateRange.ProtoReflect.Descriptor instead.
func (*DateRange) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{2}
}

func (x *DateRange) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *DateRange) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

type TimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start int64 `protobuf:"varint,1,opt,name=start,proto3" json:"start,omitempty"`
	End   int64 `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *TimeRange) Reset() {
	*x = TimeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRange) ProtoMessage() {}

func (x *TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRange.ProtoReflect.Descriptor instead.
func (*TimeRange) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{3}
}

func (x *TimeRange) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *TimeRange) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{4}
}

type PageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size int64 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	// 排序信息
	Orders []*PageInfo_OrderBy `protobuf:"bytes,3,rep,name=orders,proto3" json:"orders,omitempty"`
}

func (x *PageInfo) Reset() {
	*x = PageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageInfo) ProtoMessage() {}

func (x *PageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageInfo.ProtoReflect.Descriptor instead.
func (*PageInfo) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{5}
}

func (x *PageInfo) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageInfo) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *PageInfo) GetOrders() []*PageInfo_OrderBy {
	if x != nil {
		return x.Orders
	}
	return nil
}

type CompareString struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CmpType string `protobuf:"bytes,1,opt,name=CmpType,proto3" json:"CmpType,omitempty"` //"=":相等 "!=":不相等 ">":大于">=":大于等于"<":小于"<=":小于等于 "like":模糊查询
	Value   string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`     //值
}

func (x *CompareString) Reset() {
	*x = CompareString{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompareString) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompareString) ProtoMessage() {}

func (x *CompareString) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompareString.ProtoReflect.Descriptor instead.
func (*CompareString) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{6}
}

func (x *CompareString) GetCmpType() string {
	if x != nil {
		return x.CmpType
	}
	return ""
}

func (x *CompareString) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type CompareInt64 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CmpType string `protobuf:"bytes,1,opt,name=CmpType,proto3" json:"CmpType,omitempty"` //"=":相等 "!=":不相等 ">":大于">=":大于等于"<":小于"<=":小于等于 "like":模糊查询
	Value   int64  `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`    //值
}

func (x *CompareInt64) Reset() {
	*x = CompareInt64{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompareInt64) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompareInt64) ProtoMessage() {}

func (x *CompareInt64) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompareInt64.ProtoReflect.Descriptor instead.
func (*CompareInt64) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{7}
}

func (x *CompareInt64) GetCmpType() string {
	if x != nil {
		return x.CmpType
	}
	return ""
}

func (x *CompareInt64) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type WithID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *WithID) Reset() {
	*x = WithID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithID) ProtoMessage() {}

func (x *WithID) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithID.ProtoReflect.Descriptor instead.
func (*WithID) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{8}
}

func (x *WithID) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type WithIDCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *WithIDCode) Reset() {
	*x = WithIDCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithIDCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithIDCode) ProtoMessage() {}

func (x *WithIDCode) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithIDCode.ProtoReflect.Descriptor instead.
func (*WithIDCode) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{9}
}

func (x *WithIDCode) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WithIDCode) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type WithCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *WithCode) Reset() {
	*x = WithCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithCode) ProtoMessage() {}

func (x *WithCode) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithCode.ProtoReflect.Descriptor instead.
func (*WithCode) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{10}
}

func (x *WithCode) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type WithAppCodeID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AppCode string `protobuf:"bytes,2,opt,name=appCode,proto3" json:"appCode,omitempty"`
	Code    string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"` //租户code
}

func (x *WithAppCodeID) Reset() {
	*x = WithAppCodeID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithAppCodeID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithAppCodeID) ProtoMessage() {}

func (x *WithAppCodeID) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithAppCodeID.ProtoReflect.Descriptor instead.
func (*WithAppCodeID) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{11}
}

func (x *WithAppCodeID) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WithAppCodeID) GetAppCode() string {
	if x != nil {
		return x.AppCode
	}
	return ""
}

func (x *WithAppCodeID) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

// NamespaceNodeInfo 用于表示命名空间节点信息
// 该消息类型包含了节点的基本信息、类型、描述、扩展属性
// 以及与节点相关的其他信息，如数据类型、聚合频率、持久化等
// 节点类型：0-文件夹, 2-文件(
// 是否的字段,传1和2，1:是2:否
type NamespaceNodeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id               int64                          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                                                                                    //用雪花算法生成
	IdPath           string                         `protobuf:"bytes,2,opt,name=idPath,proto3" json:"idPath,omitempty"`                                                                                                             // 路径path
	ParentID         int64                          `protobuf:"varint,3,opt,name=parentID,proto3" json:"parentID,omitempty"`                                                                                                        // 父节点ID,根节点传0
	Name             string                         `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                                                                                                 // 节点名称
	Namespace        string                         `protobuf:"bytes,5,opt,name=namespace,proto3" json:"namespace,omitempty"`                                                                                                       // 命名空间,格式为: namespace,namepath
	NodeType         int64                          `protobuf:"varint,6,opt,name=nodeType,proto3" json:"nodeType,omitempty"`                                                                                                        // 节点类型: 1-文件夹, 2-文件(文件夹时只用传NamespaceNodeInfoCreateDir)
	Description      string                         `protobuf:"bytes,7,opt,name=description,proto3" json:"description,omitempty"`                                                                                                   // 节点描述
	DisplayName      string                         `protobuf:"bytes,8,opt,name=displayName,proto3" json:"displayName,omitempty"`                                                                                                   // 节点显示名称
	ExtendProperties map[string]string              `protobuf:"bytes,9,rep,name=extendProperties,proto3" json:"extendProperties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //扩展属性
	TemplateID       int64                          `protobuf:"varint,10,opt,name=templateID,proto3" json:"templateID,omitempty"`                                                                                                   // 模板id
	GenerateTemplate int64                          `protobuf:"varint,11,opt,name=generateTemplate,proto3" json:"generateTemplate,omitempty"`                                                                                       // 是否生成模板(只有创建文件夹时才有用)
	Subscribe        int64                          `protobuf:"varint,12,opt,name=subscribe,proto3" json:"subscribe,omitempty"`                                                                                                     // 是否订阅
	Definition       []*NamespaceNodeInfoDefinition `protobuf:"bytes,13,rep,name=definition,proto3" json:"definition,omitempty"`                                                                                                    // 定义
	// 文件相关
	DataType             DataTypeEnum               `protobuf:"varint,23,opt,name=dataType,proto3,enum=uns.DataTypeEnum" json:"dataType,omitempty"`    // 数据类型
	AggregationFrequency string                     `protobuf:"bytes,24,opt,name=aggregationFrequency,proto3" json:"aggregationFrequency,omitempty"`   // 聚合频率
	AggregationTarget    []int64                    `protobuf:"varint,25,rep,packed,name=aggregationTarget,proto3" json:"aggregationTarget,omitempty"` // 聚合文件id
	Persistence          int64                      `protobuf:"varint,26,opt,name=persistence,proto3" json:"persistence,omitempty"`                    // 是否持久化
	Dashboard            int64                      `protobuf:"varint,28,opt,name=dashboard,proto3" json:"dashboard,omitempty"`                        // 是否生成仪表盘
	MockData             int64                      `protobuf:"varint,29,opt,name=mockData,proto3" json:"mockData,omitempty"`                          // 是否生成模拟数据
	CreatedTime          int64                      `protobuf:"varint,30,opt,name=createdTime,proto3" json:"createdTime,omitempty"`                    //插入时间
	UpdatedTime          int64                      `protobuf:"varint,31,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"`                    //更新时间
	Labels               []*NamespaceLabelInfo      `protobuf:"bytes,32,rep,name=labels,proto3" json:"labels,omitempty"`                               // 标签对象列表,详情才返回
	Children             []*NamespaceNodeInfo       `protobuf:"bytes,40,rep,name=children,proto3" json:"children,omitempty"`                           //只读,当 withChildren传true时会返回,如果要获取全部树,id传1即可
	Attachments          []*NamespaceNodeAttachment `protobuf:"bytes,41,rep,name=attachments,proto3" json:"attachments,omitempty"`                     //当 withAttachments传true时会返回
}

func (x *NamespaceNodeInfo) Reset() {
	*x = NamespaceNodeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceNodeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceNodeInfo) ProtoMessage() {}

func (x *NamespaceNodeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceNodeInfo.ProtoReflect.Descriptor instead.
func (*NamespaceNodeInfo) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{12}
}

func (x *NamespaceNodeInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NamespaceNodeInfo) GetIdPath() string {
	if x != nil {
		return x.IdPath
	}
	return ""
}

func (x *NamespaceNodeInfo) GetParentID() int64 {
	if x != nil {
		return x.ParentID
	}
	return 0
}

func (x *NamespaceNodeInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamespaceNodeInfo) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *NamespaceNodeInfo) GetNodeType() int64 {
	if x != nil {
		return x.NodeType
	}
	return 0
}

func (x *NamespaceNodeInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *NamespaceNodeInfo) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *NamespaceNodeInfo) GetExtendProperties() map[string]string {
	if x != nil {
		return x.ExtendProperties
	}
	return nil
}

func (x *NamespaceNodeInfo) GetTemplateID() int64 {
	if x != nil {
		return x.TemplateID
	}
	return 0
}

func (x *NamespaceNodeInfo) GetGenerateTemplate() int64 {
	if x != nil {
		return x.GenerateTemplate
	}
	return 0
}

func (x *NamespaceNodeInfo) GetSubscribe() int64 {
	if x != nil {
		return x.Subscribe
	}
	return 0
}

func (x *NamespaceNodeInfo) GetDefinition() []*NamespaceNodeInfoDefinition {
	if x != nil {
		return x.Definition
	}
	return nil
}

func (x *NamespaceNodeInfo) GetDataType() DataTypeEnum {
	if x != nil {
		return x.DataType
	}
	return DataTypeEnum_DataTypeUnknown
}

func (x *NamespaceNodeInfo) GetAggregationFrequency() string {
	if x != nil {
		return x.AggregationFrequency
	}
	return ""
}

func (x *NamespaceNodeInfo) GetAggregationTarget() []int64 {
	if x != nil {
		return x.AggregationTarget
	}
	return nil
}

func (x *NamespaceNodeInfo) GetPersistence() int64 {
	if x != nil {
		return x.Persistence
	}
	return 0
}

func (x *NamespaceNodeInfo) GetDashboard() int64 {
	if x != nil {
		return x.Dashboard
	}
	return 0
}

func (x *NamespaceNodeInfo) GetMockData() int64 {
	if x != nil {
		return x.MockData
	}
	return 0
}

func (x *NamespaceNodeInfo) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *NamespaceNodeInfo) GetUpdatedTime() int64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *NamespaceNodeInfo) GetLabels() []*NamespaceLabelInfo {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *NamespaceNodeInfo) GetChildren() []*NamespaceNodeInfo {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *NamespaceNodeInfo) GetAttachments() []*NamespaceNodeAttachment {
	if x != nil {
		return x.Attachments
	}
	return nil
}

type NamespaceNodeInfoDefinition struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                // 字段名
	Type        string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`                // 字段类型: INTEGER | LONG | FLOAT | DOUBLE | BOOLEAN | DATETIME | STRING | BLOB | LBLOB
	MaxLen      int64  `protobuf:"varint,3,opt,name=maxLen,proto3" json:"maxLen,omitempty"`           // 最大长度
	Remark      string `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`            // 字段描述
	DisplayName string `protobuf:"bytes,5,opt,name=displayName,proto3" json:"displayName,omitempty"`  // 显示名
	SystemField int64  `protobuf:"varint,6,opt,name=systemField,proto3" json:"systemField,omitempty"` // 是否系统
	Unit        string `protobuf:"bytes,7,opt,name=unit,proto3" json:"unit,omitempty"`                // 单位
}

func (x *NamespaceNodeInfoDefinition) Reset() {
	*x = NamespaceNodeInfoDefinition{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceNodeInfoDefinition) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceNodeInfoDefinition) ProtoMessage() {}

func (x *NamespaceNodeInfoDefinition) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceNodeInfoDefinition.ProtoReflect.Descriptor instead.
func (*NamespaceNodeInfoDefinition) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{13}
}

func (x *NamespaceNodeInfoDefinition) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamespaceNodeInfoDefinition) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *NamespaceNodeInfoDefinition) GetMaxLen() int64 {
	if x != nil {
		return x.MaxLen
	}
	return 0
}

func (x *NamespaceNodeInfoDefinition) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *NamespaceNodeInfoDefinition) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *NamespaceNodeInfoDefinition) GetSystemField() int64 {
	if x != nil {
		return x.SystemField
	}
	return 0
}

func (x *NamespaceNodeInfoDefinition) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

type NamespaceNodeInfoGetOneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	WithChildren    bool  `protobuf:"varint,2,opt,name=withChildren,proto3" json:"withChildren,omitempty"`
	WithAttachments bool  `protobuf:"varint,3,opt,name=withAttachments,proto3" json:"withAttachments,omitempty"`
}

func (x *NamespaceNodeInfoGetOneReq) Reset() {
	*x = NamespaceNodeInfoGetOneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceNodeInfoGetOneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceNodeInfoGetOneReq) ProtoMessage() {}

func (x *NamespaceNodeInfoGetOneReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceNodeInfoGetOneReq.ProtoReflect.Descriptor instead.
func (*NamespaceNodeInfoGetOneReq) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{14}
}

func (x *NamespaceNodeInfoGetOneReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NamespaceNodeInfoGetOneReq) GetWithChildren() bool {
	if x != nil {
		return x.WithChildren
	}
	return false
}

func (x *NamespaceNodeInfoGetOneReq) GetWithAttachments() bool {
	if x != nil {
		return x.WithAttachments
	}
	return false
}

type NamespaceNodeInfoGetListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Recycle bool `protobuf:"varint,1,opt,name=recycle,proto3" json:"recycle,omitempty"` //回收站,只显示软删除的
}

func (x *NamespaceNodeInfoGetListReq) Reset() {
	*x = NamespaceNodeInfoGetListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceNodeInfoGetListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceNodeInfoGetListReq) ProtoMessage() {}

func (x *NamespaceNodeInfoGetListReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceNodeInfoGetListReq.ProtoReflect.Descriptor instead.
func (*NamespaceNodeInfoGetListReq) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{15}
}

func (x *NamespaceNodeInfoGetListReq) GetRecycle() bool {
	if x != nil {
		return x.Recycle
	}
	return false
}

type NamespaceNodeInfoGetListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*NamespaceNodeInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *NamespaceNodeInfoGetListResp) Reset() {
	*x = NamespaceNodeInfoGetListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceNodeInfoGetListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceNodeInfoGetListResp) ProtoMessage() {}

func (x *NamespaceNodeInfoGetListResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceNodeInfoGetListResp.ProtoReflect.Descriptor instead.
func (*NamespaceNodeInfoGetListResp) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{16}
}

func (x *NamespaceNodeInfoGetListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *NamespaceNodeInfoGetListResp) GetList() []*NamespaceNodeInfo {
	if x != nil {
		return x.List
	}
	return nil
}

// NamespaceNodeInfoGetFileListReq 用于获取文件列表,不会返回树结构
// 获取所有文件列表时传nodeType=2和page.size=0
// 获取模板的文件列表时传templateID和nodeType=2
// 获取标签关联的文件列表时传labelID和nodeType=0
type NamespaceNodeInfoGetFileListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page       *PageInfo `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"`              //分页信息 只获取一个则不填
	NodeType   int64     `protobuf:"varint,2,opt,name=nodeType,proto3" json:"nodeType,omitempty"`     // 节点类型: 1-文件夹, 2-文件 获取文件传2
	TemplateID int64     `protobuf:"varint,3,opt,name=templateID,proto3" json:"templateID,omitempty"` // 模板id,模板详情里的文件列表查询用
	Name       string    `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	LabelID    int64     `protobuf:"varint,5,opt,name=labelID,proto3" json:"labelID,omitempty"` // labelID,获取该标签关联的文件列表
}

func (x *NamespaceNodeInfoGetFileListReq) Reset() {
	*x = NamespaceNodeInfoGetFileListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceNodeInfoGetFileListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceNodeInfoGetFileListReq) ProtoMessage() {}

func (x *NamespaceNodeInfoGetFileListReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceNodeInfoGetFileListReq.ProtoReflect.Descriptor instead.
func (*NamespaceNodeInfoGetFileListReq) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{17}
}

func (x *NamespaceNodeInfoGetFileListReq) GetPage() *PageInfo {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *NamespaceNodeInfoGetFileListReq) GetNodeType() int64 {
	if x != nil {
		return x.NodeType
	}
	return 0
}

func (x *NamespaceNodeInfoGetFileListReq) GetTemplateID() int64 {
	if x != nil {
		return x.TemplateID
	}
	return 0
}

func (x *NamespaceNodeInfoGetFileListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamespaceNodeInfoGetFileListReq) GetLabelID() int64 {
	if x != nil {
		return x.LabelID
	}
	return 0
}

type NamespaceNodeInfoGetFileListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*NamespaceNodeInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *NamespaceNodeInfoGetFileListResp) Reset() {
	*x = NamespaceNodeInfoGetFileListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceNodeInfoGetFileListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceNodeInfoGetFileListResp) ProtoMessage() {}

func (x *NamespaceNodeInfoGetFileListResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceNodeInfoGetFileListResp.ProtoReflect.Descriptor instead.
func (*NamespaceNodeInfoGetFileListResp) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{18}
}

func (x *NamespaceNodeInfoGetFileListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *NamespaceNodeInfoGetFileListResp) GetList() []*NamespaceNodeInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type NamespaceNodeAttachment struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id       int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`            // 附件ID,前端自己生成,不重复即可
	FileName string `protobuf:"bytes,3,opt,name=fileName,proto3" json:"fileName,omitempty"` // 只读:文件名
	FileUrl  string `protobuf:"bytes,5,opt,name=fileUrl,proto3" json:"fileUrl,omitempty"`   // 只读:访问url
	FilePath string `protobuf:"bytes,6,opt,name=filePath,proto3" json:"filePath,omitempty"` //更新的时候传
}

func (x *NamespaceNodeAttachment) Reset() {
	*x = NamespaceNodeAttachment{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceNodeAttachment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceNodeAttachment) ProtoMessage() {}

func (x *NamespaceNodeAttachment) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceNodeAttachment.ProtoReflect.Descriptor instead.
func (*NamespaceNodeAttachment) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{19}
}

func (x *NamespaceNodeAttachment) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NamespaceNodeAttachment) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

func (x *NamespaceNodeAttachment) GetFileUrl() string {
	if x != nil {
		return x.FileUrl
	}
	return ""
}

func (x *NamespaceNodeAttachment) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

type NamespaceTemplateInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64                          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                   // 模板ID
	Name        string                         `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                // 模板名称
	NodeID      int64                          `protobuf:"varint,3,opt,name=nodeID,proto3" json:"nodeID,omitempty"`           // 关联的unsid
	Description string                         `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`  // 模板描述
	CreatedTime int64                          `protobuf:"varint,5,opt,name=createdTime,proto3" json:"createdTime,omitempty"` //插入时间
	UpdatedTime int64                          `protobuf:"varint,6,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"` //更新时间
	Definition  []*NamespaceNodeInfoDefinition `protobuf:"bytes,13,rep,name=definition,proto3" json:"definition,omitempty"`   // 定义
}

func (x *NamespaceTemplateInfo) Reset() {
	*x = NamespaceTemplateInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceTemplateInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceTemplateInfo) ProtoMessage() {}

func (x *NamespaceTemplateInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceTemplateInfo.ProtoReflect.Descriptor instead.
func (*NamespaceTemplateInfo) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{20}
}

func (x *NamespaceTemplateInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NamespaceTemplateInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamespaceTemplateInfo) GetNodeID() int64 {
	if x != nil {
		return x.NodeID
	}
	return 0
}

func (x *NamespaceTemplateInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *NamespaceTemplateInfo) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *NamespaceTemplateInfo) GetUpdatedTime() int64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

func (x *NamespaceTemplateInfo) GetDefinition() []*NamespaceNodeInfoDefinition {
	if x != nil {
		return x.Definition
	}
	return nil
}

type NamespaceTemplateInfoGetListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *PageInfo `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"` //分页信息 只获取一个则不填
	Name string    `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` // 模板名称
}

func (x *NamespaceTemplateInfoGetListReq) Reset() {
	*x = NamespaceTemplateInfoGetListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceTemplateInfoGetListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceTemplateInfoGetListReq) ProtoMessage() {}

func (x *NamespaceTemplateInfoGetListReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceTemplateInfoGetListReq.ProtoReflect.Descriptor instead.
func (*NamespaceTemplateInfoGetListReq) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{21}
}

func (x *NamespaceTemplateInfoGetListReq) GetPage() *PageInfo {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *NamespaceTemplateInfoGetListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type NamespaceTemplateInfoGetListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                    `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*NamespaceTemplateInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *NamespaceTemplateInfoGetListResp) Reset() {
	*x = NamespaceTemplateInfoGetListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceTemplateInfoGetListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceTemplateInfoGetListResp) ProtoMessage() {}

func (x *NamespaceTemplateInfoGetListResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceTemplateInfoGetListResp.ProtoReflect.Descriptor instead.
func (*NamespaceTemplateInfoGetListResp) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{22}
}

func (x *NamespaceTemplateInfoGetListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *NamespaceTemplateInfoGetListResp) GetList() []*NamespaceTemplateInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type NamespaceLabelInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                   // 标签ID
	Name        string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                // 标签名称
	CreatedTime int64  `protobuf:"varint,3,opt,name=createdTime,proto3" json:"createdTime,omitempty"` //插入时间
	UpdatedTime int64  `protobuf:"varint,4,opt,name=updatedTime,proto3" json:"updatedTime,omitempty"` //更新时间
}

func (x *NamespaceLabelInfo) Reset() {
	*x = NamespaceLabelInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceLabelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceLabelInfo) ProtoMessage() {}

func (x *NamespaceLabelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceLabelInfo.ProtoReflect.Descriptor instead.
func (*NamespaceLabelInfo) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{23}
}

func (x *NamespaceLabelInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NamespaceLabelInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamespaceLabelInfo) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *NamespaceLabelInfo) GetUpdatedTime() int64 {
	if x != nil {
		return x.UpdatedTime
	}
	return 0
}

type NamespaceLabelInfoGetListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *PageInfo `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"` //分页信息 只获取一个则不填
	Name string    `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` // 标签名称
}

func (x *NamespaceLabelInfoGetListReq) Reset() {
	*x = NamespaceLabelInfoGetListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceLabelInfoGetListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceLabelInfoGetListReq) ProtoMessage() {}

func (x *NamespaceLabelInfoGetListReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceLabelInfoGetListReq.ProtoReflect.Descriptor instead.
func (*NamespaceLabelInfoGetListReq) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{24}
}

func (x *NamespaceLabelInfoGetListReq) GetPage() *PageInfo {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *NamespaceLabelInfoGetListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type NamespaceLabelInfoGetListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*NamespaceLabelInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *NamespaceLabelInfoGetListResp) Reset() {
	*x = NamespaceLabelInfoGetListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceLabelInfoGetListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceLabelInfoGetListResp) ProtoMessage() {}

func (x *NamespaceLabelInfoGetListResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceLabelInfoGetListResp.ProtoReflect.Descriptor instead.
func (*NamespaceLabelInfoGetListResp) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{25}
}

func (x *NamespaceLabelInfoGetListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *NamespaceLabelInfoGetListResp) GetList() []*NamespaceLabelInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type NamespaceLabelNodeID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`              // 标签ID
	NodeID    int64  `protobuf:"varint,2,opt,name=nodeID,proto3" json:"nodeID,omitempty"`      // 关联的nodeID
	Name      string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`           // uns名称，创建不用传
	Namespace string `protobuf:"bytes,4,opt,name=namespace,proto3" json:"namespace,omitempty"` // uns命名空间，创建不用传
}

func (x *NamespaceLabelNodeID) Reset() {
	*x = NamespaceLabelNodeID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceLabelNodeID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceLabelNodeID) ProtoMessage() {}

func (x *NamespaceLabelNodeID) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceLabelNodeID.ProtoReflect.Descriptor instead.
func (*NamespaceLabelNodeID) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{26}
}

func (x *NamespaceLabelNodeID) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NamespaceLabelNodeID) GetNodeID() int64 {
	if x != nil {
		return x.NodeID
	}
	return 0
}

func (x *NamespaceLabelNodeID) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamespaceLabelNodeID) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

type NamespaceJson2FsReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Json string `protobuf:"bytes,1,opt,name=json,proto3" json:"json,omitempty"` // JSON字符串
}

func (x *NamespaceJson2FsReq) Reset() {
	*x = NamespaceJson2FsReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceJson2FsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceJson2FsReq) ProtoMessage() {}

func (x *NamespaceJson2FsReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceJson2FsReq.ProtoReflect.Descriptor instead.
func (*NamespaceJson2FsReq) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{27}
}

func (x *NamespaceJson2FsReq) GetJson() string {
	if x != nil {
		return x.Json
	}
	return ""
}

type NamespaceJson2FsResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Data []*NamespaceJson2FsItems `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
}

func (x *NamespaceJson2FsResp) Reset() {
	*x = NamespaceJson2FsResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceJson2FsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceJson2FsResp) ProtoMessage() {}

func (x *NamespaceJson2FsResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceJson2FsResp.ProtoReflect.Descriptor instead.
func (*NamespaceJson2FsResp) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{28}
}

func (x *NamespaceJson2FsResp) GetData() []*NamespaceJson2FsItems {
	if x != nil {
		return x.Data
	}
	return nil
}

type NamespaceJson2FsItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	DataPath string                    `protobuf:"bytes,1,opt,name=dataPath,proto3" json:"dataPath,omitempty"`
	Fields   []*NamespaceJson2FsFields `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"` // 字段列表
}

func (x *NamespaceJson2FsItems) Reset() {
	*x = NamespaceJson2FsItems{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceJson2FsItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceJson2FsItems) ProtoMessage() {}

func (x *NamespaceJson2FsItems) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceJson2FsItems.ProtoReflect.Descriptor instead.
func (*NamespaceJson2FsItems) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{29}
}

func (x *NamespaceJson2FsItems) GetDataPath() string {
	if x != nil {
		return x.DataPath
	}
	return ""
}

func (x *NamespaceJson2FsItems) GetFields() []*NamespaceJson2FsFields {
	if x != nil {
		return x.Fields
	}
	return nil
}

type NamespaceJson2FsFields struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name        string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Type        string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`
	SystemField bool   `protobuf:"varint,3,opt,name=systemField,proto3" json:"systemField,omitempty"` // 是否系统字段
}

func (x *NamespaceJson2FsFields) Reset() {
	*x = NamespaceJson2FsFields{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceJson2FsFields) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceJson2FsFields) ProtoMessage() {}

func (x *NamespaceJson2FsFields) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceJson2FsFields.ProtoReflect.Descriptor instead.
func (*NamespaceJson2FsFields) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{30}
}

func (x *NamespaceJson2FsFields) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamespaceJson2FsFields) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *NamespaceJson2FsFields) GetSystemField() bool {
	if x != nil {
		return x.SystemField
	}
	return false
}

type ConnectNodeInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name        string  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`               // 节点名称
	Description string  `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"` // 节点描述
	AuthMethod  int64   `protobuf:"varint,4,opt,name=authMethod,proto3" json:"authMethod,omitempty"`  // 认证方式:   1-密钥认证,2-自定义
	SecretKey   string  `protobuf:"bytes,5,opt,name=secretKey,proto3" json:"secretKey,omitempty"`     // 密钥
	Username    string  `protobuf:"bytes,6,opt,name=username,proto3" json:"username,omitempty"`       // 用户名
	Password    string  `protobuf:"bytes,7,opt,name=password,proto3" json:"password,omitempty"`       // 密码
	Cpu         float32 `protobuf:"fixed32,8,opt,name=cpu,proto3" json:"cpu,omitempty"`               // CPU信息
	Memory      float32 `protobuf:"fixed32,9,opt,name=memory,proto3" json:"memory,omitempty"`         // 内存信息
	Msg         int64   `protobuf:"varint,10,opt,name=msg,proto3" json:"msg,omitempty"`               // 消息数量
	Version     string  `protobuf:"bytes,11,opt,name=version,proto3" json:"version,omitempty"`        // 版本信息
}

func (x *ConnectNodeInfo) Reset() {
	*x = ConnectNodeInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectNodeInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectNodeInfo) ProtoMessage() {}

func (x *ConnectNodeInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectNodeInfo.ProtoReflect.Descriptor instead.
func (*ConnectNodeInfo) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{31}
}

func (x *ConnectNodeInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ConnectNodeInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ConnectNodeInfo) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ConnectNodeInfo) GetAuthMethod() int64 {
	if x != nil {
		return x.AuthMethod
	}
	return 0
}

func (x *ConnectNodeInfo) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

func (x *ConnectNodeInfo) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *ConnectNodeInfo) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ConnectNodeInfo) GetCpu() float32 {
	if x != nil {
		return x.Cpu
	}
	return 0
}

func (x *ConnectNodeInfo) GetMemory() float32 {
	if x != nil {
		return x.Memory
	}
	return 0
}

func (x *ConnectNodeInfo) GetMsg() int64 {
	if x != nil {
		return x.Msg
	}
	return 0
}

func (x *ConnectNodeInfo) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type ConnectNodeInfoListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *PageInfo `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"` // 分页信息 只获取一个则不填
	Name string    `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` // 节点名称
}

func (x *ConnectNodeInfoListReq) Reset() {
	*x = ConnectNodeInfoListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectNodeInfoListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectNodeInfoListReq) ProtoMessage() {}

func (x *ConnectNodeInfoListReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectNodeInfoListReq.ProtoReflect.Descriptor instead.
func (*ConnectNodeInfoListReq) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{32}
}

func (x *ConnectNodeInfoListReq) GetPage() *PageInfo {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *ConnectNodeInfoListReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ConnectNodeInfoListResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Total int64              `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List  []*ConnectNodeInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *ConnectNodeInfoListResp) Reset() {
	*x = ConnectNodeInfoListResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectNodeInfoListResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectNodeInfoListResp) ProtoMessage() {}

func (x *ConnectNodeInfoListResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectNodeInfoListResp.ProtoReflect.Descriptor instead.
func (*ConnectNodeInfoListResp) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{33}
}

func (x *ConnectNodeInfoListResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ConnectNodeInfoListResp) GetList() []*ConnectNodeInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type ConnectNodeInfoGetOneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`    // 节点ID
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` // 节点名称
}

func (x *ConnectNodeInfoGetOneReq) Reset() {
	*x = ConnectNodeInfoGetOneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ConnectNodeInfoGetOneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectNodeInfoGetOneReq) ProtoMessage() {}

func (x *ConnectNodeInfoGetOneReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectNodeInfoGetOneReq.ProtoReflect.Descriptor instead.
func (*ConnectNodeInfoGetOneReq) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{34}
}

func (x *ConnectNodeInfoGetOneReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ConnectNodeInfoGetOneReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type DashboardReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	NodeID    int64 `protobuf:"varint,1,opt,name=nodeID,proto3" json:"nodeID,omitempty"`
	TimeStart int64 `protobuf:"varint,2,opt,name=timeStart,proto3" json:"timeStart,omitempty"`
	TimeEnd   int64 `protobuf:"varint,3,opt,name=timeEnd,proto3" json:"timeEnd,omitempty"`
}

func (x *DashboardReq) Reset() {
	*x = DashboardReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardReq) ProtoMessage() {}

func (x *DashboardReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardReq.ProtoReflect.Descriptor instead.
func (*DashboardReq) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{35}
}

func (x *DashboardReq) GetNodeID() int64 {
	if x != nil {
		return x.NodeID
	}
	return 0
}

func (x *DashboardReq) GetTimeStart() int64 {
	if x != nil {
		return x.TimeStart
	}
	return 0
}

func (x *DashboardReq) GetTimeEnd() int64 {
	if x != nil {
		return x.TimeEnd
	}
	return 0
}

type DashboardResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Fields []*NamespaceNodeInfoDefinition `protobuf:"bytes,1,rep,name=fields,proto3" json:"fields,omitempty"` // "fields" 是一个 map，其中 key 是 string，value 是字段类型= 1;      // "filed" 是一个字符串数组
	List   []*DashboardItem               `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`     // "y" 是一个 map，其中 key 是 string，value 是 Y 类型
}

func (x *DashboardResp) Reset() {
	*x = DashboardResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardResp) ProtoMessage() {}

func (x *DashboardResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardResp.ProtoReflect.Descriptor instead.
func (*DashboardResp) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{36}
}

func (x *DashboardResp) GetFields() []*NamespaceNodeInfoDefinition {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *DashboardResp) GetList() []*DashboardItem {
	if x != nil {
		return x.List
	}
	return nil
}

type DashboardItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Timestamp int64  `protobuf:"varint,1,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Payload   string `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
}

func (x *DashboardItem) Reset() {
	*x = DashboardItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DashboardItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DashboardItem) ProtoMessage() {}

func (x *DashboardItem) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DashboardItem.ProtoReflect.Descriptor instead.
func (*DashboardItem) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{37}
}

func (x *DashboardItem) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *DashboardItem) GetPayload() string {
	if x != nil {
		return x.Payload
	}
	return ""
}

type PageInfo_OrderBy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 排序的字段名
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	// 排序方式：0 aes, 1 desc
	Sort int64 `protobuf:"varint,2,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *PageInfo_OrderBy) Reset() {
	*x = PageInfo_OrderBy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_uns_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageInfo_OrderBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageInfo_OrderBy) ProtoMessage() {}

func (x *PageInfo_OrderBy) ProtoReflect() protoreflect.Message {
	mi := &file_proto_uns_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageInfo_OrderBy.ProtoReflect.Descriptor instead.
func (*PageInfo_OrderBy) Descriptor() ([]byte, []int) {
	return file_proto_uns_proto_rawDescGZIP(), []int{5, 0}
}

func (x *PageInfo_OrderBy) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *PageInfo_OrderBy) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

var File_proto_uns_proto protoreflect.FileDescriptor

var file_proto_uns_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x75, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x03, 0x75, 0x6e, 0x73, 0x22, 0x1d, 0x0a, 0x07, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x70, 0x69, 0x6e, 0x67, 0x22, 0x1e, 0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x6e, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x70, 0x6f, 0x6e, 0x67, 0x22, 0x33, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0x33, 0x0a, 0x09, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a,
	0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22,
	0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x96, 0x01, 0x0a, 0x08, 0x50, 0x61, 0x67,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x2d, 0x0a,
	0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x75, 0x6e, 0x73, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x42, 0x79, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x1a, 0x33, 0x0a, 0x07,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72,
	0x74, 0x22, 0x3f, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x53, 0x74, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x3e, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x49, 0x6e, 0x74,
	0x36, 0x34, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x22, 0x18, 0x0a, 0x06, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x30, 0x0a, 0x0a,
	0x57, 0x69, 0x74, 0x68, 0x49, 0x44, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x1e,
	0x0a, 0x08, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x4d,
	0x0a, 0x0d, 0x57, 0x69, 0x74, 0x68, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x70, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x8a, 0x08,
	0x0a, 0x11, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x69, 0x64, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x69, 0x64, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70,
	0x61, 0x72, 0x65, 0x6e, 0x74, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x64,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6e, 0x6f, 0x64,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x58, 0x0a, 0x10, 0x65, 0x78, 0x74,
	0x65, 0x6e, 0x64, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x09, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x45, 0x78, 0x74, 0x65,
	0x6e, 0x64, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x10, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49,
	0x44, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74,
	0x65, 0x49, 0x44, 0x12, 0x2a, 0x0a, 0x10, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x52, 0x10, 0x67,
	0x65, 0x6e, 0x65, 0x72, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x12, 0x40, 0x0a,
	0x0a, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2d, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x18, 0x17, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x11, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65,
	0x45, 0x6e, 0x75, 0x6d, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x32,
	0x0a, 0x14, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x6e, 0x63, 0x79, 0x18, 0x18, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x61, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x46, 0x72, 0x65, 0x71, 0x75, 0x65, 0x6e,
	0x63, 0x79, 0x12, 0x2c, 0x0a, 0x11, 0x61, 0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x19, 0x20, 0x03, 0x28, 0x03, 0x52, 0x11, 0x61,
	0x67, 0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x12, 0x20, 0x0a, 0x0b, 0x70, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x63, 0x65, 0x18,
	0x1a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x70, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x63, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x18,
	0x1c, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x6d, 0x6f, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x18, 0x1d, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x08, 0x6d, 0x6f, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x20, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x1e, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x1f, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x2f, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x20, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x17, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x12, 0x32, 0x0a, 0x08, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x28, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x63, 0x68, 0x69,
	0x6c, 0x64, 0x72, 0x65, 0x6e, 0x12, 0x3e, 0x0a, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d,
	0x65, 0x6e, 0x74, 0x73, 0x18, 0x29, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x75, 0x6e, 0x73,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x74,
	0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x61, 0x74, 0x74, 0x61, 0x63, 0x68,
	0x6d, 0x65, 0x6e, 0x74, 0x73, 0x1a, 0x43, 0x0a, 0x15, 0x45, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xcd, 0x01, 0x0a, 0x1b, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x44, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x6d, 0x61, 0x78, 0x4c, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65,
	0x6d, 0x61, 0x72, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x6d, 0x61,
	0x72, 0x6b, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x73, 0x79, 0x73, 0x74, 0x65,
	0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x22, 0x7a, 0x0a, 0x1a, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47,
	0x65, 0x74, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x22, 0x0a, 0x0c, 0x77, 0x69, 0x74, 0x68,
	0x43, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c,
	0x77, 0x69, 0x74, 0x68, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x72, 0x65, 0x6e, 0x12, 0x28, 0x0a, 0x0f,
	0x77, 0x69, 0x74, 0x68, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0f, 0x77, 0x69, 0x74, 0x68, 0x41, 0x74, 0x74, 0x61, 0x63,
	0x68, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x22, 0x37, 0x0a, 0x1b, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x72, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x22,
	0x60, 0x0a, 0x1c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05,
	0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x22, 0xae, 0x01, 0x0a, 0x1f, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x6f, 0x64, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x6e, 0x6f, 0x64, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65,
	0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x49, 0x44, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x49, 0x44, 0x22, 0x64, 0x0a, 0x20, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2a, 0x0a, 0x04,
	0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x75, 0x6e, 0x73,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x7b, 0x0a, 0x17, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x74, 0x74, 0x61, 0x63, 0x68, 0x6d,
	0x65, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x02, 0x69, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x66, 0x69, 0x6c, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0xfb, 0x01, 0x0a, 0x15, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x20, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x20, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x40, 0x0a, 0x0a, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0a, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x22, 0x58, 0x0a, 0x1f, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x68, 0x0a,
	0x20, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61,
	0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2e, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18,
	0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x7c, 0x0a, 0x12, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x55, 0x0a, 0x1c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x62, 0x0a, 0x1d,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e,
	0x66, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x2b, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74,
	0x22, 0x70, 0x0a, 0x14, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65,
	0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x44,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x22, 0x29, 0x0a, 0x13, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4a,
	0x73, 0x6f, 0x6e, 0x32, 0x66, 0x73, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x6a, 0x73, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6a, 0x73, 0x6f, 0x6e, 0x22, 0x46, 0x0a,
	0x14, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4a, 0x73, 0x6f, 0x6e, 0x32, 0x66,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4a, 0x73, 0x6f, 0x6e, 0x32, 0x66, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x68, 0x0a, 0x15, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x4a, 0x73, 0x6f, 0x6e, 0x32, 0x66, 0x73, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x50, 0x61, 0x74, 0x68, 0x12, 0x33, 0x0a, 0x06, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x75, 0x6e, 0x73,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4a, 0x73, 0x6f, 0x6e, 0x32, 0x66,
	0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x22,
	0x62, 0x0a, 0x16, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4a, 0x73, 0x6f, 0x6e,
	0x32, 0x66, 0x73, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x46, 0x69, 0x65, 0x6c, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x73, 0x79, 0x73, 0x74, 0x65, 0x6d, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x22, 0xa3, 0x02, 0x0a, 0x0f, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x0a,
	0x0a, 0x61, 0x75, 0x74, 0x68, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x0a, 0x61, 0x75, 0x74, 0x68, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x1a, 0x0a, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x63, 0x70, 0x75, 0x18, 0x08, 0x20, 0x01, 0x28, 0x02,
	0x52, 0x03, 0x63, 0x70, 0x75, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x02, 0x52, 0x06, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22, 0x4f, 0x0a, 0x16, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x0d, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x59, 0x0a, 0x17, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x28, 0x0a, 0x04, 0x6c,
	0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x75, 0x6e, 0x73, 0x2e,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x3e, 0x0a, 0x18, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x5e, 0x0a, 0x0c, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x12, 0x1c, 0x0a,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x53, 0x74, 0x61, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x74,
	0x69, 0x6d, 0x65, 0x45, 0x6e, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07, 0x74, 0x69,
	0x6d, 0x65, 0x45, 0x6e, 0x64, 0x22, 0x71, 0x0a, 0x0d, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x12, 0x38, 0x0a, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73,
	0x12, 0x26, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x74,
	0x65, 0x6d, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x47, 0x0a, 0x0d, 0x44, 0x61, 0x73, 0x68,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x49, 0x74, 0x65, 0x6d, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x2a, 0x4a, 0x0a, 0x0c, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x45, 0x6e, 0x75,
	0x6d, 0x12, 0x13, 0x0a, 0x0f, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x55, 0x6e, 0x6b,
	0x6e, 0x6f, 0x77, 0x6e, 0x10, 0x00, 0x12, 0x11, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x61, 0x54, 0x79,
	0x70, 0x65, 0x53, 0x74, 0x61, 0x74, 0x65, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x44, 0x61, 0x74,
	0x61, 0x54, 0x79, 0x70, 0x65, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x10, 0x02, 0x32, 0xe9, 0x0a,
	0x0a, 0x09, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x35, 0x0a, 0x0e, 0x6e,
	0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e,
	0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x0b, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x57, 0x69, 0x74, 0x68,
	0x49, 0x44, 0x12, 0x34, 0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x0a, 0x2e, 0x75,
	0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x0b, 0x2e, 0x75, 0x6e, 0x73,
	0x2e, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44, 0x1a, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x49, 0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47,
	0x65, 0x74, 0x4f, 0x6e, 0x65, 0x12, 0x1f, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74,
	0x4f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x56,
	0x0a, 0x0f, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x12, 0x20, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x21, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x62, 0x0a, 0x13, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x24, 0x2e,
	0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x46, 0x69, 0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x46, 0x69,
	0x6c, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x2e, 0x0a, 0x13, 0x6e, 0x6f,
	0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x55, 0x6e, 0x64,
	0x6f, 0x12, 0x0b, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44, 0x1a, 0x0a,
	0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x30, 0x0a, 0x15, 0x6e, 0x6f,
	0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x63, 0x79, 0x63, 0x6c, 0x65, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x12, 0x0b, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44,
	0x1a, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3d, 0x0a, 0x12,
	0x74, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x12, 0x1a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x0b,
	0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44, 0x12, 0x3c, 0x0a, 0x12, 0x74,
	0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x1a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x0a, 0x2e,
	0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x2d, 0x0a, 0x12, 0x74, 0x65, 0x6d,
	0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12,
	0x0b, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44, 0x1a, 0x0a, 0x2e, 0x75,
	0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3d, 0x0a, 0x12, 0x74, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x12, 0x0b,
	0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44, 0x1a, 0x1a, 0x2e, 0x75, 0x6e,
	0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x62, 0x0a, 0x13, 0x74, 0x65, 0x6d, 0x70, 0x6c,
	0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x24,
	0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x54, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x1a, 0x25, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x54, 0x65, 0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36, 0x0a, 0x0f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x17,
	0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x12, 0x36, 0x0a, 0x0f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x17, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x1a,
	0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x2a, 0x0a, 0x0f, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x0b,
	0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44, 0x1a, 0x0a, 0x2e, 0x75, 0x6e,
	0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x37, 0x0a, 0x0f, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x12, 0x0b, 0x2e, 0x75, 0x6e, 0x73,
	0x2e, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44, 0x1a, 0x17, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x59, 0x0a, 0x10, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74,
	0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x22, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x49, 0x6e, 0x66, 0x6f,
	0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3a, 0x0a, 0x11, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x12, 0x19, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x1a, 0x0a, 0x2e, 0x75, 0x6e,
	0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x2c, 0x0a, 0x11, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x4e, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x0b, 0x2e, 0x75,
	0x6e, 0x73, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44, 0x1a, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x3e, 0x0a, 0x07, 0x6a, 0x73, 0x6f, 0x6e, 0x32, 0x66, 0x73,
	0x12, 0x18, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x4a, 0x73, 0x6f, 0x6e, 0x32, 0x66, 0x73, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x75, 0x6e, 0x73,
	0x2e, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x4a, 0x73, 0x6f, 0x6e, 0x32, 0x66,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x09, 0x64, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x12, 0x11, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x44, 0x61, 0x73, 0x68, 0x62, 0x6f, 0x61,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x44, 0x61, 0x73, 0x68,
	0x62, 0x6f, 0x61, 0x72, 0x64, 0x52, 0x65, 0x73, 0x70, 0x32, 0xa2, 0x04, 0x0a, 0x0d, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x12, 0x33, 0x0a, 0x0e, 0x6e,
	0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x14, 0x2e,
	0x75, 0x6e, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x1a, 0x0b, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44,
	0x12, 0x32, 0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x12, 0x14, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x12, 0x29, 0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x0b, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x57, 0x69, 0x74,
	0x68, 0x49, 0x44, 0x1a, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x45, 0x0a, 0x0e, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4f, 0x6e,
	0x65, 0x12, 0x1d, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4e,
	0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x71,
	0x1a, 0x14, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4e, 0x6f,
	0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x4c, 0x0a, 0x0f, 0x6e, 0x6f, 0x64, 0x65, 0x49, 0x6e,
	0x66, 0x6f, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x1b, 0x2e, 0x75, 0x6e, 0x73, 0x2e,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c,
	0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x43, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x4e, 0x6f, 0x64, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x2b, 0x0a, 0x11, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x49,
	0x6e, 0x66, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x12, 0x2c, 0x0a, 0x12, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x66, 0x6f,
	0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x34, 0x0a, 0x1a, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4c, 0x6f, 0x67, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x0a, 0x2e,
	0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x28, 0x0a, 0x0e, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x53, 0x79, 0x6e, 0x63, 0x55, 0x6e, 0x73, 0x12, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x1a, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x2d, 0x0a, 0x13, 0x6d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x54, 0x6f, 0x45, 0x64, 0x67, 0x65, 0x12, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x0a, 0x2e, 0x75, 0x6e, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x08,
	0x5a, 0x06, 0x70, 0x62, 0x2f, 0x75, 0x6e, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_uns_proto_rawDescOnce sync.Once
	file_proto_uns_proto_rawDescData = file_proto_uns_proto_rawDesc
)

func file_proto_uns_proto_rawDescGZIP() []byte {
	file_proto_uns_proto_rawDescOnce.Do(func() {
		file_proto_uns_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_uns_proto_rawDescData)
	})
	return file_proto_uns_proto_rawDescData
}

var file_proto_uns_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_proto_uns_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_proto_uns_proto_goTypes = []interface{}{
	(DataTypeEnum)(0),                        // 0: uns.DataTypeEnum
	(*Request)(nil),                          // 1: uns.Request
	(*Response)(nil),                         // 2: uns.Response
	(*DateRange)(nil),                        // 3: uns.DateRange
	(*TimeRange)(nil),                        // 4: uns.TimeRange
	(*Empty)(nil),                            // 5: uns.Empty
	(*PageInfo)(nil),                         // 6: uns.PageInfo
	(*CompareString)(nil),                    // 7: uns.CompareString
	(*CompareInt64)(nil),                     // 8: uns.CompareInt64
	(*WithID)(nil),                           // 9: uns.WithID
	(*WithIDCode)(nil),                       // 10: uns.WithIDCode
	(*WithCode)(nil),                         // 11: uns.WithCode
	(*WithAppCodeID)(nil),                    // 12: uns.WithAppCodeID
	(*NamespaceNodeInfo)(nil),                // 13: uns.NamespaceNodeInfo
	(*NamespaceNodeInfoDefinition)(nil),      // 14: uns.NamespaceNodeInfoDefinition
	(*NamespaceNodeInfoGetOneReq)(nil),       // 15: uns.NamespaceNodeInfoGetOneReq
	(*NamespaceNodeInfoGetListReq)(nil),      // 16: uns.NamespaceNodeInfoGetListReq
	(*NamespaceNodeInfoGetListResp)(nil),     // 17: uns.NamespaceNodeInfoGetListResp
	(*NamespaceNodeInfoGetFileListReq)(nil),  // 18: uns.NamespaceNodeInfoGetFileListReq
	(*NamespaceNodeInfoGetFileListResp)(nil), // 19: uns.NamespaceNodeInfoGetFileListResp
	(*NamespaceNodeAttachment)(nil),          // 20: uns.NamespaceNodeAttachment
	(*NamespaceTemplateInfo)(nil),            // 21: uns.NamespaceTemplateInfo
	(*NamespaceTemplateInfoGetListReq)(nil),  // 22: uns.NamespaceTemplateInfoGetListReq
	(*NamespaceTemplateInfoGetListResp)(nil), // 23: uns.NamespaceTemplateInfoGetListResp
	(*NamespaceLabelInfo)(nil),               // 24: uns.NamespaceLabelInfo
	(*NamespaceLabelInfoGetListReq)(nil),     // 25: uns.NamespaceLabelInfoGetListReq
	(*NamespaceLabelInfoGetListResp)(nil),    // 26: uns.NamespaceLabelInfoGetListResp
	(*NamespaceLabelNodeID)(nil),             // 27: uns.NamespaceLabelNodeID
	(*NamespaceJson2FsReq)(nil),              // 28: uns.NamespaceJson2fsReq
	(*NamespaceJson2FsResp)(nil),             // 29: uns.NamespaceJson2fsResp
	(*NamespaceJson2FsItems)(nil),            // 30: uns.NamespaceJson2fsItems
	(*NamespaceJson2FsFields)(nil),           // 31: uns.NamespaceJson2fsFields
	(*ConnectNodeInfo)(nil),                  // 32: uns.ConnectNodeInfo
	(*ConnectNodeInfoListReq)(nil),           // 33: uns.ConnectNodeInfoListReq
	(*ConnectNodeInfoListResp)(nil),          // 34: uns.ConnectNodeInfoListResp
	(*ConnectNodeInfoGetOneReq)(nil),         // 35: uns.ConnectNodeInfoGetOneReq
	(*DashboardReq)(nil),                     // 36: uns.DashboardReq
	(*DashboardResp)(nil),                    // 37: uns.DashboardResp
	(*DashboardItem)(nil),                    // 38: uns.DashboardItem
	(*PageInfo_OrderBy)(nil),                 // 39: uns.PageInfo.OrderBy
	nil,                                      // 40: uns.NamespaceNodeInfo.ExtendPropertiesEntry
}
var file_proto_uns_proto_depIdxs = []int32{
	39, // 0: uns.PageInfo.orders:type_name -> uns.PageInfo.OrderBy
	40, // 1: uns.NamespaceNodeInfo.extendProperties:type_name -> uns.NamespaceNodeInfo.ExtendPropertiesEntry
	14, // 2: uns.NamespaceNodeInfo.definition:type_name -> uns.NamespaceNodeInfoDefinition
	0,  // 3: uns.NamespaceNodeInfo.dataType:type_name -> uns.DataTypeEnum
	24, // 4: uns.NamespaceNodeInfo.labels:type_name -> uns.NamespaceLabelInfo
	13, // 5: uns.NamespaceNodeInfo.children:type_name -> uns.NamespaceNodeInfo
	20, // 6: uns.NamespaceNodeInfo.attachments:type_name -> uns.NamespaceNodeAttachment
	13, // 7: uns.NamespaceNodeInfoGetListResp.list:type_name -> uns.NamespaceNodeInfo
	6,  // 8: uns.NamespaceNodeInfoGetFileListReq.page:type_name -> uns.PageInfo
	13, // 9: uns.NamespaceNodeInfoGetFileListResp.list:type_name -> uns.NamespaceNodeInfo
	14, // 10: uns.NamespaceTemplateInfo.definition:type_name -> uns.NamespaceNodeInfoDefinition
	6,  // 11: uns.NamespaceTemplateInfoGetListReq.page:type_name -> uns.PageInfo
	21, // 12: uns.NamespaceTemplateInfoGetListResp.list:type_name -> uns.NamespaceTemplateInfo
	6,  // 13: uns.NamespaceLabelInfoGetListReq.page:type_name -> uns.PageInfo
	24, // 14: uns.NamespaceLabelInfoGetListResp.list:type_name -> uns.NamespaceLabelInfo
	30, // 15: uns.NamespaceJson2fsResp.data:type_name -> uns.NamespaceJson2fsItems
	31, // 16: uns.NamespaceJson2fsItems.fields:type_name -> uns.NamespaceJson2fsFields
	6,  // 17: uns.ConnectNodeInfoListReq.page:type_name -> uns.PageInfo
	32, // 18: uns.ConnectNodeInfoListResp.list:type_name -> uns.ConnectNodeInfo
	14, // 19: uns.DashboardResp.fields:type_name -> uns.NamespaceNodeInfoDefinition
	38, // 20: uns.DashboardResp.list:type_name -> uns.DashboardItem
	13, // 21: uns.Namespace.nodeInfoCreate:input_type -> uns.NamespaceNodeInfo
	13, // 22: uns.Namespace.nodeInfoUpdate:input_type -> uns.NamespaceNodeInfo
	9,  // 23: uns.Namespace.nodeInfoDelete:input_type -> uns.WithID
	15, // 24: uns.Namespace.nodeInfoGetOne:input_type -> uns.NamespaceNodeInfoGetOneReq
	16, // 25: uns.Namespace.nodeInfoGetList:input_type -> uns.NamespaceNodeInfoGetListReq
	18, // 26: uns.Namespace.nodeInfoGetFileList:input_type -> uns.NamespaceNodeInfoGetFileListReq
	9,  // 27: uns.Namespace.nodeInfoRecycleUndo:input_type -> uns.WithID
	9,  // 28: uns.Namespace.nodeInfoRecycleDelete:input_type -> uns.WithID
	21, // 29: uns.Namespace.templateInfoCreate:input_type -> uns.NamespaceTemplateInfo
	21, // 30: uns.Namespace.templateInfoUpdate:input_type -> uns.NamespaceTemplateInfo
	9,  // 31: uns.Namespace.templateInfoDelete:input_type -> uns.WithID
	9,  // 32: uns.Namespace.templateInfoGetOne:input_type -> uns.WithID
	22, // 33: uns.Namespace.templateInfoGetList:input_type -> uns.NamespaceTemplateInfoGetListReq
	24, // 34: uns.Namespace.labelInfoCreate:input_type -> uns.NamespaceLabelInfo
	24, // 35: uns.Namespace.labelInfoUpdate:input_type -> uns.NamespaceLabelInfo
	9,  // 36: uns.Namespace.labelInfoDelete:input_type -> uns.WithID
	9,  // 37: uns.Namespace.labelInfoGetOne:input_type -> uns.WithID
	25, // 38: uns.Namespace.labelInfoGetList:input_type -> uns.NamespaceLabelInfoGetListReq
	27, // 39: uns.Namespace.labelNodeIDCreate:input_type -> uns.NamespaceLabelNodeID
	9,  // 40: uns.Namespace.labelNodeIDDelete:input_type -> uns.WithID
	28, // 41: uns.Namespace.json2fs:input_type -> uns.NamespaceJson2fsReq
	36, // 42: uns.Namespace.dashboard:input_type -> uns.DashboardReq
	32, // 43: uns.ConnectManage.nodeInfoCreate:input_type -> uns.ConnectNodeInfo
	32, // 44: uns.ConnectManage.nodeInfoUpdate:input_type -> uns.ConnectNodeInfo
	9,  // 45: uns.ConnectManage.nodeInfoDelete:input_type -> uns.WithID
	35, // 46: uns.ConnectManage.nodeInfoGetOne:input_type -> uns.ConnectNodeInfoGetOneReq
	33, // 47: uns.ConnectManage.nodeInfoGetList:input_type -> uns.ConnectNodeInfoListReq
	5,  // 48: uns.ConnectManage.mappingInfoUpdate:input_type -> uns.Empty
	5,  // 49: uns.ConnectManage.mappingInfoGetList:input_type -> uns.Empty
	5,  // 50: uns.ConnectManage.mappingOperationLogGetList:input_type -> uns.Empty
	5,  // 51: uns.ConnectManage.mappingSyncUns:input_type -> uns.Empty
	5,  // 52: uns.ConnectManage.mappingDeployToEdge:input_type -> uns.Empty
	9,  // 53: uns.Namespace.nodeInfoCreate:output_type -> uns.WithID
	5,  // 54: uns.Namespace.nodeInfoUpdate:output_type -> uns.Empty
	5,  // 55: uns.Namespace.nodeInfoDelete:output_type -> uns.Empty
	13, // 56: uns.Namespace.nodeInfoGetOne:output_type -> uns.NamespaceNodeInfo
	17, // 57: uns.Namespace.nodeInfoGetList:output_type -> uns.NamespaceNodeInfoGetListResp
	19, // 58: uns.Namespace.nodeInfoGetFileList:output_type -> uns.NamespaceNodeInfoGetFileListResp
	5,  // 59: uns.Namespace.nodeInfoRecycleUndo:output_type -> uns.Empty
	5,  // 60: uns.Namespace.nodeInfoRecycleDelete:output_type -> uns.Empty
	9,  // 61: uns.Namespace.templateInfoCreate:output_type -> uns.WithID
	5,  // 62: uns.Namespace.templateInfoUpdate:output_type -> uns.Empty
	5,  // 63: uns.Namespace.templateInfoDelete:output_type -> uns.Empty
	21, // 64: uns.Namespace.templateInfoGetOne:output_type -> uns.NamespaceTemplateInfo
	23, // 65: uns.Namespace.templateInfoGetList:output_type -> uns.NamespaceTemplateInfoGetListResp
	5,  // 66: uns.Namespace.labelInfoCreate:output_type -> uns.Empty
	5,  // 67: uns.Namespace.labelInfoUpdate:output_type -> uns.Empty
	5,  // 68: uns.Namespace.labelInfoDelete:output_type -> uns.Empty
	24, // 69: uns.Namespace.labelInfoGetOne:output_type -> uns.NamespaceLabelInfo
	26, // 70: uns.Namespace.labelInfoGetList:output_type -> uns.NamespaceLabelInfoGetListResp
	5,  // 71: uns.Namespace.labelNodeIDCreate:output_type -> uns.Empty
	5,  // 72: uns.Namespace.labelNodeIDDelete:output_type -> uns.Empty
	29, // 73: uns.Namespace.json2fs:output_type -> uns.NamespaceJson2fsResp
	37, // 74: uns.Namespace.dashboard:output_type -> uns.DashboardResp
	9,  // 75: uns.ConnectManage.nodeInfoCreate:output_type -> uns.WithID
	5,  // 76: uns.ConnectManage.nodeInfoUpdate:output_type -> uns.Empty
	5,  // 77: uns.ConnectManage.nodeInfoDelete:output_type -> uns.Empty
	32, // 78: uns.ConnectManage.nodeInfoGetOne:output_type -> uns.ConnectNodeInfo
	34, // 79: uns.ConnectManage.nodeInfoGetList:output_type -> uns.ConnectNodeInfoListResp
	5,  // 80: uns.ConnectManage.mappingInfoUpdate:output_type -> uns.Empty
	5,  // 81: uns.ConnectManage.mappingInfoGetList:output_type -> uns.Empty
	5,  // 82: uns.ConnectManage.mappingOperationLogGetList:output_type -> uns.Empty
	5,  // 83: uns.ConnectManage.mappingSyncUns:output_type -> uns.Empty
	5,  // 84: uns.ConnectManage.mappingDeployToEdge:output_type -> uns.Empty
	53, // [53:85] is the sub-list for method output_type
	21, // [21:53] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_proto_uns_proto_init() }
func file_proto_uns_proto_init() {
	if File_proto_uns_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_uns_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Request); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompareString); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompareInt64); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithIDCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithAppCodeID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceNodeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceNodeInfoDefinition); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceNodeInfoGetOneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceNodeInfoGetListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceNodeInfoGetListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceNodeInfoGetFileListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceNodeInfoGetFileListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceNodeAttachment); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceTemplateInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceTemplateInfoGetListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceTemplateInfoGetListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceLabelInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceLabelInfoGetListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceLabelInfoGetListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceLabelNodeID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceJson2FsReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceJson2FsResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceJson2FsItems); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceJson2FsFields); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectNodeInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectNodeInfoListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectNodeInfoListResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ConnectNodeInfoGetOneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DashboardItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_uns_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageInfo_OrderBy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_uns_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   40,
			NumExtensions: 0,
			NumServices:   2,
		},
		GoTypes:           file_proto_uns_proto_goTypes,
		DependencyIndexes: file_proto_uns_proto_depIdxs,
		EnumInfos:         file_proto_uns_proto_enumTypes,
		MessageInfos:      file_proto_uns_proto_msgTypes,
	}.Build()
	File_proto_uns_proto = out.File
	file_proto_uns_proto_rawDesc = nil
	file_proto_uns_proto_goTypes = nil
	file_proto_uns_proto_depIdxs = nil
}
