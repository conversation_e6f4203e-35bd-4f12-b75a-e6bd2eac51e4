// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: proto/uns.proto

package uns

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Namespace_NodeInfoCreate_FullMethodName        = "/uns.Namespace/nodeInfoCreate"
	Namespace_NodeInfoUpdate_FullMethodName        = "/uns.Namespace/nodeInfoUpdate"
	Namespace_NodeInfoDelete_FullMethodName        = "/uns.Namespace/nodeInfoDelete"
	Namespace_NodeInfoGetOne_FullMethodName        = "/uns.Namespace/nodeInfoGetOne"
	Namespace_NodeInfoGetList_FullMethodName       = "/uns.Namespace/nodeInfoGetList"
	Namespace_NodeInfoGetFileList_FullMethodName   = "/uns.Namespace/nodeInfoGetFileList"
	Namespace_NodeInfoRecycleUndo_FullMethodName   = "/uns.Namespace/nodeInfoRecycleUndo"
	Namespace_NodeInfoRecycleDelete_FullMethodName = "/uns.Namespace/nodeInfoRecycleDelete"
	Namespace_TemplateInfoCreate_FullMethodName    = "/uns.Namespace/templateInfoCreate"
	Namespace_TemplateInfoUpdate_FullMethodName    = "/uns.Namespace/templateInfoUpdate"
	Namespace_TemplateInfoDelete_FullMethodName    = "/uns.Namespace/templateInfoDelete"
	Namespace_TemplateInfoGetOne_FullMethodName    = "/uns.Namespace/templateInfoGetOne"
	Namespace_TemplateInfoGetList_FullMethodName   = "/uns.Namespace/templateInfoGetList"
	Namespace_LabelInfoCreate_FullMethodName       = "/uns.Namespace/labelInfoCreate"
	Namespace_LabelInfoUpdate_FullMethodName       = "/uns.Namespace/labelInfoUpdate"
	Namespace_LabelInfoDelete_FullMethodName       = "/uns.Namespace/labelInfoDelete"
	Namespace_LabelInfoGetOne_FullMethodName       = "/uns.Namespace/labelInfoGetOne"
	Namespace_LabelInfoGetList_FullMethodName      = "/uns.Namespace/labelInfoGetList"
	Namespace_LabelNodeIDCreate_FullMethodName     = "/uns.Namespace/labelNodeIDCreate"
	Namespace_LabelNodeIDDelete_FullMethodName     = "/uns.Namespace/labelNodeIDDelete"
	Namespace_Json2Fs_FullMethodName               = "/uns.Namespace/json2fs"
	Namespace_Dashboard_FullMethodName             = "/uns.Namespace/dashboard"
)

// NamespaceClient is the client API for Namespace service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NamespaceClient interface {
	NodeInfoCreate(ctx context.Context, in *NamespaceNodeInfo, opts ...grpc.CallOption) (*WithID, error)
	NodeInfoUpdate(ctx context.Context, in *NamespaceNodeInfo, opts ...grpc.CallOption) (*Empty, error)
	NodeInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
	NodeInfoGetOne(ctx context.Context, in *NamespaceNodeInfoGetOneReq, opts ...grpc.CallOption) (*NamespaceNodeInfo, error)
	NodeInfoGetList(ctx context.Context, in *NamespaceNodeInfoGetListReq, opts ...grpc.CallOption) (*NamespaceNodeInfoGetListResp, error)
	// 获取指定命名空间下的文件列表
	NodeInfoGetFileList(ctx context.Context, in *NamespaceNodeInfoGetFileListReq, opts ...grpc.CallOption) (*NamespaceNodeInfoGetFileListResp, error)
	// 回收站撤销
	NodeInfoRecycleUndo(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
	// 回收站删除
	NodeInfoRecycleDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
	TemplateInfoCreate(ctx context.Context, in *NamespaceTemplateInfo, opts ...grpc.CallOption) (*WithID, error)
	TemplateInfoUpdate(ctx context.Context, in *NamespaceTemplateInfo, opts ...grpc.CallOption) (*Empty, error)
	TemplateInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
	TemplateInfoGetOne(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*NamespaceTemplateInfo, error)
	TemplateInfoGetList(ctx context.Context, in *NamespaceTemplateInfoGetListReq, opts ...grpc.CallOption) (*NamespaceTemplateInfoGetListResp, error)
	LabelInfoCreate(ctx context.Context, in *NamespaceLabelInfo, opts ...grpc.CallOption) (*Empty, error)
	LabelInfoUpdate(ctx context.Context, in *NamespaceLabelInfo, opts ...grpc.CallOption) (*Empty, error)
	LabelInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
	LabelInfoGetOne(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*NamespaceLabelInfo, error)
	LabelInfoGetList(ctx context.Context, in *NamespaceLabelInfoGetListReq, opts ...grpc.CallOption) (*NamespaceLabelInfoGetListResp, error)
	LabelNodeIDCreate(ctx context.Context, in *NamespaceLabelNodeID, opts ...grpc.CallOption) (*Empty, error)
	// 已弃用,用文件列表传labelid方式获取
	// rpc labelNodeIDGetList(NamespaceLabelNodeIDGetListReq) returns(NamespaceLabelNodeIDGetListResp);
	LabelNodeIDDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
	// json转definition
	Json2Fs(ctx context.Context, in *NamespaceJson2FsReq, opts ...grpc.CallOption) (*NamespaceJson2FsResp, error)
	// dashboard
	Dashboard(ctx context.Context, in *DashboardReq, opts ...grpc.CallOption) (*DashboardResp, error)
}

type namespaceClient struct {
	cc grpc.ClientConnInterface
}

func NewNamespaceClient(cc grpc.ClientConnInterface) NamespaceClient {
	return &namespaceClient{cc}
}

func (c *namespaceClient) NodeInfoCreate(ctx context.Context, in *NamespaceNodeInfo, opts ...grpc.CallOption) (*WithID, error) {
	out := new(WithID)
	err := c.cc.Invoke(ctx, Namespace_NodeInfoCreate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) NodeInfoUpdate(ctx context.Context, in *NamespaceNodeInfo, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Namespace_NodeInfoUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) NodeInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Namespace_NodeInfoDelete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) NodeInfoGetOne(ctx context.Context, in *NamespaceNodeInfoGetOneReq, opts ...grpc.CallOption) (*NamespaceNodeInfo, error) {
	out := new(NamespaceNodeInfo)
	err := c.cc.Invoke(ctx, Namespace_NodeInfoGetOne_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) NodeInfoGetList(ctx context.Context, in *NamespaceNodeInfoGetListReq, opts ...grpc.CallOption) (*NamespaceNodeInfoGetListResp, error) {
	out := new(NamespaceNodeInfoGetListResp)
	err := c.cc.Invoke(ctx, Namespace_NodeInfoGetList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) NodeInfoGetFileList(ctx context.Context, in *NamespaceNodeInfoGetFileListReq, opts ...grpc.CallOption) (*NamespaceNodeInfoGetFileListResp, error) {
	out := new(NamespaceNodeInfoGetFileListResp)
	err := c.cc.Invoke(ctx, Namespace_NodeInfoGetFileList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) NodeInfoRecycleUndo(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Namespace_NodeInfoRecycleUndo_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) NodeInfoRecycleDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Namespace_NodeInfoRecycleDelete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) TemplateInfoCreate(ctx context.Context, in *NamespaceTemplateInfo, opts ...grpc.CallOption) (*WithID, error) {
	out := new(WithID)
	err := c.cc.Invoke(ctx, Namespace_TemplateInfoCreate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) TemplateInfoUpdate(ctx context.Context, in *NamespaceTemplateInfo, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Namespace_TemplateInfoUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) TemplateInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Namespace_TemplateInfoDelete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) TemplateInfoGetOne(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*NamespaceTemplateInfo, error) {
	out := new(NamespaceTemplateInfo)
	err := c.cc.Invoke(ctx, Namespace_TemplateInfoGetOne_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) TemplateInfoGetList(ctx context.Context, in *NamespaceTemplateInfoGetListReq, opts ...grpc.CallOption) (*NamespaceTemplateInfoGetListResp, error) {
	out := new(NamespaceTemplateInfoGetListResp)
	err := c.cc.Invoke(ctx, Namespace_TemplateInfoGetList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) LabelInfoCreate(ctx context.Context, in *NamespaceLabelInfo, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Namespace_LabelInfoCreate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) LabelInfoUpdate(ctx context.Context, in *NamespaceLabelInfo, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Namespace_LabelInfoUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) LabelInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Namespace_LabelInfoDelete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) LabelInfoGetOne(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*NamespaceLabelInfo, error) {
	out := new(NamespaceLabelInfo)
	err := c.cc.Invoke(ctx, Namespace_LabelInfoGetOne_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) LabelInfoGetList(ctx context.Context, in *NamespaceLabelInfoGetListReq, opts ...grpc.CallOption) (*NamespaceLabelInfoGetListResp, error) {
	out := new(NamespaceLabelInfoGetListResp)
	err := c.cc.Invoke(ctx, Namespace_LabelInfoGetList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) LabelNodeIDCreate(ctx context.Context, in *NamespaceLabelNodeID, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Namespace_LabelNodeIDCreate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) LabelNodeIDDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Namespace_LabelNodeIDDelete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) Json2Fs(ctx context.Context, in *NamespaceJson2FsReq, opts ...grpc.CallOption) (*NamespaceJson2FsResp, error) {
	out := new(NamespaceJson2FsResp)
	err := c.cc.Invoke(ctx, Namespace_Json2Fs_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *namespaceClient) Dashboard(ctx context.Context, in *DashboardReq, opts ...grpc.CallOption) (*DashboardResp, error) {
	out := new(DashboardResp)
	err := c.cc.Invoke(ctx, Namespace_Dashboard_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NamespaceServer is the server API for Namespace service.
// All implementations must embed UnimplementedNamespaceServer
// for forward compatibility
type NamespaceServer interface {
	NodeInfoCreate(context.Context, *NamespaceNodeInfo) (*WithID, error)
	NodeInfoUpdate(context.Context, *NamespaceNodeInfo) (*Empty, error)
	NodeInfoDelete(context.Context, *WithID) (*Empty, error)
	NodeInfoGetOne(context.Context, *NamespaceNodeInfoGetOneReq) (*NamespaceNodeInfo, error)
	NodeInfoGetList(context.Context, *NamespaceNodeInfoGetListReq) (*NamespaceNodeInfoGetListResp, error)
	// 获取指定命名空间下的文件列表
	NodeInfoGetFileList(context.Context, *NamespaceNodeInfoGetFileListReq) (*NamespaceNodeInfoGetFileListResp, error)
	// 回收站撤销
	NodeInfoRecycleUndo(context.Context, *WithID) (*Empty, error)
	// 回收站删除
	NodeInfoRecycleDelete(context.Context, *WithID) (*Empty, error)
	TemplateInfoCreate(context.Context, *NamespaceTemplateInfo) (*WithID, error)
	TemplateInfoUpdate(context.Context, *NamespaceTemplateInfo) (*Empty, error)
	TemplateInfoDelete(context.Context, *WithID) (*Empty, error)
	TemplateInfoGetOne(context.Context, *WithID) (*NamespaceTemplateInfo, error)
	TemplateInfoGetList(context.Context, *NamespaceTemplateInfoGetListReq) (*NamespaceTemplateInfoGetListResp, error)
	LabelInfoCreate(context.Context, *NamespaceLabelInfo) (*Empty, error)
	LabelInfoUpdate(context.Context, *NamespaceLabelInfo) (*Empty, error)
	LabelInfoDelete(context.Context, *WithID) (*Empty, error)
	LabelInfoGetOne(context.Context, *WithID) (*NamespaceLabelInfo, error)
	LabelInfoGetList(context.Context, *NamespaceLabelInfoGetListReq) (*NamespaceLabelInfoGetListResp, error)
	LabelNodeIDCreate(context.Context, *NamespaceLabelNodeID) (*Empty, error)
	// 已弃用,用文件列表传labelid方式获取
	// rpc labelNodeIDGetList(NamespaceLabelNodeIDGetListReq) returns(NamespaceLabelNodeIDGetListResp);
	LabelNodeIDDelete(context.Context, *WithID) (*Empty, error)
	// json转definition
	Json2Fs(context.Context, *NamespaceJson2FsReq) (*NamespaceJson2FsResp, error)
	// dashboard
	Dashboard(context.Context, *DashboardReq) (*DashboardResp, error)
	mustEmbedUnimplementedNamespaceServer()
}

// UnimplementedNamespaceServer must be embedded to have forward compatible implementations.
type UnimplementedNamespaceServer struct {
}

func (UnimplementedNamespaceServer) NodeInfoCreate(context.Context, *NamespaceNodeInfo) (*WithID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoCreate not implemented")
}
func (UnimplementedNamespaceServer) NodeInfoUpdate(context.Context, *NamespaceNodeInfo) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoUpdate not implemented")
}
func (UnimplementedNamespaceServer) NodeInfoDelete(context.Context, *WithID) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoDelete not implemented")
}
func (UnimplementedNamespaceServer) NodeInfoGetOne(context.Context, *NamespaceNodeInfoGetOneReq) (*NamespaceNodeInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoGetOne not implemented")
}
func (UnimplementedNamespaceServer) NodeInfoGetList(context.Context, *NamespaceNodeInfoGetListReq) (*NamespaceNodeInfoGetListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoGetList not implemented")
}
func (UnimplementedNamespaceServer) NodeInfoGetFileList(context.Context, *NamespaceNodeInfoGetFileListReq) (*NamespaceNodeInfoGetFileListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoGetFileList not implemented")
}
func (UnimplementedNamespaceServer) NodeInfoRecycleUndo(context.Context, *WithID) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoRecycleUndo not implemented")
}
func (UnimplementedNamespaceServer) NodeInfoRecycleDelete(context.Context, *WithID) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoRecycleDelete not implemented")
}
func (UnimplementedNamespaceServer) TemplateInfoCreate(context.Context, *NamespaceTemplateInfo) (*WithID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TemplateInfoCreate not implemented")
}
func (UnimplementedNamespaceServer) TemplateInfoUpdate(context.Context, *NamespaceTemplateInfo) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TemplateInfoUpdate not implemented")
}
func (UnimplementedNamespaceServer) TemplateInfoDelete(context.Context, *WithID) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TemplateInfoDelete not implemented")
}
func (UnimplementedNamespaceServer) TemplateInfoGetOne(context.Context, *WithID) (*NamespaceTemplateInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TemplateInfoGetOne not implemented")
}
func (UnimplementedNamespaceServer) TemplateInfoGetList(context.Context, *NamespaceTemplateInfoGetListReq) (*NamespaceTemplateInfoGetListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TemplateInfoGetList not implemented")
}
func (UnimplementedNamespaceServer) LabelInfoCreate(context.Context, *NamespaceLabelInfo) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelInfoCreate not implemented")
}
func (UnimplementedNamespaceServer) LabelInfoUpdate(context.Context, *NamespaceLabelInfo) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelInfoUpdate not implemented")
}
func (UnimplementedNamespaceServer) LabelInfoDelete(context.Context, *WithID) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelInfoDelete not implemented")
}
func (UnimplementedNamespaceServer) LabelInfoGetOne(context.Context, *WithID) (*NamespaceLabelInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelInfoGetOne not implemented")
}
func (UnimplementedNamespaceServer) LabelInfoGetList(context.Context, *NamespaceLabelInfoGetListReq) (*NamespaceLabelInfoGetListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelInfoGetList not implemented")
}
func (UnimplementedNamespaceServer) LabelNodeIDCreate(context.Context, *NamespaceLabelNodeID) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelNodeIDCreate not implemented")
}
func (UnimplementedNamespaceServer) LabelNodeIDDelete(context.Context, *WithID) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelNodeIDDelete not implemented")
}
func (UnimplementedNamespaceServer) Json2Fs(context.Context, *NamespaceJson2FsReq) (*NamespaceJson2FsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Json2Fs not implemented")
}
func (UnimplementedNamespaceServer) Dashboard(context.Context, *DashboardReq) (*DashboardResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Dashboard not implemented")
}
func (UnimplementedNamespaceServer) mustEmbedUnimplementedNamespaceServer() {}

// UnsafeNamespaceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NamespaceServer will
// result in compilation errors.
type UnsafeNamespaceServer interface {
	mustEmbedUnimplementedNamespaceServer()
}

func RegisterNamespaceServer(s grpc.ServiceRegistrar, srv NamespaceServer) {
	s.RegisterService(&Namespace_ServiceDesc, srv)
}

func _Namespace_NodeInfoCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceNodeInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).NodeInfoCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_NodeInfoCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).NodeInfoCreate(ctx, req.(*NamespaceNodeInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_NodeInfoUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceNodeInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).NodeInfoUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_NodeInfoUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).NodeInfoUpdate(ctx, req.(*NamespaceNodeInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_NodeInfoDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).NodeInfoDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_NodeInfoDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).NodeInfoDelete(ctx, req.(*WithID))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_NodeInfoGetOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceNodeInfoGetOneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).NodeInfoGetOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_NodeInfoGetOne_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).NodeInfoGetOne(ctx, req.(*NamespaceNodeInfoGetOneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_NodeInfoGetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceNodeInfoGetListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).NodeInfoGetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_NodeInfoGetList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).NodeInfoGetList(ctx, req.(*NamespaceNodeInfoGetListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_NodeInfoGetFileList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceNodeInfoGetFileListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).NodeInfoGetFileList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_NodeInfoGetFileList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).NodeInfoGetFileList(ctx, req.(*NamespaceNodeInfoGetFileListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_NodeInfoRecycleUndo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).NodeInfoRecycleUndo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_NodeInfoRecycleUndo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).NodeInfoRecycleUndo(ctx, req.(*WithID))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_NodeInfoRecycleDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).NodeInfoRecycleDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_NodeInfoRecycleDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).NodeInfoRecycleDelete(ctx, req.(*WithID))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_TemplateInfoCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceTemplateInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).TemplateInfoCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_TemplateInfoCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).TemplateInfoCreate(ctx, req.(*NamespaceTemplateInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_TemplateInfoUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceTemplateInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).TemplateInfoUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_TemplateInfoUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).TemplateInfoUpdate(ctx, req.(*NamespaceTemplateInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_TemplateInfoDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).TemplateInfoDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_TemplateInfoDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).TemplateInfoDelete(ctx, req.(*WithID))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_TemplateInfoGetOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).TemplateInfoGetOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_TemplateInfoGetOne_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).TemplateInfoGetOne(ctx, req.(*WithID))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_TemplateInfoGetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceTemplateInfoGetListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).TemplateInfoGetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_TemplateInfoGetList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).TemplateInfoGetList(ctx, req.(*NamespaceTemplateInfoGetListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_LabelInfoCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceLabelInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).LabelInfoCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_LabelInfoCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).LabelInfoCreate(ctx, req.(*NamespaceLabelInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_LabelInfoUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceLabelInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).LabelInfoUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_LabelInfoUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).LabelInfoUpdate(ctx, req.(*NamespaceLabelInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_LabelInfoDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).LabelInfoDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_LabelInfoDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).LabelInfoDelete(ctx, req.(*WithID))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_LabelInfoGetOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).LabelInfoGetOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_LabelInfoGetOne_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).LabelInfoGetOne(ctx, req.(*WithID))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_LabelInfoGetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceLabelInfoGetListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).LabelInfoGetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_LabelInfoGetList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).LabelInfoGetList(ctx, req.(*NamespaceLabelInfoGetListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_LabelNodeIDCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceLabelNodeID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).LabelNodeIDCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_LabelNodeIDCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).LabelNodeIDCreate(ctx, req.(*NamespaceLabelNodeID))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_LabelNodeIDDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).LabelNodeIDDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_LabelNodeIDDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).LabelNodeIDDelete(ctx, req.(*WithID))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_Json2Fs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NamespaceJson2FsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).Json2Fs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_Json2Fs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).Json2Fs(ctx, req.(*NamespaceJson2FsReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Namespace_Dashboard_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DashboardReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NamespaceServer).Dashboard(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Namespace_Dashboard_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NamespaceServer).Dashboard(ctx, req.(*DashboardReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Namespace_ServiceDesc is the grpc.ServiceDesc for Namespace service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Namespace_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "uns.Namespace",
	HandlerType: (*NamespaceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "nodeInfoCreate",
			Handler:    _Namespace_NodeInfoCreate_Handler,
		},
		{
			MethodName: "nodeInfoUpdate",
			Handler:    _Namespace_NodeInfoUpdate_Handler,
		},
		{
			MethodName: "nodeInfoDelete",
			Handler:    _Namespace_NodeInfoDelete_Handler,
		},
		{
			MethodName: "nodeInfoGetOne",
			Handler:    _Namespace_NodeInfoGetOne_Handler,
		},
		{
			MethodName: "nodeInfoGetList",
			Handler:    _Namespace_NodeInfoGetList_Handler,
		},
		{
			MethodName: "nodeInfoGetFileList",
			Handler:    _Namespace_NodeInfoGetFileList_Handler,
		},
		{
			MethodName: "nodeInfoRecycleUndo",
			Handler:    _Namespace_NodeInfoRecycleUndo_Handler,
		},
		{
			MethodName: "nodeInfoRecycleDelete",
			Handler:    _Namespace_NodeInfoRecycleDelete_Handler,
		},
		{
			MethodName: "templateInfoCreate",
			Handler:    _Namespace_TemplateInfoCreate_Handler,
		},
		{
			MethodName: "templateInfoUpdate",
			Handler:    _Namespace_TemplateInfoUpdate_Handler,
		},
		{
			MethodName: "templateInfoDelete",
			Handler:    _Namespace_TemplateInfoDelete_Handler,
		},
		{
			MethodName: "templateInfoGetOne",
			Handler:    _Namespace_TemplateInfoGetOne_Handler,
		},
		{
			MethodName: "templateInfoGetList",
			Handler:    _Namespace_TemplateInfoGetList_Handler,
		},
		{
			MethodName: "labelInfoCreate",
			Handler:    _Namespace_LabelInfoCreate_Handler,
		},
		{
			MethodName: "labelInfoUpdate",
			Handler:    _Namespace_LabelInfoUpdate_Handler,
		},
		{
			MethodName: "labelInfoDelete",
			Handler:    _Namespace_LabelInfoDelete_Handler,
		},
		{
			MethodName: "labelInfoGetOne",
			Handler:    _Namespace_LabelInfoGetOne_Handler,
		},
		{
			MethodName: "labelInfoGetList",
			Handler:    _Namespace_LabelInfoGetList_Handler,
		},
		{
			MethodName: "labelNodeIDCreate",
			Handler:    _Namespace_LabelNodeIDCreate_Handler,
		},
		{
			MethodName: "labelNodeIDDelete",
			Handler:    _Namespace_LabelNodeIDDelete_Handler,
		},
		{
			MethodName: "json2fs",
			Handler:    _Namespace_Json2Fs_Handler,
		},
		{
			MethodName: "dashboard",
			Handler:    _Namespace_Dashboard_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/uns.proto",
}

const (
	ConnectManage_NodeInfoCreate_FullMethodName             = "/uns.ConnectManage/nodeInfoCreate"
	ConnectManage_NodeInfoUpdate_FullMethodName             = "/uns.ConnectManage/nodeInfoUpdate"
	ConnectManage_NodeInfoDelete_FullMethodName             = "/uns.ConnectManage/nodeInfoDelete"
	ConnectManage_NodeInfoGetOne_FullMethodName             = "/uns.ConnectManage/nodeInfoGetOne"
	ConnectManage_NodeInfoGetList_FullMethodName            = "/uns.ConnectManage/nodeInfoGetList"
	ConnectManage_MappingInfoUpdate_FullMethodName          = "/uns.ConnectManage/mappingInfoUpdate"
	ConnectManage_MappingInfoGetList_FullMethodName         = "/uns.ConnectManage/mappingInfoGetList"
	ConnectManage_MappingOperationLogGetList_FullMethodName = "/uns.ConnectManage/mappingOperationLogGetList"
	ConnectManage_MappingSyncUns_FullMethodName             = "/uns.ConnectManage/mappingSyncUns"
	ConnectManage_MappingDeployToEdge_FullMethodName        = "/uns.ConnectManage/mappingDeployToEdge"
)

// ConnectManageClient is the client API for ConnectManage service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ConnectManageClient interface {
	NodeInfoCreate(ctx context.Context, in *ConnectNodeInfo, opts ...grpc.CallOption) (*WithID, error)
	NodeInfoUpdate(ctx context.Context, in *ConnectNodeInfo, opts ...grpc.CallOption) (*Empty, error)
	NodeInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error)
	NodeInfoGetOne(ctx context.Context, in *ConnectNodeInfoGetOneReq, opts ...grpc.CallOption) (*ConnectNodeInfo, error)
	NodeInfoGetList(ctx context.Context, in *ConnectNodeInfoListReq, opts ...grpc.CallOption) (*ConnectNodeInfoListResp, error)
	// 先不做
	// 更新连接映射信息,节点创建的时候后端需要自动创建对应的表,节点删除的时候也需要删除映射信息
	MappingInfoUpdate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	// 全部挂载在根节点1下面
	MappingInfoGetList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	// 映射操作日志获取
	MappingOperationLogGetList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	// 从边缘节点同步节点
	MappingSyncUns(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	// 部署到边缘节点
	MappingDeployToEdge(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
}

type connectManageClient struct {
	cc grpc.ClientConnInterface
}

func NewConnectManageClient(cc grpc.ClientConnInterface) ConnectManageClient {
	return &connectManageClient{cc}
}

func (c *connectManageClient) NodeInfoCreate(ctx context.Context, in *ConnectNodeInfo, opts ...grpc.CallOption) (*WithID, error) {
	out := new(WithID)
	err := c.cc.Invoke(ctx, ConnectManage_NodeInfoCreate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectManageClient) NodeInfoUpdate(ctx context.Context, in *ConnectNodeInfo, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ConnectManage_NodeInfoUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectManageClient) NodeInfoDelete(ctx context.Context, in *WithID, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ConnectManage_NodeInfoDelete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectManageClient) NodeInfoGetOne(ctx context.Context, in *ConnectNodeInfoGetOneReq, opts ...grpc.CallOption) (*ConnectNodeInfo, error) {
	out := new(ConnectNodeInfo)
	err := c.cc.Invoke(ctx, ConnectManage_NodeInfoGetOne_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectManageClient) NodeInfoGetList(ctx context.Context, in *ConnectNodeInfoListReq, opts ...grpc.CallOption) (*ConnectNodeInfoListResp, error) {
	out := new(ConnectNodeInfoListResp)
	err := c.cc.Invoke(ctx, ConnectManage_NodeInfoGetList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectManageClient) MappingInfoUpdate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ConnectManage_MappingInfoUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectManageClient) MappingInfoGetList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ConnectManage_MappingInfoGetList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectManageClient) MappingOperationLogGetList(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ConnectManage_MappingOperationLogGetList_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectManageClient) MappingSyncUns(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ConnectManage_MappingSyncUns_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *connectManageClient) MappingDeployToEdge(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, ConnectManage_MappingDeployToEdge_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ConnectManageServer is the server API for ConnectManage service.
// All implementations must embed UnimplementedConnectManageServer
// for forward compatibility
type ConnectManageServer interface {
	NodeInfoCreate(context.Context, *ConnectNodeInfo) (*WithID, error)
	NodeInfoUpdate(context.Context, *ConnectNodeInfo) (*Empty, error)
	NodeInfoDelete(context.Context, *WithID) (*Empty, error)
	NodeInfoGetOne(context.Context, *ConnectNodeInfoGetOneReq) (*ConnectNodeInfo, error)
	NodeInfoGetList(context.Context, *ConnectNodeInfoListReq) (*ConnectNodeInfoListResp, error)
	// 先不做
	// 更新连接映射信息,节点创建的时候后端需要自动创建对应的表,节点删除的时候也需要删除映射信息
	MappingInfoUpdate(context.Context, *Empty) (*Empty, error)
	// 全部挂载在根节点1下面
	MappingInfoGetList(context.Context, *Empty) (*Empty, error)
	// 映射操作日志获取
	MappingOperationLogGetList(context.Context, *Empty) (*Empty, error)
	// 从边缘节点同步节点
	MappingSyncUns(context.Context, *Empty) (*Empty, error)
	// 部署到边缘节点
	MappingDeployToEdge(context.Context, *Empty) (*Empty, error)
	mustEmbedUnimplementedConnectManageServer()
}

// UnimplementedConnectManageServer must be embedded to have forward compatible implementations.
type UnimplementedConnectManageServer struct {
}

func (UnimplementedConnectManageServer) NodeInfoCreate(context.Context, *ConnectNodeInfo) (*WithID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoCreate not implemented")
}
func (UnimplementedConnectManageServer) NodeInfoUpdate(context.Context, *ConnectNodeInfo) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoUpdate not implemented")
}
func (UnimplementedConnectManageServer) NodeInfoDelete(context.Context, *WithID) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoDelete not implemented")
}
func (UnimplementedConnectManageServer) NodeInfoGetOne(context.Context, *ConnectNodeInfoGetOneReq) (*ConnectNodeInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoGetOne not implemented")
}
func (UnimplementedConnectManageServer) NodeInfoGetList(context.Context, *ConnectNodeInfoListReq) (*ConnectNodeInfoListResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NodeInfoGetList not implemented")
}
func (UnimplementedConnectManageServer) MappingInfoUpdate(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MappingInfoUpdate not implemented")
}
func (UnimplementedConnectManageServer) MappingInfoGetList(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MappingInfoGetList not implemented")
}
func (UnimplementedConnectManageServer) MappingOperationLogGetList(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MappingOperationLogGetList not implemented")
}
func (UnimplementedConnectManageServer) MappingSyncUns(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MappingSyncUns not implemented")
}
func (UnimplementedConnectManageServer) MappingDeployToEdge(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MappingDeployToEdge not implemented")
}
func (UnimplementedConnectManageServer) mustEmbedUnimplementedConnectManageServer() {}

// UnsafeConnectManageServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ConnectManageServer will
// result in compilation errors.
type UnsafeConnectManageServer interface {
	mustEmbedUnimplementedConnectManageServer()
}

func RegisterConnectManageServer(s grpc.ServiceRegistrar, srv ConnectManageServer) {
	s.RegisterService(&ConnectManage_ServiceDesc, srv)
}

func _ConnectManage_NodeInfoCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectNodeInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectManageServer).NodeInfoCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectManage_NodeInfoCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectManageServer).NodeInfoCreate(ctx, req.(*ConnectNodeInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConnectManage_NodeInfoUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectNodeInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectManageServer).NodeInfoUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectManage_NodeInfoUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectManageServer).NodeInfoUpdate(ctx, req.(*ConnectNodeInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConnectManage_NodeInfoDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectManageServer).NodeInfoDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectManage_NodeInfoDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectManageServer).NodeInfoDelete(ctx, req.(*WithID))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConnectManage_NodeInfoGetOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectNodeInfoGetOneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectManageServer).NodeInfoGetOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectManage_NodeInfoGetOne_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectManageServer).NodeInfoGetOne(ctx, req.(*ConnectNodeInfoGetOneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConnectManage_NodeInfoGetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConnectNodeInfoListReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectManageServer).NodeInfoGetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectManage_NodeInfoGetList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectManageServer).NodeInfoGetList(ctx, req.(*ConnectNodeInfoListReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConnectManage_MappingInfoUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectManageServer).MappingInfoUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectManage_MappingInfoUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectManageServer).MappingInfoUpdate(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConnectManage_MappingInfoGetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectManageServer).MappingInfoGetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectManage_MappingInfoGetList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectManageServer).MappingInfoGetList(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConnectManage_MappingOperationLogGetList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectManageServer).MappingOperationLogGetList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectManage_MappingOperationLogGetList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectManageServer).MappingOperationLogGetList(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConnectManage_MappingSyncUns_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectManageServer).MappingSyncUns(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectManage_MappingSyncUns_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectManageServer).MappingSyncUns(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

func _ConnectManage_MappingDeployToEdge_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ConnectManageServer).MappingDeployToEdge(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ConnectManage_MappingDeployToEdge_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ConnectManageServer).MappingDeployToEdge(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// ConnectManage_ServiceDesc is the grpc.ServiceDesc for ConnectManage service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ConnectManage_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "uns.ConnectManage",
	HandlerType: (*ConnectManageServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "nodeInfoCreate",
			Handler:    _ConnectManage_NodeInfoCreate_Handler,
		},
		{
			MethodName: "nodeInfoUpdate",
			Handler:    _ConnectManage_NodeInfoUpdate_Handler,
		},
		{
			MethodName: "nodeInfoDelete",
			Handler:    _ConnectManage_NodeInfoDelete_Handler,
		},
		{
			MethodName: "nodeInfoGetOne",
			Handler:    _ConnectManage_NodeInfoGetOne_Handler,
		},
		{
			MethodName: "nodeInfoGetList",
			Handler:    _ConnectManage_NodeInfoGetList_Handler,
		},
		{
			MethodName: "mappingInfoUpdate",
			Handler:    _ConnectManage_MappingInfoUpdate_Handler,
		},
		{
			MethodName: "mappingInfoGetList",
			Handler:    _ConnectManage_MappingInfoGetList_Handler,
		},
		{
			MethodName: "mappingOperationLogGetList",
			Handler:    _ConnectManage_MappingOperationLogGetList_Handler,
		},
		{
			MethodName: "mappingSyncUns",
			Handler:    _ConnectManage_MappingSyncUns_Handler,
		},
		{
			MethodName: "mappingDeployToEdge",
			Handler:    _ConnectManage_MappingDeployToEdge_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/uns.proto",
}
