// etl.go
package etl

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/apache/arrow-go/v18/arrow"
	"github.com/apache/arrow-go/v18/arrow/array"
	"github.com/apache/arrow-go/v18/arrow/memory"
	"github.com/apache/iceberg-go"
	"github.com/apache/iceberg-go/catalog"
	_ "github.com/apache/iceberg-go/catalog/rest"
	"github.com/apache/iceberg-go/table"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
)

const LastKey = "etl:last_id"

// Buffer 结构体
type Buffer struct {
	Records     []*relationDB.UnsTimeNamespaceRecord
	LastFlushed time.Time
}

type ETLService struct {
	db            *sql.DB
	cat           catalog.Catalog
	rdb           *redis.Redis
	buffers       map[int64]*Buffer // node_id -> buffer
	mu            sync.Mutex
	flushSize     int
	flushTimeout  time.Duration
	queryDuration time.Duration
	queryLimit    time.Duration
}

// 当buffer>flushSize || flushTimeout 达到时flush;
// flushTimeout 定义小于 queryDuration
// NewETLService
func NewETLService(svcCtx *svc.ServiceContext, flushSize int, flushTimeout, queryDuration time.Duration) (*ETLService, error) {
	catalog, err := NewCatelog(svcCtx.Config.AwsS3tables)
	if err != nil {
		return nil, err
	}
	return &ETLService{
		db:            nil,
		cat:           catalog,
		rdb:           svcCtx.Redis,
		buffers:       make(map[int64]*Buffer),
		flushSize:     flushSize,
		flushTimeout:  flushTimeout,
		queryDuration: queryDuration,
	}, nil
}

func (e *ETLService) Run(ctx context.Context) {
	go func() {
		ticker := time.NewTicker(e.queryDuration)
		defer ticker.Stop()
		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				e.processNewRows(ctx)
				e.flushIfNeeded(ctx)
			}
		}
	}()
}

func (e *ETLService) getLastID(ctx context.Context) (int64, error) {
	val, err := e.rdb.GetCtx(ctx, LastKey)
	if err != nil {
		return 0, err
	}
	if val == "" {
		logx.Infof("初始化ETL_LASTID")
		return 0, nil
	}
	valInt, err := strconv.Atoi(val)
	if err != nil {
		return 0, err
	}
	return int64(valInt), nil
}

func (e *ETLService) saveLastID(ctx context.Context, id int64) {
	e.rdb.SetCtx(ctx, LastKey, fmt.Sprintf("%d", id))
}

func (e *ETLService) processNewRows(ctx context.Context) {
	lastID, err := e.getLastID(ctx)
	if err != nil {
		logx.Errorf("ETL getLastID error:%v", err)
		return
	}
	logx.Infof("ETL LastID:%d", lastID)

	repo := relationDB.NewTimeNamespaceRecordRepo(ctx)
	filter := relationDB.TimeNamespaceRecordFilter{
		IDGT: lastID,
	}
	var page *stores.PageInfo
	if e.queryLimit > 0 {
		page = &stores.PageInfo{Size: int64(e.queryLimit)}
	}
	records, err := repo.FindByFilter(ctx, filter, page)
	if err != nil {
		logx.Debug("Query error:", err)
		return
	}
	if len(records) == 0 {
		logx.Debug("Query Count 0 , do nothing")
		return
	}

	// 遍历查询结果并处理
	for _, r := range records {
		e.mu.Lock()
		buffer, exists := e.buffers[r.NodeID]
		if !exists {
			buffer = &Buffer{LastFlushed: time.Now()}
			e.buffers[r.NodeID] = buffer
		}
		buffer.Records = append(buffer.Records, r)
		e.mu.Unlock()

		lastID = r.ID
	}
	e.saveLastID(ctx, lastID)

}

func (e *ETLService) flushIfNeeded(ctx context.Context) {
	e.mu.Lock()
	defer e.mu.Unlock()

	now := time.Now()
	for nodeID, buffer := range e.buffers {
		if len(buffer.Records) == 0 {
			continue
		}
		if len(buffer.Records) >= e.flushSize || now.Sub(buffer.LastFlushed) >= e.flushTimeout {
			e.flushBuffer(ctx, nodeID, buffer)
			buffer.Records = nil
			buffer.LastFlushed = now
		}
	}
}

func (e *ETLService) flushBuffer(ctx context.Context, nodeID int64, buffer *Buffer) {
	tableName := fmt.Sprintf("time_namespace_record_%d", nodeID)
	ident := []string{"tier0", tableName}
	exists, err := e.cat.CheckTableExists(ctx, ident)
	if err != nil {
		logx.Debug("Check table error:", err)
		return
	}

	var tbl *table.Table
	if !exists {
		schema := iceberg.NewSchema(1,
			iceberg.NestedField{ID: 1, Name: "id", Type: iceberg.PrimitiveTypes.Int64, Required: true},
			iceberg.NestedField{ID: 2, Name: "node_id", Type: iceberg.PrimitiveTypes.Int64, Required: true},
			iceberg.NestedField{ID: 3, Name: "timestamp", Type: iceberg.PrimitiveTypes.TimestampTz, Required: true},
			iceberg.NestedField{ID: 4, Name: "payload", Type: iceberg.PrimitiveTypes.String, Required: false},
			// iceberg.NestedField{ID: 5, Name: "created_time", Type: iceberg.PrimitiveTypes.TimestampTz, Required: true},
		)
		tbl, err = e.cat.CreateTable(ctx, ident, schema)
		if err != nil {
			logx.Debug("Create table error:", err)
			return
		}
	} else {
		tbl, err = e.cat.LoadTable(ctx, ident, nil)
		if err != nil {
			logx.Debug("Load table error:", err)
			return
		}
	}

	records := buffer.Records
	pool := memory.NewGoAllocator()
	tsType := &arrow.TimestampType{Unit: arrow.Microsecond, TimeZone: "UTC"}
	fields := []arrow.Field{
		{Name: "id", Type: arrow.PrimitiveTypes.Int64},
		{Name: "node_id", Type: arrow.PrimitiveTypes.Int64},
		{Name: "timestamp", Type: tsType}, // 必须加 tz
		{Name: "payload", Type: arrow.BinaryTypes.String},
		// {Name: "created_time", Type: tsType},
	}
	schema := arrow.NewSchema(fields, nil)
	idBuilder := array.NewInt64Builder(pool)
	nodeBuilder := array.NewInt64Builder(pool)
	tsBuilder := array.NewTimestampBuilder(pool, tsType)
	payloadBuilder := array.NewStringBuilder(pool)
	// createdBuilder := array.NewTimestampBuilder(pool, &arrow.TimestampType{Unit: arrow.Nanosecond, TimeZone: "UTC"})

	for _, r := range records {
		payloadJSON, err := json.Marshal(r.Payload)
		if err != nil {
			continue
		}
		payloadBuilder.Append(string(payloadJSON))
		idBuilder.Append(r.ID)
		nodeBuilder.Append(r.NodeID)
		tsBuilder.Append(arrow.Timestamp(r.Timestamp.UnixMicro()))
	}

	cols := []arrow.Array{
		idBuilder.NewArray(),
		nodeBuilder.NewArray(),
		tsBuilder.NewArray(),
		payloadBuilder.NewArray(),
	}

	record := array.NewRecord(
		schema,
		cols,
		int64(len(records)),
	)
	defer record.Release()

	rdr, err := array.NewRecordReader(record.Schema(), []arrow.Record{record})
	if err != nil {
		logx.Debugf("failed to NewRecordReader: %v", err)
		return
	}
	defer rdr.Release()

	_, err = tbl.Append(ctx, rdr, nil)
	if err != nil {
		logx.Debug("Append error:", err)
	}
	logx.Debugf("[FLUSH] node_id=%d, rows=%d\n", nodeID, len(records))
}
