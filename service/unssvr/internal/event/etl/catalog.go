package etl

import (
	"context"
	"fmt"
	"os"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/config"
	"github.com/apache/iceberg-go"
	"github.com/apache/iceberg-go/catalog"
	"github.com/aws/aws-sdk-go-v2/aws"
	awsConfig "github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sts"
	"github.com/zeromicro/go-zero/core/logx"
)

func NewCatelog(cfg config.AwsS3tables) (catalog.Catalog, error) {
	logx.Infof("AWS_ACCESS_KEY_ID=", os.Getenv("AWS_ACCESS_KEY_ID"))
	logx.Infof("AWS_SECRET_ACCESS_KEY=", os.Getenv("AWS_SECRET_ACCESS_KEY"))

	bucket := cfg.Bucket
	_, accountID, region, err := getAWSAccountRegion()
	if err != nil {
		logx.Errorf("catalog getAWSAccountRegion error:%v", err)
		return nil, err
	}
	propsUri := fmt.Sprintf("https://s3tables.%s.amazonaws.com/iceberg", region)
	propsWarehouse := fmt.Sprintf("arn:aws:s3tables:%s:%s:bucket/%s", region, accountID, bucket)
	props := iceberg.Properties{
		"uri":                 propsUri,
		"warehouse":           propsWarehouse,
		"rest.sigv4-enabled":  "true",
		"rest.signing-name":   "s3tables",
		"rest.signing-region": region,
	}

	logx.Debug("iceber.Properties配置===>")
	for k, v := range props {
		logx.Infof("%v   :   %v", k, v)
	}

	ctx := context.Background()
	cat, err := catalog.Load(ctx, "S3TablesCatalog", props)
	if err != nil {
		logx.Errorf("failed to load catalog: %v", err.Error())
		return nil, err
	}
	logx.Infof("catalog client Load success!")
	return cat, nil
}

func getAWSAccountRegion() (cfg aws.Config, accountID string, region string, err error) {
	ctx := context.Background()
	cfg, err = awsConfig.LoadDefaultConfig(
		ctx,
	)
	if err != nil {
		return
	}
	region = cfg.Region
	client := sts.NewFromConfig(cfg)
	out, err := client.GetCallerIdentity(ctx, &sts.GetCallerIdentityInput{})
	if err != nil {
		return
	}
	accountID = *out.Account
	return
}
