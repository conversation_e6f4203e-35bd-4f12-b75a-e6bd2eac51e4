package deviceSub

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"gitee.com/unitedrhino/share/utils"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/share/clients"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/datatypes"
)

type DeviceSubServer struct {
	svcCtx *svc.ServiceContext
	ctx    context.Context
	logx.Logger
	repo *relationDB.TimeNamespaceRecordRepo
}

func NewDeviceSubServer(svcCtx *svc.ServiceContext, ctx context.Context) *DeviceSubServer {
	return &DeviceSubServer{
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		repo:   relationDB.NewTimeNamespaceRecordRepo(ctx),
	}
}

var nodeidMap = map[string]int64{}

// Msg 设备发布物模型消息的信息通过nats转发给内部服务
func (s *DeviceSubServer) Msg(topic string, payload []byte) error {
	var tmp any
	if err := json.Unmarshal(payload, &tmp); err != nil {
		return fmt.Errorf("非法 JSON: %w", err)
	}
	var nodeID int64 = 0
	if cacheNID, ok := nodeidMap[topic]; !ok {
		// 查找nodeid
		nodeRepo := relationDB.NewNamespaceNodeInfoRepo(s.ctx)
		info, err := nodeRepo.FindOneByFilter(s.ctx, relationDB.NamespaceNodeInfoFilter{Namespace: topic})
		if err != nil {
			return err
		}
		nodeidMap[topic] = info.ID
		nodeID = info.ID
	} else {
		nodeID = cacheNID
	}

	data := relationDB.UnsTimeNamespaceRecord{
		NodeID:    nodeID,
		Timestamp: time.Now(),
		Payload:   datatypes.JSON(payload),
	}
	s.repo.AsyncInsert(data)
	return nil
}

func (s *DeviceSubServer) Connected(info *clients.DevConn) error {
	s.Infof("%s info:%v", utils.FuncName(), utils.Fmt(info))
	return nil
}

func (s *DeviceSubServer) Disconnected(info *clients.DevConn) error {
	s.Infof("%s info:%v", utils.FuncName(), utils.Fmt(info))
	return nil
}
