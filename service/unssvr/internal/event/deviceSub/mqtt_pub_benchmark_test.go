package deviceSub

import (
	"encoding/json"
	"fmt"
	"log"
	"math/rand"
	"sync/atomic"
	"testing"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"
)

var (
	broker   = "tcp://127.0.0.1:1883"
	topics   = []string{"a", "b", "c", "d", "e", "f", "g", "h", "i"}
	clientID = "benchmark-pub"
)

type Payload struct {
	TimeStamp int64  `json:"timeStamp"`
	N30101    int    `json:"n30101"`
	Quality   string `json:"quality"`
}

var (
	client       mqtt.Client
	failCount    int64
	successCount int64
)

func init() {
	opts := mqtt.NewClientOptions().
		AddBroker(broker).
		SetClientID(fmt.Sprintf("%s-%d", clientID, time.Now().UnixNano())).
		SetCleanSession(true)

	client = mqtt.NewClient(opts)
	if token := client.Connect(); token.Wait() && token.Error() != nil {
		panic(token.Error())
	}
}

func generatePayload() []byte {
	p := Payload{
		TimeStamp: time.Now().Unix(),
		N30101:    rand.Intn(990001) + 10000,
		Quality:   []string{"0", "1", "2"}[rand.Intn(3)],
	}
	data, _ := json.Marshal(p)
	return data
}

func BenchmarkMQTTPublish(b *testing.B) {
	atomic.StoreInt64(&failCount, 0)
	atomic.StoreInt64(&successCount, 0)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		topic := topics[rand.Intn(len(topics))]
		data := generatePayload()

		start := time.Now()
		token := client.Publish(topic, 0, false, data)
		token.Wait()
		_ = time.Since(start)

		if token.Error() != nil {
			atomic.AddInt64(&failCount, 1)
		} else {
			atomic.AddInt64(&successCount, 1)
		}
	}
	b.StopTimer()

	fmt.Printf("✅ Success: %d, ❌ Failed: %d, ⏱ Avg latency: %.2f µs\n",
		successCount,
		failCount,
		float64(b.Elapsed().Microseconds())/float64(b.N),
	)
}

func BenchmarkMQTTPublishParallel(b *testing.B) {
	atomic.StoreInt64(&failCount, 0)
	atomic.StoreInt64(&successCount, 0)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			topic := topics[rand.Intn(len(topics))]
			data := generatePayload()

			start := time.Now()
			token := client.Publish(topic, 0, false, data)
			token.Wait()
			_ = time.Since(start)

			if token.Error() != nil {
				atomic.AddInt64(&failCount, 1)
			} else {
				atomic.AddInt64(&successCount, 1)
			}
		}
	})
	b.StopTimer()

	fmt.Printf("✅ [Parallel] Success: %d, ❌ Failed: %d, ⏱ Avg latency: %.2f µs\n",
		successCount,
		failCount,
		float64(b.Elapsed().Microseconds())/float64(b.N),
	)
}

func TestMQTTPublish20W(t *testing.T) {
	const totalMessages = 10_000
	var successCount, failCount int64

	mqttClient := client

	start := time.Now()

	for i := 0; i < totalMessages; i++ {
		topic := topics[rand.Intn(len(topics))]
		payload := generatePayload()

		token := mqttClient.Publish(topic, 0, false, payload)
		token.Wait()

		if token.Error() != nil {
			atomic.AddInt64(&failCount, 1)
		} else {
			atomic.AddInt64(&successCount, 1)
		}

		if i > 0 && i%(totalMessages/10) == 0 {
			log.Printf("📦 已发送 %d 条\n", i)
		}
	}

	duration := time.Since(start)
	qps := float64(successCount) / duration.Seconds()

	log.Printf("\n✅ Success: %d\n❌ Failed: %d\n⏱ Time: %v\n🚀 QPS: %.2f\n",
		successCount, failCount, duration, qps,
	)
}
