package connectmanagelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type MappingDeployToEdgeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewMappingDeployToEdgeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MappingDeployToEdgeLogic {
	return &MappingDeployToEdgeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 部署到边缘节点
func (l *MappingDeployToEdgeLogic) MappingDeployToEdge(in *uns.Empty) (*uns.Empty, error) {
	// todo: add your logic here and delete this line

	return &uns.Empty{}, nil
}
