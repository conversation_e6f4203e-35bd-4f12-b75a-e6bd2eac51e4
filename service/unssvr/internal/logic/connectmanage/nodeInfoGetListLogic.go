package connectmanagelogic

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/logic"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodeInfoGetListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoGetListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoGetListLogic {
	return &NodeInfoGetListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodeInfoGetListLogic) NodeInfoGetList(in *uns.ConnectNodeInfoListReq) (*uns.ConnectNodeInfoListResp, error) {
	db := relationDB.NewConnectNodeInfoRepo(stores.GetCommonConn(l.ctx))
	filter := relationDB.ConnectNodeInfoFilter{}
	infos, err := db.FindByFilter(l.ctx, filter, logic.ToPageInfo(in.Page))
	if err != nil {
		return nil, err
	}
	total, err := db.CountByFilter(l.ctx, filter)
	resp := &uns.ConnectNodeInfoListResp{}
	resp.Total = total
	for _, info := range infos {
		resp.List = append(resp.List, &uns.ConnectNodeInfo{
			Id:          info.ID,
			Name:        info.Name,
			Description: info.Description,
			AuthMethod:  info.AuthMethod,
			Username:    info.Username,
			Password:    info.Password,
			SecretKey:   info.SecretKey,
			Cpu:         info.CPU,
			Memory:      info.Memory,
			Msg:         info.Msg,
			Version:     info.Version,
		})
	}
	return resp, nil

}
