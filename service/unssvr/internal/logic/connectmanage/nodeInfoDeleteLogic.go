package connectmanagelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodeInfoDeleteLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoDeleteLogic {
	return &NodeInfoDeleteLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodeInfoDeleteLogic) NodeInfoDelete(in *uns.WithID) (*uns.Empty, error) {
	ctx := context.Background()
	db := relationDB.NewConnectNodeInfoRepo(ctx)
	err := db.Delete(ctx, in.Id)
	return &uns.Empty{}, err
}
