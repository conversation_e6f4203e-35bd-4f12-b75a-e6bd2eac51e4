package connectmanagelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type MappingSyncUnsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewMappingSyncUnsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MappingSyncUnsLogic {
	return &MappingSyncUnsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 从边缘节点同步节点
func (l *MappingSyncUnsLogic) MappingSyncUns(in *uns.Empty) (*uns.Empty, error) {
	// todo: add your logic here and delete this line

	return &uns.Empty{}, nil
}
