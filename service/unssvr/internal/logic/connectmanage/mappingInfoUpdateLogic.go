package connectmanagelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type MappingInfoUpdateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewMappingInfoUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MappingInfoUpdateLogic {
	return &MappingInfoUpdateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 先不做
func (l *MappingInfoUpdateLogic) MappingInfoUpdate(in *uns.Empty) (*uns.Empty, error) {
	// todo: add your logic here and delete this line

	return &uns.Empty{}, nil
}
