package connectmanagelogic

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodeInfoUpdateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoUpdateLogic {
	return &NodeInfoUpdateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodeInfoUpdateLogic) NodeInfoUpdate(in *uns.ConnectNodeInfo) (*uns.Empty, error) {
	ctx := context.Background()

	repo := relationDB.NewConnectNodeInfoRepo(stores.GetCommonConn(ctx))

	// 1. 检查节点是否存在
	_, err := repo.FindOne(ctx, in.Id)
	if err != nil {
		return nil, err
	}

	// 2. 只允许更新部分字段
	updates := map[string]interface{}{}
	if in.Name != "" {
		updates["name"] = in.Name
	}
	if in.Username != "" {
		updates["username"] = in.Username
	}
	if in.AuthMethod != 0 {
		updates["auth_method"] = in.AuthMethod
	}
	if in.Password != "" {
		updates["password"] = in.Password
	}
	if in.Description != "" {
		updates["description"] = in.Description
	}
	if len(updates) == 0 {
		return &uns.Empty{}, nil
	}
	if err := repo.UpdateWithField(ctx, relationDB.ConnectNodeInfoFilter{ID: in.Id}, updates); err != nil {
		return nil, err
	}
	return &uns.Empty{}, nil

}
