package connectmanagelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type MappingOperationLogGetListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewMappingOperationLogGetListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MappingOperationLogGetListLogic {
	return &MappingOperationLogGetListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 映射操作日志获取
func (l *MappingOperationLogGetListLogic) MappingOperationLogGetList(in *uns.Empty) (*uns.Empty, error) {
	// todo: add your logic here and delete this line

	return &uns.Empty{}, nil
}
