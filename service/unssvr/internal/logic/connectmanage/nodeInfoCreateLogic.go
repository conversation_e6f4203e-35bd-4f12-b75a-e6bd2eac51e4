package connectmanagelogic

import (
	"context"
	"fmt"

	"gitee.com/unitedrhino/share/stores"
	"gitee.com/unitedrhino/share/utils"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"
	"gorm.io/gorm"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodeInfoCreateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoCreateLogic {
	return &NodeInfoCreateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodeInfoCreateLogic) NodeInfoCreate(in *uns.ConnectNodeInfo) (*uns.WithID, error) {
	ctx := context.Background()
	id := l.svcCtx.NodeID.GetSnowflakeId()
	err := stores.GetCommonConn(ctx).Transaction(func(tx *gorm.DB) error {
		if in.Name == "" {
			return fmt.Errorf(" name empty")
		}
		db := relationDB.NewConnectNodeInfoRepo(tx)

		// 检查唯一性
		exist, err := db.FindOneByFilter(ctx, relationDB.ConnectNodeInfoFilter{Name: in.Name})
		if err == nil && exist.ID > 0 {
			return fmt.Errorf("name重复")
		}
		secretKey := utils.GetRandomBase64(20)
		info := &relationDB.UnsConnectNodeInfo{
			ID:          id,
			Name:        in.Name,
			Description: in.Description,
			AuthMethod:  in.AuthMethod,
			Username:    in.Username,
			Password:    in.Password,
			SecretKey:   secretKey,
			CPU:         in.Cpu,
			Memory:      in.Memory,
			Msg:         in.Msg,
			Version:     in.Version,
		}

		if err := db.Insert(ctx, info); err != nil {
			return err
		}
		logx.Infof("ConnectNodeInfoCreate success:name= %s,id=%d", info.Name, info.ID)
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &uns.WithID{Id: id}, nil
}
