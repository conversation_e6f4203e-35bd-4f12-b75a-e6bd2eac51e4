package connectmanagelogic

import (
	"context"
	"fmt"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodeInfoGetOneLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoGetOneLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoGetOneLogic {
	return &NodeInfoGetOneLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodeInfoGetOneLogic) NodeInfoGetOne(in *uns.ConnectNodeInfoGetOneReq) (*uns.ConnectNodeInfo, error) {
	db := relationDB.NewConnectNodeInfoRepo(stores.GetCommonConn(l.ctx))
	info, err := db.FindOneByFilter(l.ctx, relationDB.ConnectNodeInfoFilter{ID: in.Id})
	if err != nil {
		return nil, fmt.Errorf("not found")
	}
	// 组装返回
	return &uns.ConnectNodeInfo{
		Id:          info.ID,
		Name:        info.Name,
		Description: info.Description,
		AuthMethod:  info.AuthMethod,
		Username:    info.Username,
		Password:    info.Password,
		SecretKey:   info.SecretKey,
		Cpu:         info.CPU,
		Memory:      info.Memory,
		Msg:         info.Msg,
		Version:     info.Version,
	}, nil
}
