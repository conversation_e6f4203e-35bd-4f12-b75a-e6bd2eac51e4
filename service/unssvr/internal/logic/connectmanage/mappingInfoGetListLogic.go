package connectmanagelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type MappingInfoGetListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewMappingInfoGetListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MappingInfoGetListLogic {
	return &MappingInfoGetListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 全部挂载在根节点1下面
func (l *MappingInfoGetListLogic) MappingInfoGetList(in *uns.Empty) (*uns.Empty, error) {
	// todo: add your logic here and delete this line

	return &uns.Empty{}, nil
}
