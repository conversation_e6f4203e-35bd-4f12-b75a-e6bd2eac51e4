package namespacelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodeInfoRecycleDeleteLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoRecycleDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoRecycleDeleteLogic {
	return &NodeInfoRecycleDeleteLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodeInfoRecycleDeleteLogic) NodeInfoRecycleDelete(in *uns.WithID) (*uns.Empty, error) {
	repo := relationDB.NewNamespaceNodeInfoRepo(l.ctx)
	// 1. 检查节点是否存在
	old, err := repo.FindOneByFilter(l.ctx, relationDB.NamespaceNodeInfoFilter{ID: in.Id, Unscoped: true})
	if err != nil {
		return nil, err
	}
	old.RecycleIsDel = 1
	err = repo.Update(l.ctx, old)
	return &uns.Empty{}, err
}
