package namespacelogic

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/logic"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type TemplateInfoGetListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewTemplateInfoGetListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TemplateInfoGetListLogic {
	return &TemplateInfoGetListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *TemplateInfoGetListLogic) TemplateInfoGetList(in *uns.NamespaceTemplateInfoGetListReq) (*uns.NamespaceTemplateInfoGetListResp, error) {
	db := relationDB.NewNamespaceTemplateInfoRepo(stores.GetCommonConn(l.ctx))
	filter := relationDB.NamespaceTemplateInfoFilter{Name: in.Name}
	Templates, err := db.FindByFilter(l.ctx, filter, logic.ToPageInfo(in.Page))
	if err != nil {
		return nil, err
	}
	total, err := db.CountByFilter(l.ctx, filter)
	resp := &uns.NamespaceTemplateInfoGetListResp{}
	resp.Total = total
	for _, Template := range Templates {
		var pbDefs []*uns.NamespaceNodeInfoDefinition
		for _, def := range Template.Definition {
			pbDefs = append(pbDefs, &uns.NamespaceNodeInfoDefinition{
				Name:        def.Name,
				Type:        def.Type,
				MaxLen:      int64(def.MaxLen),
				Remark:      def.Remark,
				DisplayName: def.DisplayName,
				SystemField: def.SystemField,
			})
		}
		resp.List = append(resp.List, &uns.NamespaceTemplateInfo{
			Id:          Template.ID,
			Name:        Template.Name,
			NodeID:      Template.NodeID,
			Description: Template.Description,
			Definition:  pbDefs,
		})
	}
	return resp, nil
}
