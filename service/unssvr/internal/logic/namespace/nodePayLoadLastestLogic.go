package namespacelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodePayLoadLastestLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodePayLoadLastestLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodePayLoadLastestLogic {
	return &NodePayLoadLastestLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodePayLoadLastestLogic) NodePayLoadLastest(in *uns.Request) (*uns.Response, error) {
	// todo: add your logic here and delete this line

	return &uns.Response{}, nil
}
