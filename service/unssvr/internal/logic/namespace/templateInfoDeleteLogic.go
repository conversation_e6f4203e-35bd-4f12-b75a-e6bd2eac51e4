package namespacelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type TemplateInfoDeleteLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewTemplateInfoDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TemplateInfoDeleteLogic {
	return &TemplateInfoDeleteLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *TemplateInfoDeleteLogic) TemplateInfoDelete(in *uns.WithID) (*uns.Empty, error) {
	ctx := context.Background()
	db := relationDB.NewNamespaceTemplateInfoRepo(ctx)
	err := db.Delete(ctx, in.Id)
	return &uns.Empty{}, err
}
