package namespacelogic

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type TemplateInfoGetOneLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewTemplateInfoGetOneLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TemplateInfoGetOneLogic {
	return &TemplateInfoGetOneLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *TemplateInfoGetOneLogic) TemplateInfoGetOne(in *uns.WithID) (*uns.NamespaceTemplateInfo, error) {
	db := relationDB.NewNamespaceTemplateInfoRepo(stores.GetCommonConn(l.ctx))
	filter := relationDB.NamespaceTemplateInfoFilter{ID: in.Id}
	Template, err := db.FindOneByFilter(l.ctx, filter)
	if err != nil {
		return nil, err
	}

	var pbDefs []*uns.NamespaceNodeInfoDefinition
	for _, def := range Template.Definition {
		pbDefs = append(pbDefs, &uns.NamespaceNodeInfoDefinition{
			Name:        def.Name,
			Type:        def.Type,
			MaxLen:      int64(def.MaxLen),
			Remark:      def.Remark,
			DisplayName: def.DisplayName,
			SystemField: def.SystemField,
		})
	}
	resp := &uns.NamespaceTemplateInfo{
		Id:          Template.ID,
		Name:        Template.Name,
		NodeID:      Template.NodeID,
		Description: Template.Description,
		Definition:  pbDefs,
		CreatedTime: Template.CreatedTime.UnixMilli(),
		UpdatedTime: Template.UpdatedTime.UnixMilli(),
	}

	return resp, nil
}
