package namespacelogic

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"
	"gorm.io/gorm"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodeInfoRecycleUndoLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoRecycleUndoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoRecycleUndoLogic {
	return &NodeInfoRecycleUndoLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodeInfoRecycleUndoLogic) NodeInfoRecycleUndo(in *uns.WithID) (*uns.Empty, error) {

	err := stores.GetCommonConn(l.ctx).Transaction(func(tx *gorm.DB) error {
		repo := relationDB.NewNamespaceNodeInfoRepo(tx)
		// 1. 检查节点是否存在
		node, err := repo.FindOneByFilter(l.ctx, relationDB.NamespaceNodeInfoFilter{ID: in.Id, Unscoped: true})
		if err != nil {
			return err
		}
		// 文件夹需要递归处理文件
		if node.NodeType == 1 {
			err = repo.UndoByDir(l.ctx, node.IDPath)
		} else {
			node.DeletedTime = 0
			err = repo.Update(l.ctx, node)
		}
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &uns.Empty{}, nil
}
