package namespacelogic

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodeInfoGetListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoGetListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoGetListLogic {
	return &NodeInfoGetListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodeInfoGetListLogic) NodeInfoGetList(in *uns.NamespaceNodeInfoGetListReq) (*uns.NamespaceNodeInfoGetListResp, error) {
	db := relationDB.NewNamespaceNodeInfoRepo(stores.GetCommonConn(l.ctx))

	// 1. 查询所有节点
	nodes := make([]*relationDB.UnsNamespaceNodeInfo, 0)
	filter := relationDB.NamespaceNodeInfoFilter{
		Recycle: in.Recycle,
	}
	page := &stores.PageInfo{
		Orders: []stores.OrderBy{
			{
				Field: "created_time",
				Sort:  stores.OrderAsc,
			},
		},
	}
	nodes, err := db.FindByFilter(l.ctx, filter, page)
	if err != nil {
		return nil, err
	}

	// 2. 构建id->node映射
	nodeMap := make(map[int64]*uns.NamespaceNodeInfo)
	for _, n := range nodes {
		pb := NodeInfoPoToPb(l.ctx, l.svcCtx, n, false)
		nodeMap[n.ID] = pb
	}

	// 3. 按照idpath关系串起来
	var roots []*uns.NamespaceNodeInfo

	// nodes 有序遍历
	for _, v := range nodes {
		node := nodeMap[v.ID]
		if node.ParentID == 0 {
			roots = append(roots, node)
		} else if parent, ok := nodeMap[node.ParentID]; ok {
			parent.Children = append(parent.Children, node)
		}
	}

	// 4. 返回
	return &uns.NamespaceNodeInfoGetListResp{
		Total: int64(len(nodes)),
		List:  roots,
	}, nil
}
