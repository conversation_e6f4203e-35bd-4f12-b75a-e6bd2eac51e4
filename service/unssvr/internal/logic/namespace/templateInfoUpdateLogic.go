package namespacelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type TemplateInfoUpdateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewTemplateInfoUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TemplateInfoUpdateLogic {
	return &TemplateInfoUpdateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *TemplateInfoUpdateLogic) TemplateInfoUpdate(in *uns.NamespaceTemplateInfo) (*uns.Empty, error) {
	ctx := context.Background()
	repo := relationDB.NewNamespaceTemplateInfoRepo(ctx)

	// 更新字段
	updates := map[string]interface{}{}
	if in.Name != "" {
		updates["name"] = in.Name
	}
	if len(in.Definition) > 0 {
		defs := make([]relationDB.Definition, 0, len(in.Definition))
		for _, def := range in.Definition {
			defs = append(defs, relationDB.Definition{
				Name:        def.Name,
				Type:        def.Type,
				MaxLen:      int(def.MaxLen),
				Remark:      def.Remark,
				DisplayName: def.DisplayName,
				SystemField: def.SystemField,
			})
		}
		updates["definition"] = defs
	}
	if len(updates) == 0 {
		return &uns.Empty{}, nil
	}
	if err := repo.UpdateWithField(ctx, relationDB.NamespaceTemplateInfoFilter{ID: in.Id}, updates); err != nil {
		return nil, err
	}
	return &uns.Empty{}, nil
}
