package namespacelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodePayLoadGetListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodePayLoadGetListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodePayLoadGetListLogic {
	return &NodePayLoadGetListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodePayLoadGetListLogic) NodePayLoadGetList(in *uns.Request) (*uns.Response, error) {
	// todo: add your logic here and delete this line

	return &uns.Response{}, nil
}
