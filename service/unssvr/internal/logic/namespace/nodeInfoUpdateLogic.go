package namespacelogic

import (
	"context"
	"fmt"
	"strings"

	"gitee.com/unitedrhino/share/errors"
	"gitee.com/unitedrhino/share/oss"
	"gitee.com/unitedrhino/share/oss/common"
	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"
	"gorm.io/gorm"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodeInfoUpdateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoUpdateLogic {
	return &NodeInfoUpdateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodeInfoUpdateLogic) NodeInfoUpdate(in *uns.NamespaceNodeInfo) (ret *uns.Empty, err error) {

	err = stores.GetCommonConn(l.ctx).Transaction(func(tx *gorm.DB) error {

		repo := relationDB.NewNamespaceNodeInfoRepo(tx)

		// 1. 检查节点是否存在
		old, err := repo.FindOne(l.ctx, in.Id)
		if err != nil {
			return err
		}
		new := *old
		// 2. 只允许更新部分字段
		if in.DisplayName != "" {
			new.DisplayName = in.DisplayName
		}
		if in.Description != "" {
			new.Description = in.Description
		}
		if in.ExtendProperties != nil {
			new.ExtendProperties = in.ExtendProperties
		}
		if in.Persistence != 0 {
			new.Persistence = in.Persistence
		}
		// 校验labels,防止新增的label在库里已存在
		labels := []relationDB.UnsNamespaceLabelInfo{}
		for k, v := range in.Labels {
			if v.Id == 0 {
				labeldb := relationDB.NewNamespaceLabelInfoRepo(stores.GetCommonConn(l.ctx))
				label, err := labeldb.FindOneByFilter(l.ctx, relationDB.NamespaceLabelInfoFilter{Name: v.Name})
				if err != nil {
					continue
				}
				in.Labels[k].Id = label.ID
			}
		}
		for _, v := range in.Labels {
			labels = append(labels, relationDB.UnsNamespaceLabelInfo{
				ID:   v.Id,
				Name: v.Name,
			})
		}
		new.Labels = labels
		var oldA = map[int64]relationDB.Attachment{}
		for _, v := range old.Attachments {
			oldA[v.ID] = v
		}
		if len(in.Attachments) != 0 {
			var up []relationDB.Attachment

			for _, attachment := range in.Attachments {
				if attachment.FilePath != "" {
					nwePath := oss.GenFilePath(l.ctx, l.svcCtx.Config.Name, "namespace", "attachment", fmt.Sprintf("%d/%s", old.ID, oss.GetFileNameWithPath(attachment.FilePath)))
					path, err := l.svcCtx.OssClient.PrivateBucket().CopyFromTempBucket(attachment.FilePath, nwePath)
					if err != nil {
						return errors.System.AddDetail(err)
					}
					up = append(up, relationDB.Attachment{
						ID:       attachment.Id,
						FilePath: path,
					})
				} else {
					o, ok := oldA[attachment.Id]
					if ok { //如果存在则直接把老的复制进去即可,如果没查到,则丢弃
						up = append(up, o)
					}
					delete(oldA, attachment.Id)
				}
			}
			if len(up) != 0 {
				new.Attachments = up
			}
		}
		if len(oldA) != 0 {
			defer func() {
				if err == nil {
					for _, v := range oldA {
						er := l.svcCtx.OssClient.Delete(l.ctx, v.FilePath, common.OptionKv{})
						if er != nil {
							l.Error(v, er)
						}
					}
				}
			}()
		}
		if len(in.Definition) > 0 {
			defs := make([]relationDB.Definition, 0, len(in.Definition))
			for _, def := range in.Definition {
				defs = append(defs, relationDB.Definition{
					Name:        def.Name,
					Type:        def.Type,
					MaxLen:      int(def.MaxLen),
					Remark:      def.Remark,
					DisplayName: def.DisplayName,
					SystemField: def.SystemField,
					Unit:        def.Unit,
				})
			}
			new.Definition = defs
		}
		// 如果改名了
		if in.Name != "" && in.Name != old.Name {
			new.Name = in.Name
			tmp := strings.Split(old.Namespace, "/")
			parentPath := strings.Join(tmp[:len(tmp)-1], "/")
			newPath := parentPath + "/" + in.Name
			newPath = strings.Trim(newPath, "/")
			new.Namespace = newPath
			if old.NodeType == 1 {
				// 批量修改下面的所有节点namespace
				err = repo.UpdateNamespaceByChangeDirName(l.ctx, old.Namespace, newPath)
				if err != nil {
					return err
				}
			}
		}
		err = repo.UpdateWithLabelAssociat(l.ctx, &new)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, err
	}
	return &uns.Empty{}, nil
}
