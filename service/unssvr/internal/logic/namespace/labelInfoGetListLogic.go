package namespacelogic

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/logic"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type LabelInfoGetListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewLabelInfoGetListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LabelInfoGetListLogic {
	return &LabelInfoGetListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *LabelInfoGetListLogic) LabelInfoGetList(in *uns.NamespaceLabelInfoGetListReq) (*uns.NamespaceLabelInfoGetListResp, error) {
	db := relationDB.NewNamespaceLabelInfoRepo(stores.GetCommonConn(l.ctx))
	filter := relationDB.NamespaceLabelInfoFilter{Name: in.Name}
	labels, err := db.FindByFilter(l.ctx, filter, logic.ToPageInfo(in.Page))
	if err != nil {
		return nil, err
	}
	total, err := db.CountByFilter(l.ctx, filter)
	resp := &uns.NamespaceLabelInfoGetListResp{}
	resp.Total = total
	for _, label := range labels {
		resp.List = append(resp.List, &uns.NamespaceLabelInfo{
			Id:   label.ID,
			Name: label.Name,
		})
	}
	return resp, nil
}
