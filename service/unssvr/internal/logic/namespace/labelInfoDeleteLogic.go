package namespacelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type LabelInfoDeleteLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewLabelInfoDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LabelInfoDeleteLogic {
	return &LabelInfoDeleteLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *LabelInfoDeleteLogic) LabelInfoDelete(in *uns.WithID) (*uns.Empty, error) {
	ctx := context.Background()
	db := relationDB.NewNamespaceLabelInfoRepo(ctx)
	err := db.Delete(ctx, in.Id)
	return &uns.Empty{}, err
}
