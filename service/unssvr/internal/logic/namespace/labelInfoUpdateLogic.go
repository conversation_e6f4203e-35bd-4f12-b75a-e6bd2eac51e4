package namespacelogic

import (
	"context"
	"fmt"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type LabelInfoUpdateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewLabelInfoUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LabelInfoUpdateLogic {
	return &LabelInfoUpdateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *LabelInfoUpdateLogic) LabelInfoUpdate(in *uns.NamespaceLabelInfo) (*uns.Empty, error) {
	ctx := context.Background()
	err := stores.GetCommonConn(ctx).Transaction(func(tx *gorm.DB) error {
		db := relationDB.NewNamespaceLabelInfoRepo(tx)
		label, err := db.FindOneByFilter(ctx, relationDB.NamespaceLabelInfoFilter{ID: in.Id})
		if err != nil {
			return fmt.Errorf("label not found")
		}
		// 更新字段
		label.Name = in.Name
		return db.Update(ctx, label)
	})
	if err != nil {
		return nil, err
	}
	return &uns.Empty{}, nil
}
