package namespacelogic

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodeInfoGetOneLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoGetOneLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoGetOneLogic {
	return &NodeInfoGetOneLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodeInfoGetOneLogic) NodeInfoGetOne(in *uns.NamespaceNodeInfoGetOneReq) (*uns.NamespaceNodeInfo, error) {
	db := relationDB.NewNamespaceNodeInfoRepo(stores.GetCommonConn(l.ctx))
	filter := relationDB.NamespaceNodeInfoFilter{
		WithLabels: true,
	}
	filter.ID = in.Id
	if in.WithAttachments {
		filter.WithAttachments = true
	}
	node, err := db.FindOneByFilter(l.ctx, filter)
	if err != nil {
		return nil, err
	}
	pb := NodeInfoPoToPb(l.ctx, l.svcCtx, node, true)
	labels := []*uns.NamespaceLabelInfo{}
	for _, v := range node.Labels {
		labels = append(labels, &uns.NamespaceLabelInfo{
			Id:   v.ID,
			Name: v.Name,
		})
	}
	pb.Labels = labels
	return pb, nil
}
