package namespacelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type LabelNodeIDDeleteLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewLabelNodeIDDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LabelNodeIDDeleteLogic {
	return &LabelNodeIDDeleteLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *LabelNodeIDDeleteLogic) LabelNodeIDDelete(in *uns.WithID) (*uns.Empty, error) {
	ctx := context.Background()
	db := relationDB.NewNamespaceLabelNodeIDRepo(ctx)
	err := db.Delete(ctx, in.Id)
	return &uns.Empty{}, err
}
