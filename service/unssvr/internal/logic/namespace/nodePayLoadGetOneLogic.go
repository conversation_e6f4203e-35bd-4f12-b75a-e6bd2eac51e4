package namespacelogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodePayLoadGetOneLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodePayLoadGetOneLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodePayLoadGetOneLogic {
	return &NodePayLoadGetOneLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodePayLoadGetOneLogic) NodePayLoadGetOne(in *uns.Request) (*uns.Response, error) {
	// todo: add your logic here and delete this line

	return &uns.Response{}, nil
}
