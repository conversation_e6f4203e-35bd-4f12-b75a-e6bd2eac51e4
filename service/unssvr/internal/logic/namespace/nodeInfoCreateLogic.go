package namespacelogic

import (
	"context"
	"fmt"
	"strings"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type NodeInfoCreateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoCreateLogic {
	return &NodeInfoCreateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodeInfoCreateLogic) NodeInfoCreate(in *uns.NamespaceNodeInfo) (*uns.WithID, error) {
	ctx := l.ctx
	var id int64
	err := stores.GetCommonConn(ctx).Transaction(func(tx *gorm.DB) error {
		// 字段校验
		if in.Name == "" {
			return fmt.Errorf("name empty")
		}
		if in.NodeType < 1 || in.NodeType > 3 {
			return fmt.Errorf("nodeType invalid")
		}
		db := relationDB.NewNamespaceNodeInfoRepo(tx)
		// 1. 检查parentId是否存在（非0时）
		parentNamespace := ""
		parentIDPath := ""
		if in.ParentID != 0 {
			parent, err := db.FindOneByFilter(ctx, relationDB.NamespaceNodeInfoFilter{ID: in.ParentID})
			if err != nil {
				return fmt.Errorf("父节点不存在")
			}
			if parent.NodeType == 2 {
				return fmt.Errorf("父节点类型错误")
			}
			parentNamespace = parent.Namespace
			parentIDPath = parent.IDPath
		}

		// 2. 组装namespace和idPath
		curNamespace := strings.Trim(parentNamespace, "/")
		if curNamespace != "" {
			curNamespace += "/"
		}
		curNamespace += in.Name

		// 校验namespace唯一性
		// var exist relationDB.UnsNamespaceNodeInfo
		exist, err := db.FindOneByFilter(ctx, relationDB.NamespaceNodeInfoFilter{Namespace: curNamespace})
		if err == nil && exist.ID > 0 {
			return fmt.Errorf("name重复")
		}

		id = l.svcCtx.NodeID.GetSnowflakeId()

		curIDPath := strings.Trim(parentIDPath, "/")
		if curIDPath != "" {
			curIDPath += "/"
		}
		curIDPath += fmt.Sprintf("%d", id)

		// 5. 处理json类型字段
		definition := make([]relationDB.Definition, 0)
		for _, def := range in.Definition {
			definition = append(definition, relationDB.Definition{
				Name:        def.Name,
				Type:        def.Type,
				MaxLen:      int(def.MaxLen),
				Remark:      def.Remark,
				DisplayName: def.DisplayName,
				SystemField: def.SystemField,
				Unit:        def.Unit,
			})
		}
		extendProperties := make(map[string]string)
		for k, v := range in.ExtendProperties {
			extendProperties[k] = v
		}
		// 校验labels,防止新增的label在库里已存在
		for k, v := range in.Labels {
			if v.Id == 0 {
				labeldb := relationDB.NewNamespaceLabelInfoRepo(stores.GetCommonConn(l.ctx))
				label, err := labeldb.FindOneByFilter(l.ctx, relationDB.NamespaceLabelInfoFilter{Name: v.Name})
				if err != nil {
					continue
				}
				in.Labels[k].Id = label.ID
			}
		}
		var labels []relationDB.UnsNamespaceLabelInfo
		for _, v := range in.Labels {
			labels = append(labels, relationDB.UnsNamespaceLabelInfo{
				ID:   v.Id,
				Name: v.Name,
			})
		}

		// 6. 插入数据库
		node := &relationDB.UnsNamespaceNodeInfo{
			ID:                   id,
			IDPath:               curIDPath,
			Name:                 in.Name,
			DisplayName:          in.DisplayName,
			Description:          in.Description,
			Namespace:            curNamespace,
			NodeType:             int64(in.NodeType),
			ParentID:             in.ParentID,
			Definition:           definition,
			DataType:             int32(in.DataType),
			ExtendProperties:     extendProperties,
			Subscribe:            in.Subscribe,
			Persistence:          in.Persistence,
			Dashboard:            in.Dashboard,
			MockData:             in.MockData,
			TemplateID:           in.TemplateID,
			AggregationFrequency: in.AggregationFrequency,
			AggregationIDS:       in.AggregationTarget,
			// labels 多对多关联模型,传入对象id为0时会自动创建label对象
			Labels:      labels,
			Attachments: []relationDB.Attachment{},
		}

		if err := db.Insert(ctx, node); err != nil {
			return err
		}
		//  连带创建 模板
		if in.GenerateTemplate == 1 {
			tmplateDb := relationDB.NewNamespaceTemplateInfoRepo(tx)
			tid := l.svcCtx.NodeID.GetSnowflakeId()
			template := &relationDB.UnsNamespaceTemplateInfo{
				ID:         tid,
				Name:       in.Name,
				Definition: definition,
				NodeID:     id,
			}
			if err := tmplateDb.Insert(ctx, template); err != nil {
				logx.WithContext(ctx).Errorf("create Template err from create node:%v", err)
				return err
			}
		}
		return nil
	})
	if err != nil {
		logx.WithContext(ctx).Errorf("create node err:%v", err)
		return nil, err
	}
	return &uns.WithID{Id: id}, nil
}
