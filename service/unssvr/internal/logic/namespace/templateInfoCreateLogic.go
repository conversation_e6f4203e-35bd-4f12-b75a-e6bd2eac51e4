package namespacelogic

import (
	"context"
	"fmt"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"
	"gorm.io/gorm"

	"github.com/zeromicro/go-zero/core/logx"
)

type TemplateInfoCreateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewTemplateInfoCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TemplateInfoCreateLogic {
	return &TemplateInfoCreateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *TemplateInfoCreateLogic) TemplateInfoCreate(in *uns.NamespaceTemplateInfo) (*uns.WithID, error) {
	ctx := context.Background()
	var id int64
	err := stores.GetCommonConn(ctx).Transaction(func(tx *gorm.DB) error {
		if in.Name == "" {
			return fmt.Errorf(" name empty")
		}
		db := relationDB.NewNamespaceTemplateInfoRepo(tx)

		// supos-ce可重复

		// // 检查唯一性
		// exist, err := db.FindOneByFilter(ctx, relationDB.NamespaceTemplateInfoFilter{Name: in.Name})
		// if err == nil && exist.ID > 0 {
		// 	return fmt.Errorf(" name重复")
		// }
		id = l.svcCtx.NodeID.GetSnowflakeId()
		definition := make([]relationDB.Definition, 0)
		for _, def := range in.Definition {
			definition = append(definition, relationDB.Definition{
				Name:        def.Name,
				Type:        def.Type,
				MaxLen:      int(def.MaxLen),
				Remark:      def.Remark,
				DisplayName: def.DisplayName,
				SystemField: def.SystemField,
			})
		}
		template := &relationDB.UnsNamespaceTemplateInfo{
			ID:          id,
			Name:        in.Name,
			Description: in.Description,
			Definition:  definition,
			NodeID:      in.NodeID,
		}

		if err := db.Insert(ctx, template); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &uns.WithID{Id: id}, nil
}
