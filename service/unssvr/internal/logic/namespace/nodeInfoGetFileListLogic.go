package namespacelogic

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/logic"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type NodeInfoGetFileListLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoGetFileListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoGetFileListLogic {
	return &NodeInfoGetFileListLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取指定命名空间下的文件列表
func (l *NodeInfoGetFileListLogic) NodeInfoGetFileList(in *uns.NamespaceNodeInfoGetFileListReq) (*uns.NamespaceNodeInfoGetFileListResp, error) {
	db := relationDB.NewNamespaceNodeInfoRepo(stores.GetCommonConn(l.ctx))

	// 1. 查询所有节点
	nodes := make([]*relationDB.UnsNamespaceNodeInfo, 0)
	filter := relationDB.NamespaceNodeInfoFilter{
		Name:       in.Name,
		NodeType:   in.NodeType,
		TemplateID: in.TemplateID,
		LabelID:    in.LabelID,
	}

	page := logic.ToPageInfoWithDefault(in.Page, &defaultPageInfo)

	nodes, err := db.FindByFilter(l.ctx, filter, page)
	if err != nil {
		return nil, err
	}
	count, err := db.CountByFilter(l.ctx, filter)
	if err != nil {
		return nil, err
	}
	// 2. 构建id->node映射
	nodeMap := make(map[int64]*uns.NamespaceNodeInfo)
	for _, n := range nodes {
		pb := NodeInfoPoToPb(l.ctx, l.svcCtx, n, false)
		nodeMap[n.ID] = pb
	}
	resp := &uns.NamespaceNodeInfoGetFileListResp{
		Total: count,
		List:  make([]*uns.NamespaceNodeInfo, 0, len(nodeMap)),
	}
	// 用切片顺序遍历
	for _, v := range nodes {
		node := nodeMap[v.ID]
		resp.List = append(resp.List, node)
	}
	return resp, nil
}
