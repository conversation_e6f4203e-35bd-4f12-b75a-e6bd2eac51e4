package namespacelogic

import (
	"context"
	"fmt"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type LabelInfoCreateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewLabelInfoCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LabelInfoCreateLogic {
	return &LabelInfoCreateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *LabelInfoCreateLogic) LabelInfoCreate(in *uns.NamespaceLabelInfo) (*uns.Empty, error) {
	ctx := context.Background()
	err := stores.GetCommonConn(ctx).Transaction(func(tx *gorm.DB) error {
		if in.Name == "" {
			return fmt.Errorf("label name empty")
		}
		db := relationDB.NewNamespaceLabelInfoRepo(tx)

		// 检查唯一性
		exist, err := db.FindOneByFilter(ctx, relationDB.NamespaceLabelInfoFilter{Name: in.Name})
		if err == nil && exist.ID > 0 {
			return fmt.Errorf("label name重复")
		}

		label := &relationDB.UnsNamespaceLabelInfo{
			Name: in.Name,
		}

		if err := db.Insert(ctx, label); err != nil {
			return err
		}
		logx.Infof("LabelInfoCreate success:name= %s,id=%d", label.Name, label.ID)
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &uns.Empty{}, nil
}
