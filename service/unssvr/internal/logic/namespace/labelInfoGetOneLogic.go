package namespacelogic

import (
	"context"
	"fmt"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type LabelInfoGetOneLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewLabelInfoGetOneLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LabelInfoGetOneLogic {
	return &LabelInfoGetOneLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *LabelInfoGetOneLogic) LabelInfoGetOne(in *uns.WithID) (*uns.NamespaceLabelInfo, error) {
	db := relationDB.NewNamespaceLabelInfoRepo(stores.GetCommonConn(l.ctx))
	label, err := db.FindOneByFilter(l.ctx, relationDB.NamespaceLabelInfoFilter{ID: in.Id})
	if err != nil {
		return nil, fmt.Errorf("label not found")
	}
	// 组装返回
	return &uns.NamespaceLabelInfo{
		Id:          label.ID,
		Name:        label.Name,
		CreatedTime: label.CreatedTime.UnixMilli(),
		UpdatedTime: label.UpdatedTime.UnixMilli(),
	}, nil
}
