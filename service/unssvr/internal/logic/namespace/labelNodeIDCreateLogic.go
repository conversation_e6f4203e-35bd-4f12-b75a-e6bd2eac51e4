package namespacelogic

import (
	"context"
	"fmt"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"
	"gorm.io/gorm"

	"github.com/zeromicro/go-zero/core/logx"
)

type LabelNodeIDCreateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewLabelNodeIDCreateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LabelNodeIDCreateLogic {
	return &LabelNodeIDCreateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *LabelNodeIDCreateLogic) LabelNodeIDCreate(in *uns.NamespaceLabelNodeID) (*uns.Empty, error) {
	ctx := context.Background()
	err := stores.GetCommonConn(ctx).Transaction(func(tx *gorm.DB) error {
		if in.Id == 0 || in.NodeID == 0 {
			return fmt.Errorf("params error")
		}
		db := relationDB.NewNamespaceLabelNodeIDRepo(tx)

		// 检查唯一性
		exist, err := db.FindOneByFilter(ctx, relationDB.NamespaceLabelNodeIDFilter{LabelID: in.Id, NodeID: in.NodeID})
		if err == nil && exist.NodeID > 0 {
			return nil
		}

		// 校验labelid
		labelDB := relationDB.NewNamespaceLabelInfoRepo(stores.GetCommonConn(ctx))
		_, err = labelDB.FindOneByFilter(ctx, relationDB.NamespaceLabelInfoFilter{ID: in.Id})
		if err != nil {
			return fmt.Errorf("label not exist")
		}

		// 校验unsid
		nodeDB := relationDB.NewNamespaceNodeInfoRepo(stores.GetCommonConn(ctx))
		_, err = nodeDB.FindOneByFilter(ctx, relationDB.NamespaceNodeInfoFilter{ID: in.NodeID})
		if err != nil {
			return fmt.Errorf("node not exist")
		}
		label := &relationDB.UnsNamespaceLabelNodeID{
			LabelID: in.Id,
			NodeID:  in.NodeID,
		}

		if err := db.Insert(ctx, label); err != nil {
			return err
		}
		logx.Infof("LabelNodeIDCreate success:label_id=%d,uns_id=%d", label.LabelID, label.NodeID)
		return nil
	})
	if err != nil {
		return nil, err
	}

	return &uns.Empty{}, nil
}
