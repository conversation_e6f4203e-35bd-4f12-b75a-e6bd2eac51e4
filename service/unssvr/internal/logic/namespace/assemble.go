package namespacelogic

import (
	"context"

	"gitee.com/unitedrhino/share/oss"
	"gitee.com/unitedrhino/share/oss/common"
	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"
	"github.com/zeromicro/go-zero/core/logx"
)

var defaultPageInfo = stores.PageInfo{
	Page: 1,
	Size: 10,
	Orders: []stores.OrderBy{
		{
			Field: "created_time",
			Sort:  stores.OrderAsc,
		},
	},
}

func NodeInfoPoToPb(ctx context.Context, svcCtx *svc.ServiceContext, po *relationDB.UnsNamespaceNodeInfo, withAttachment bool) *uns.NamespaceNodeInfo {
	// definition 字段转换
	var pbDefs []*uns.NamespaceNodeInfoDefinition
	for _, def := range po.Definition {
		pbDefs = append(pbDefs, &uns.NamespaceNodeInfoDefinition{
			Name:        def.Name,
			Type:        def.Type,
			MaxLen:      int64(def.MaxLen),
			Remark:      def.Remark,
			DisplayName: def.DisplayName,
			SystemField: def.SystemField,
		})
	}
	var at []*uns.NamespaceNodeAttachment
	if withAttachment {
		for _, att := range po.Attachments {
			url, err := svcCtx.OssClient.PrivateBucket().SignedGetUrl(ctx, att.FilePath, 300, common.OptionKv{})
			if err != nil {
				logx.WithContext(ctx).Errorf("get url err:%v", err)
				continue
			}
			at = append(at, &uns.NamespaceNodeAttachment{
				Id:       att.ID,
				FileUrl:  url,
				FileName: oss.GetFileNameWithPath(att.FilePath),
			})
		}
	}

	return &uns.NamespaceNodeInfo{
		Id:                   po.ID,
		IdPath:               po.IDPath,
		ParentID:             po.ParentID,
		Name:                 po.Name,
		DisplayName:          po.DisplayName,
		Namespace:            po.Namespace,
		NodeType:             po.NodeType,
		Description:          po.Description,
		Definition:           pbDefs,
		DataType:             uns.DataTypeEnum(po.DataType),
		ExtendProperties:     po.ExtendProperties,
		Subscribe:            po.Subscribe,
		Persistence:          po.Persistence,
		Dashboard:            po.Dashboard,
		TemplateID:           po.TemplateID,
		AggregationFrequency: po.AggregationFrequency,
		AggregationTarget:    po.AggregationIDS,
		Children:             []*uns.NamespaceNodeInfo{},
		Attachments:          at,
		CreatedTime:          po.CreatedTime.UnixMilli(),
		UpdatedTime:          po.UpdatedTime.UnixMilli(),
	}
}
