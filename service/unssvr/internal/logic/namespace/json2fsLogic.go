package namespacelogic

import (
	"context"
	"encoding/json"
	"math"
	"sort"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type Json2fsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewJson2fsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *Json2fsLogic {
	return &Json2fsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// json转definition
func (l *Json2fsLogic) Json2Fs(in *uns.NamespaceJson2FsReq) (*uns.NamespaceJson2FsResp, error) {
	result, err := parseJsonToStructure(in.Json)
	if err != nil || len(result) == 0 {
		return nil, err
	}
	resp := &uns.NamespaceJson2FsResp{}
	for _, item := range result {
		tmp := uns.NamespaceJson2FsItems{
			DataPath: item.DataPath,
			Fields:   make([]*uns.NamespaceJson2FsFields, 0, len(item.Fields)),
		}
		for _, field := range item.Fields {
			tmp.Fields = append(tmp.Fields, &uns.NamespaceJson2FsFields{
				Name:        field.Name,
				Type:        field.Type,
				SystemField: field.SystemField,
			})
		}
		resp.Data = append(resp.Data, &tmp)
	}
	return resp, nil
}

type Field struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	SystemField bool   `json:"systemField"`
}

type DataItem struct {
	DataPath string  `json:"dataPath"`
	Fields   []Field `json:"fields"`
}

type Output []DataItem

func detectType(v interface{}) string {
	switch v := v.(type) {
	case bool:
		return "BOOLEAN"
	case float64:
		if math.Floor(v) == v {
			return "INTEGER"
		}
		return "DOUBLE"
	case string:
		return "STRING"
	case nil:
		return "STRING"
	default:
		return "STRING"
	}
}

func mergeFieldTypes(list []map[string]interface{}) map[string]string {
	fieldTypes := make(map[string]string)
	for _, obj := range list {
		for k, v := range obj {
			t := detectType(v)
			existing, ok := fieldTypes[k]
			if !ok || typePriority(t) > typePriority(existing) {
				fieldTypes[k] = t
			}
		}
	}
	return fieldTypes
}

func typePriority(t string) int {
	switch t {
	case "INTEGER":
		return 1
	case "DOUBLE":
		return 2
	case "BOOLEAN":
		return 3
	case "STRING":
		return 4
	default:
		return 0
	}
}

type ListResult struct {
	dataPath   string
	dataInList bool
	list       []map[string]interface{}
}

func findDataLists(value interface{}, path string) []ListResult {
	var results []ListResult

	switch v := value.(type) {
	case []interface{}:
		objs := make([]map[string]interface{}, 0)
		for _, item := range v {
			if m, ok := item.(map[string]interface{}); ok {
				objs = append(objs, m)
			}
		}
		if len(objs) > 0 {
			results = append(results, ListResult{
				dataPath:   path,
				dataInList: true,
				list:       objs,
			})
		}
		for _, item := range v {
			results = append(results, findDataLists(item, path)...)
		}
	case map[string]interface{}:
		results = append(results, ListResult{
			dataPath:   path,
			dataInList: false,
			list:       []map[string]interface{}{v},
		})
		for k, val := range v {
			subPath := k
			if path != "" {
				subPath = path + "." + k
			}
			results = append(results, findDataLists(val, subPath)...)
		}
	}
	return results
}

func mapToFields(r ListResult) *DataItem {
	fieldMap := mergeFieldTypes(r.list)
	fields := []Field{}
	for k, t := range fieldMap {
		fields = append(fields, Field{
			Name:        k,
			Type:        t,
			SystemField: false,
		})
	}
	if len(fields) == 0 {
		return nil
	}
	return &DataItem{
		DataPath: r.dataPath,
		Fields:   fields,
	}
}

func parseJsonToStructure(jsonStr string) ([]DataItem, error) {
	var root interface{}
	err := json.Unmarshal([]byte(jsonStr), &root)
	if err != nil {
		return nil, err
	}

	allResults := findDataLists(root, "")

	// 优先选择所有 dataInList 的
	var chosen []ListResult
	for _, r := range allResults {
		if r.dataInList {
			chosen = append(chosen, r)
		}
	}

	if len(chosen) == 0 {
		chosen = allResults
	}

	// 去重路径 -> 只保留一个结果 per path
	unique := make(map[string]*DataItem)
	for _, r := range chosen {
		item := mapToFields(r)
		if item != nil {
			unique[r.dataPath] = item
		}
	}

	// 按路径排序可加，可选
	result := []DataItem{}
	for _, item := range unique {
		result = append(result, *item)
	}

	// 按 dataPath 排序
	sort.Slice(result, func(i, j int) bool {
		return result[i].DataPath < result[j].DataPath
	})

	return result, nil
}
