package namespacelogic

import (
	"context"
	"fmt"

	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type NodeInfoDeleteLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewNodeInfoDeleteLogic(ctx context.Context, svcCtx *svc.ServiceContext) *NodeInfoDeleteLogic {
	return &NodeInfoDeleteLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *NodeInfoDeleteLogic) NodeInfoDelete(in *uns.WithID) (*uns.Empty, error) {
	ctx := context.Background()

	err := stores.GetCommonConn(ctx).Transaction(func(tx *gorm.DB) error {
		if in.Id < 1 {
			return fmt.Errorf("params error")
		}
		db := relationDB.NewNamespaceNodeInfoRepo(tx)
		// 1. 检查节点是否存在
		node, err := db.FindOneByFilter(ctx, relationDB.NamespaceNodeInfoFilter{ID: in.Id})
		if err != nil {
			return fmt.Errorf("节点不存在")
		}
		_, err = db.FindOneByFilter(ctx, relationDB.NamespaceNodeInfoFilter{ParentID: in.Id})
		if err == nil {
			return fmt.Errorf("有子节点不能删除")
		}
		// 2. 删除节点
		if err := db.Delete(ctx, node.ID); err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &uns.Empty{}, nil
}
