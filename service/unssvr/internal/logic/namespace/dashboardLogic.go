package namespacelogic

import (
	"context"
	"time"

	"gitee.com/unitedrhino/share/errors"
	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"

	"github.com/zeromicro/go-zero/core/logx"
)

type DashboardLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDashboardLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DashboardLogic {
	return &DashboardLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// dashboard
func (l *DashboardLogic) Dashboard(in *uns.DashboardReq) (*uns.DashboardResp, error) {
	if in.NodeID == 0 {
		return nil, errors.Parameter
	}
	// 将前端传来的时间戳（秒）转换为 time.Time 类型
	startTime := time.Unix(in.TimeStart, 0) // 转换为时间（秒）
	endTime := time.Unix(in.TimeEnd, 0)     // 转换为时间（秒）

	db := relationDB.NewTimeNamespaceRecordRepo(l.ctx)

	// 查询数据库获取符合条件的记录
	filter := relationDB.TimeNamespaceRecordFilter{
		NodeID:            in.NodeID,
		TimestampsBetween: []time.Time{startTime, endTime},
	}
	page := &stores.PageInfo{
		Orders: []stores.OrderBy{
			{
				Field: "timestamp",
				Sort:  stores.OrderAsc,
			},
		},
	}
	res, err := db.FindByFilter(l.ctx, filter, page)
	if err != nil {
		return nil, err
	}
	nodeRepo := relationDB.NewNamespaceNodeInfoRepo(l.ctx)
	nodeInfo, err := nodeRepo.FindOneByFilter(l.ctx, relationDB.NamespaceNodeInfoFilter{ID: in.NodeID})
	if err != nil {
		return nil, err
	}
	resp := uns.DashboardResp{}
	definition := make([]*uns.NamespaceNodeInfoDefinition, 0)
	for _, v := range nodeInfo.Definition {
		definition = append(definition, &uns.NamespaceNodeInfoDefinition{
			Name:        v.Name,
			Type:        v.Type,
			MaxLen:      int64(v.MaxLen),
			Remark:      v.Remark,
			DisplayName: v.DisplayName,
			SystemField: v.SystemField,
			Unit:        v.Unit,
		})
	}
	resp.Fields = definition
	for _, v := range res {
		// var payloadMap map[string]any
		// err := json.Unmarshal(v.Payload, &payloadMap)
		// if err != nil {
		// 	continue
		// }
		item := uns.DashboardItem{
			Timestamp: v.Timestamp.Unix(),
			Payload:   v.Payload.String(),
		}
		resp.List = append(resp.List, &item)
	}
	return &resp, nil

}
