package config

import (
	"gitee.com/unitedrhino/share/conf"
	"github.com/zeromicro/go-zero/core/stores/cache"
	"github.com/zeromicro/go-zero/zrpc"
)

type Config struct {
	zrpc.RpcServerConf
	Database    conf.Database
	CacheRedis  cache.ClusterConf
	DevLink     conf.DevLinkConf //和设备交互的设置
	OssConf     conf.OssConf     `json:",optional"`
	AwsS3tables AwsS3tables      `json:",optional"`
}

type AwsS3tables struct {
	AccessKeyID     string `json:",env=AWS_ACCESS_KEY_ID"`
	SecretAccessKey string `json:",env=AWS_SECRET_ACCESS_KEY"`
	Bucket          string `json:",env=AWS_S3BUCKET"`
	Region          string `json:",env=AWS_REGION"`
}
