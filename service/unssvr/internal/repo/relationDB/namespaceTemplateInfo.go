package relationDB

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type NamespaceTemplateInfoRepo struct {
	db *gorm.DB
}

func NewNamespaceTemplateInfoRepo(in any) *NamespaceTemplateInfoRepo {
	return &NamespaceTemplateInfoRepo{db: stores.GetCommonConn(in)}
}

type NamespaceTemplateInfoFilter struct {
	//todo 添加过滤字段
	ID   int64
	Name string
}

func (p NamespaceTemplateInfoRepo) fmtFilter(ctx context.Context, f NamespaceTemplateInfoFilter) *gorm.DB {
	db := p.db.WithContext(ctx)
	//todo 添加条件
	if f.ID != 0 {
		db = db.Where("id = ?", f.ID)
	}
	if f.Name != "" {
		db = db.Where("name = ?", f.ID)
	}
	return db
}

func (p NamespaceTemplateInfoRepo) Insert(ctx context.Context, data *UnsNamespaceTemplateInfo) error {
	result := p.db.WithContext(ctx).Create(data)
	return stores.ErrFmt(result.Error)
}

func (p NamespaceTemplateInfoRepo) FindOneByFilter(ctx context.Context, f NamespaceTemplateInfoFilter) (*UnsNamespaceTemplateInfo, error) {
	var result UnsNamespaceTemplateInfo
	db := p.fmtFilter(ctx, f)
	err := db.First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}
func (p NamespaceTemplateInfoRepo) FindByFilter(ctx context.Context, f NamespaceTemplateInfoFilter, page *stores.PageInfo) ([]*UnsNamespaceTemplateInfo, error) {
	var results []*UnsNamespaceTemplateInfo
	db := p.fmtFilter(ctx, f).Model(&UnsNamespaceTemplateInfo{})
	db = page.ToGorm(db)
	err := db.Find(&results).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return results, nil
}

func (p NamespaceTemplateInfoRepo) CountByFilter(ctx context.Context, f NamespaceTemplateInfoFilter) (size int64, err error) {
	db := p.fmtFilter(ctx, f).Model(&UnsNamespaceTemplateInfo{})
	err = db.Count(&size).Error
	return size, stores.ErrFmt(err)
}

func (p NamespaceTemplateInfoRepo) Update(ctx context.Context, data *UnsNamespaceTemplateInfo) error {
	err := p.db.WithContext(ctx).Where("id = ?", data.ID).Save(data).Error
	return stores.ErrFmt(err)
}

func (p NamespaceTemplateInfoRepo) DeleteByFilter(ctx context.Context, f NamespaceTemplateInfoFilter) error {
	db := p.fmtFilter(ctx, f)
	err := db.Delete(&UnsNamespaceTemplateInfo{}).Error
	return stores.ErrFmt(err)
}

func (p NamespaceTemplateInfoRepo) Delete(ctx context.Context, id int64) error {
	err := p.db.WithContext(ctx).Where("id = ?", id).Delete(&UnsNamespaceTemplateInfo{}).Error
	return stores.ErrFmt(err)
}
func (p NamespaceTemplateInfoRepo) FindOne(ctx context.Context, id int64) (*UnsNamespaceTemplateInfo, error) {
	var result UnsNamespaceTemplateInfo
	err := p.db.WithContext(ctx).Where("id = ?", id).First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}

// 批量插入 LightStrategyDevice 记录
func (p NamespaceTemplateInfoRepo) MultiInsert(ctx context.Context, data []*UnsNamespaceTemplateInfo) error {
	err := p.db.WithContext(ctx).Clauses(clause.OnConflict{UpdateAll: true}).Model(&UnsNamespaceTemplateInfo{}).Create(data).Error
	return stores.ErrFmt(err)
}

func (d NamespaceTemplateInfoRepo) UpdateWithField(ctx context.Context, f NamespaceTemplateInfoFilter, updates map[string]any) error {
	db := d.fmtFilter(ctx, f)
	err := db.Model(&UnsNamespaceTemplateInfo{}).Updates(updates).Error
	return stores.ErrFmt(err)
}
