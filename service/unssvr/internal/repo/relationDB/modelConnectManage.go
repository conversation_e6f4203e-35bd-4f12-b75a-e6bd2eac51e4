package relationDB

import (
	"time"

	"gitee.com/unitedrhino/share/stores"
	"gorm.io/datatypes"
)

// UnsConnectNodeInfo 边缘节点信息
type UnsConnectNodeInfo struct {
	ID          int64   `gorm:"column:id;type:bigint;primary_key;comment:id编号雪花算法生成"`     // id编号
	Name        string  `gorm:"column:name;type:varchar(255);not null;comment:节点名称"`      // 节点名称
	Description string  `gorm:"column:description;type:text;comment:节点描述"`                // 节点描述
	AuthMethod  int64   `gorm:"column:auth_method;type:int;comment:'认证方式: 1-密钥认证,2-自定义'"` // 认证方式: 1-密钥认证,2-自定义
	SecretKey   string  `gorm:"column:secret_key;type:varchar(255);comment:密钥"`           // 密钥
	Username    string  `gorm:"column:username;type:varchar(64);comment:用户名"`             // 用户名
	Password    string  `gorm:"column:password;type:varchar(255);comment:密码"`             // 密码
	ClientID    string  `gorm:"column:client_id;type:varchar(64);comment:客户端ID"`          // 客户端ID
	CPU         float32 `gorm:"column:cpu;type:float;comment:CPU信息"`                      // CPU信息
	Memory      float32 `gorm:"column:memory;type:float;comment:内存信息"`                    // 内存信息
	Msg         int64   `gorm:"column:msg;type:bigint;comment:消息数量"`                      // 消息数量
	Version     string  `gorm:"column:version;type:varchar(64);comment:版本信息"`             // 版本信息
	stores.NoDelTime
	DeletedTime stores.DeletedTime `gorm:"column:deleted_time;default:0;uniqueIndex:idx_connect_eage_node_info_id;comment:删除时间"`
}

func (UnsConnectNodeInfo) TableName() string {
	return "uns_connect_node_info"
}

type UnsTimeNamespaceRecord struct {
	ID        int64          `gorm:"column:id;primaryKey;autoIncrement"`
	NodeID    int64          `gorm:"column:node_id;type:bigint;not null;index:idx_time_namespace_record_nodeid_timestamp;comment:nodeID;"` // 关联的nodeID
	Timestamp time.Time      `gorm:"column:timestamp;type:timestamptz;not null;index:idx_time_namespace_record_nodeid_timestamp;comment:时间戳"`
	Payload   datatypes.JSON `gorm:"column:payload;type:jsonb;comment:报文"`
}

// TableName sets the insert table name for this struct type
func (UnsTimeNamespaceRecord) TableName() string {
	return "time_namespace_record"
}
