package relationDB

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

/*
这个是参考样例
使用教程:
1. 将NamespaceLabelNodeID全局替换为模型的表名
2. 完善todo
*/

type NamespaceLabelNodeIDRepo struct {
	db *gorm.DB
}

func NewNamespaceLabelNodeIDRepo(in any) *NamespaceLabelNodeIDRepo {
	return &NamespaceLabelNodeIDRepo{db: stores.GetCommonConn(in)}
}

type NamespaceLabelNodeIDFilter struct {
	//todo 添加过滤字段
	LabelID int64
	NodeID  int64
}

func (p NamespaceLabelNodeIDRepo) fmtFilter(ctx context.Context, f NamespaceLabelNodeIDFilter) *gorm.DB {
	db := p.db.WithContext(ctx)
	db.Where("label_id > 0")
	db.Where("node_id > 0")
	if f.LabelID != 0 {
		db = db.Where("label_id = ?", f.LabelID)
	}
	if f.NodeID != 0 {
		db = db.Where("node_id = ?", f.NodeID)
	}
	return db
}

func (p NamespaceLabelNodeIDRepo) Insert(ctx context.Context, data *UnsNamespaceLabelNodeID) error {
	result := p.db.WithContext(ctx).Create(data)
	return stores.ErrFmt(result.Error)
}

func (p NamespaceLabelNodeIDRepo) FindOneByFilter(ctx context.Context, f NamespaceLabelNodeIDFilter) (*UnsNamespaceLabelNodeID, error) {
	var result UnsNamespaceLabelNodeID
	db := p.fmtFilter(ctx, f)
	err := db.First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}
func (p NamespaceLabelNodeIDRepo) FindByFilter(ctx context.Context, f NamespaceLabelNodeIDFilter, page *stores.PageInfo) ([]*UnsNamespaceLabelNodeID, error) {
	var results []*UnsNamespaceLabelNodeID
	db := p.fmtFilter(ctx, f).Model(&UnsNamespaceLabelNodeID{})
	db = page.ToGorm(db)
	err := db.Find(&results).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return results, nil
}

func (p NamespaceLabelNodeIDRepo) CountByFilter(ctx context.Context, f NamespaceLabelNodeIDFilter) (size int64, err error) {
	db := p.fmtFilter(ctx, f).Model(&UnsNamespaceLabelNodeID{})
	err = db.Count(&size).Error
	return size, stores.ErrFmt(err)
}

// func (p NamespaceLabelNodeIDRepo) Update(ctx context.Context, data *UnsNamespaceLabelNodeID) error {
// 	err := p.db.WithContext(ctx).Where("id = ?", data.ID).Save(data).Error
// 	return stores.ErrFmt(err)
// }

func (p NamespaceLabelNodeIDRepo) DeleteByFilter(ctx context.Context, f NamespaceLabelNodeIDFilter) error {
	db := p.fmtFilter(ctx, f)
	err := db.Delete(&UnsNamespaceLabelNodeID{}).Error
	return stores.ErrFmt(err)
}

func (p NamespaceLabelNodeIDRepo) Delete(ctx context.Context, id int64) error {
	err := p.db.WithContext(ctx).Where("id = ?", id).Delete(&UnsNamespaceLabelNodeID{}).Error
	return stores.ErrFmt(err)
}
func (p NamespaceLabelNodeIDRepo) FindOne(ctx context.Context, id int64) (*UnsNamespaceLabelNodeID, error) {
	var result UnsNamespaceLabelNodeID
	err := p.db.WithContext(ctx).Where("id = ?", id).First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}

// 批量插入 LightStrategyDevice 记录
func (p NamespaceLabelNodeIDRepo) MultiInsert(ctx context.Context, data []*UnsNamespaceLabelNodeID) error {
	err := p.db.WithContext(ctx).Clauses(clause.OnConflict{UpdateAll: true}).Model(&UnsNamespaceLabelNodeID{}).Create(data).Error
	return stores.ErrFmt(err)
}

func (d NamespaceLabelNodeIDRepo) UpdateWithField(ctx context.Context, f NamespaceLabelNodeIDFilter, updates map[string]any) error {
	db := d.fmtFilter(ctx, f)
	err := db.Model(&UnsNamespaceLabelNodeID{}).Updates(updates).Error
	return stores.ErrFmt(err)
}
