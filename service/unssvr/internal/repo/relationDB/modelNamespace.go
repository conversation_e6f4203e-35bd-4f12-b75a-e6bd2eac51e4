package relationDB

import "gitee.com/unitedrhino/share/stores"

// 示例
type UnsExample struct {
	ID    int64 `gorm:"column:id;type:bigint;primary_key;AUTO_INCREMENT"`    // id编号
	alias int64 `gorm:"column:alias;type:bigint;primary_key;AUTO_INCREMENT"` // 别名
}

// 文件
type UnsNamespaceNodeInfo struct {
	ID                   int64                   `gorm:"column:id;primaryKey;type:bigint;"`
	IDPath               string                  `gorm:"column:id_path;index;comment:路径ID"`
	Name                 string                  `gorm:"column:name;comment:名称;"`
	DisplayName          string                  `gorm:"column:display_name;comment:显示名称"`
	Description          string                  `gorm:"column:description;comment:描述"`
	Namespace            string                  `gorm:"column:namespace;uniqueIndex:idx_namespace_node_info_namespace;comment:'命名空间,namepath';"`
	NodeType             int64                   `gorm:"column:node_type;comment:路径类型1文件夹2文件"`
	ParentID             int64                   `gorm:"column:parent_id;comment:父节点id"`
	Definition           []Definition            `gorm:"column:definition;type:json;serializer:json;comment:字段定义"`
	DataType             int32                   `gorm:"column:data_type;comment:'数据类型1-时序 2-关系 3-计算 4-聚合 5-引用'"`
	ExtendProperties     map[string]string       `gorm:"column:extendproperties;type:json;serializer:json"`
	Subscribe            int64                   `gorm:"column:subscribe;comment:订阅1是2否"`
	Persistence          int64                   `gorm:"column:persistence;comment:持久化1是2否"`
	Dashboard            int64                   `gorm:"column:dashboard;comment:生成仪表盘1是2否"`
	MockData             int64                   `gorm:"column:mock_data;comment:生成模拟数据1是2否"`
	TemplateID           int64                   `gorm:"column:template_id;comment:引用的模板ID"`
	AggregationFrequency string                  `gorm:"column:aggregation_frequency;comment:'聚合频率天(d)时(h)、分(m)、秒(s)'"`
	AggregationIDS       []int64                 `gorm:"column:aggregation_ids;type:json;serializer:json;comment:聚合ID"`
	Labels               []UnsNamespaceLabelInfo `gorm:"many2many:uns_namespace_label_nodeid;foreignKey:ID;joinForeignKey:NodeID;References:ID;joinReferences:LabelID"`
	Attachments          []Attachment            `gorm:"column:attachments;type:json;serializer:json;comment:附件"`
	RecycleIsDel         int8                    `gorm:"column:recycle_is_del;default:2;comment:回收站是否删除1是2否"`
	stores.NoDelTime
	DeletedTime stores.DeletedTime `gorm:"column:deleted_time;default:0;uniqueIndex:idx_namespace_node_info_namespace;"`
}

func (m *UnsNamespaceNodeInfo) TableName() string {
	return "uns_namespace_node_info"
}

type Attachment struct {
	ID       int64  `json:"id"`
	FilePath string `json:"filePath"`
}

type Definition struct {
	Name        string `json:"name"`
	Type        string `json:"type"` // enum(INTEGER | LONG | …)
	MaxLen      int    `json:"maxLen"`
	Remark      string `json:"remark"`
	DisplayName string `json:"displayName"`
	SystemField int64  `json:"systemField"`
	Unit        string `json:"unit"`
}

type UnsNamespaceNodeAttachment struct {
	ID             int64  `gorm:"column:id;primaryKey;autoIncrement;"`
	NodeID         int64  `gorm:"column:node_id;comment:'关联的nodeID'"`
	OriginalName   string `gorm:"column:original_name;comment:原始文件名"`
	AttachmentName string `gorm:"column:attachment_name;comment:生成的文件名"`
	AttachmentPath string `gorm:"column:attachment_path;comment:路径"`
	ExtensionName  string `gorm:"column:extension_name;comment:文件后缀"`
	stores.SoftTime
}

// TableName sets the insert table name for this struct type
func (UnsNamespaceNodeAttachment) TableName() string {
	return "uns_namespace_node_attachment"
}

type UnsNamespaceTemplateInfo struct {
	ID          int64        `gorm:"primaryKey;autoIncrement;comment:模板ID"`
	Name        string       `gorm:"size:128;comment:模板名称"`
	NodeID      int64        `gorm:"column:node_id;comment:'关联的nodeID'"`
	Description string       `gorm:"size:512;comment:模板描述"`
	Definition  []Definition `gorm:"type:json;serializer:json;comment:模板定义"`
	stores.SoftTime
}

// TableName sets the insert table name for this struct type
func (UnsNamespaceTemplateInfo) TableName() string {
	return "uns_namespace_template_info"
}

type UnsNamespaceLabelInfo struct {
	ID    int64                  `gorm:"column:id;type:bigint;primaryKey;autoIncrement"` // 标签ID
	Name  string                 `gorm:"size:128;uniqueIndex:idx_namespace_label_name;comment:标签名称"`
	Nodes []UnsNamespaceNodeInfo `gorm:"many2many:uns_namespace_label_nodeid;foreignKey:ID;joinForeignKey:LabelID;References:ID;joinReferences:NodeID"`
	stores.NoDelTime
	DeletedTime stores.DeletedTime `gorm:"column:deleted_time;default:0;uniqueIndex:idx_namespace_label_name"`
}

// TableName sets the insert table name for this struct type
func (UnsNamespaceLabelInfo) TableName() string {
	return "uns_namespace_label_info"
}

type UnsNamespaceLabelNodeID struct {
	LabelID int64 `gorm:"column:label_id;primaryKey;comment:labelID;"` // 标签ID
	NodeID  int64 `gorm:"column:node_id;primaryKey;comment:nodeID;"`   // 关联的nodeID
	stores.OnlyTime
}

// TableName sets the insert table name for this struct type
func (UnsNamespaceLabelNodeID) TableName() string {
	return "uns_namespace_label_nodeid"
}
