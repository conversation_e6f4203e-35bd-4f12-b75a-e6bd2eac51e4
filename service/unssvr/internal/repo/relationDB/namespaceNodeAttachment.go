package relationDB

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type NamespaceNodeAttachmentRepo struct {
	db *gorm.DB
}

func NewNamespaceNodeAttachmentRepo(in any) *NamespaceNodeAttachmentRepo {
	return &NamespaceNodeAttachmentRepo{db: stores.GetCommonConn(in)}
}

type NamespaceNodeAttachmentFilter struct {
	//todo 添加过滤字段
	ID     int64
	NodeID int64
}

func (p NamespaceNodeAttachmentRepo) fmtFilter(ctx context.Context, f NamespaceNodeAttachmentFilter) *gorm.DB {
	db := p.db.WithContext(ctx)
	//todo 添加条件
	if f.ID != 0 {
		db = db.Where("id = ?", f.ID)
	}
	if f.NodeID != 0 {
		db = db.Where("node_id = ?", f.NodeID)
	}
	return db
}

func (p NamespaceNodeAttachmentRepo) Insert(ctx context.Context, data *UnsNamespaceNodeAttachment) error {
	result := p.db.WithContext(ctx).Create(data)
	return stores.ErrFmt(result.Error)
}

func (p NamespaceNodeAttachmentRepo) FindOneByFilter(ctx context.Context, f NamespaceNodeAttachmentFilter) (*UnsNamespaceNodeAttachment, error) {
	var result UnsNamespaceNodeAttachment
	db := p.fmtFilter(ctx, f)
	err := db.First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}
func (p NamespaceNodeAttachmentRepo) FindByFilter(ctx context.Context, f NamespaceNodeAttachmentFilter, page *stores.PageInfo) ([]*UnsNamespaceNodeAttachment, error) {
	var results []*UnsNamespaceNodeAttachment
	db := p.fmtFilter(ctx, f).Model(&UnsNamespaceNodeAttachment{})
	db = page.ToGorm(db)
	err := db.Find(&results).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return results, nil
}

func (p NamespaceNodeAttachmentRepo) CountByFilter(ctx context.Context, f NamespaceNodeAttachmentFilter) (size int64, err error) {
	db := p.fmtFilter(ctx, f).Model(&UnsNamespaceNodeAttachment{})
	err = db.Count(&size).Error
	return size, stores.ErrFmt(err)
}

func (p NamespaceNodeAttachmentRepo) Update(ctx context.Context, data *UnsNamespaceNodeAttachment) error {
	err := p.db.WithContext(ctx).Where("id = ?", data.ID).Save(data).Error
	return stores.ErrFmt(err)
}

func (p NamespaceNodeAttachmentRepo) DeleteByFilter(ctx context.Context, f NamespaceNodeAttachmentFilter) error {
	db := p.fmtFilter(ctx, f)
	err := db.Delete(&UnsNamespaceNodeAttachment{}).Error
	return stores.ErrFmt(err)
}

func (p NamespaceNodeAttachmentRepo) Delete(ctx context.Context, id int64) error {
	err := p.db.WithContext(ctx).Where("id = ?", id).Delete(&UnsNamespaceNodeAttachment{}).Error
	return stores.ErrFmt(err)
}
func (p NamespaceNodeAttachmentRepo) FindOne(ctx context.Context, id int64) (*UnsNamespaceNodeAttachment, error) {
	var result UnsNamespaceNodeAttachment
	err := p.db.WithContext(ctx).Where("id = ?", id).First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}

// 批量插入 LightStrategyDevice 记录
func (p NamespaceNodeAttachmentRepo) MultiInsert(ctx context.Context, data []*UnsNamespaceNodeAttachment) error {
	err := p.db.WithContext(ctx).Clauses(clause.OnConflict{UpdateAll: true}).Model(&UnsNamespaceNodeAttachment{}).Create(data).Error
	return stores.ErrFmt(err)
}

func (d NamespaceNodeAttachmentRepo) UpdateWithField(ctx context.Context, f NamespaceNodeAttachmentFilter, updates map[string]any) error {
	db := d.fmtFilter(ctx, f)
	err := db.Model(&UnsNamespaceNodeAttachment{}).Updates(updates).Error
	return stores.ErrFmt(err)
}
