package relationDB

import (
	"context"
	"time"

	"gitee.com/unitedrhino/share/stores"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

/*
这个是参考样例
使用教程:
1. 将example全局替换为模型的表名
2. 完善todo
*/

type TimeNamespaceRecordRepo struct {
	db          *gorm.DB
	asyncInsert *stores.AsyncInsert[UnsTimeNamespaceRecord]
}

func NewTimeNamespaceRecordRepo(in any) *TimeNamespaceRecordRepo {
	return &TimeNamespaceRecordRepo{
		db:          stores.GetCommonConn(in),
		asyncInsert: stores.NewAsyncInsert[UnsTimeNamespaceRecord](stores.GetCommonConn(in), ""),
	}
}

func (p TimeNamespaceRecordRepo) AsyncInsert(data UnsTimeNamespaceRecord) {
	p.asyncInsert.AsyncInsert(&data)
}

type TimeNamespaceRecordFilter struct {
	NodeID            int64
	TimestampsBetween []time.Time
	IDGT              int64
}

func (p TimeNamespaceRecordRepo) fmtFilter(ctx context.Context, f TimeNamespaceRecordFilter) *gorm.DB {
	db := p.db.WithContext(ctx)
	if len(f.TimestampsBetween) == 2 {
		db = db.Where("timestamp between ? and ?", f.TimestampsBetween[0], f.TimestampsBetween[1])
	}
	if f.NodeID != 0 {
		db = db.Where("node_id = ?", f.NodeID)
	}
	if f.IDGT != 0 {
		db = db.Where("id > ?", f.IDGT)
	}
	return db
}

func (p TimeNamespaceRecordRepo) Insert(ctx context.Context, data *UnsTimeNamespaceRecord) error {
	result := p.db.WithContext(ctx).Create(data)
	return stores.ErrFmt(result.Error)
}

func (p TimeNamespaceRecordRepo) FindOneByFilter(ctx context.Context, f TimeNamespaceRecordFilter) (*UnsTimeNamespaceRecord, error) {
	var result UnsTimeNamespaceRecord
	db := p.fmtFilter(ctx, f)
	err := db.First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}
func (p TimeNamespaceRecordRepo) FindByFilter(ctx context.Context, f TimeNamespaceRecordFilter, page *stores.PageInfo) ([]*UnsTimeNamespaceRecord, error) {
	var results []*UnsTimeNamespaceRecord
	db := p.fmtFilter(ctx, f).Model(&UnsTimeNamespaceRecord{})
	db = page.ToGorm(db)
	err := db.Find(&results).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return results, nil
}

func (p TimeNamespaceRecordRepo) CountByFilter(ctx context.Context, f TimeNamespaceRecordFilter) (size int64, err error) {
	db := p.fmtFilter(ctx, f).Model(&UnsTimeNamespaceRecord{})
	err = db.Count(&size).Error
	return size, stores.ErrFmt(err)
}

func (p TimeNamespaceRecordRepo) Update(ctx context.Context, data *UnsTimeNamespaceRecord) error {
	err := p.db.WithContext(ctx).Where("id = ?", data.ID).Save(data).Error
	return stores.ErrFmt(err)
}

func (p TimeNamespaceRecordRepo) DeleteByFilter(ctx context.Context, f TimeNamespaceRecordFilter) error {
	db := p.fmtFilter(ctx, f)
	err := db.Delete(&UnsTimeNamespaceRecord{}).Error
	return stores.ErrFmt(err)
}

func (p TimeNamespaceRecordRepo) Delete(ctx context.Context, id int64) error {
	err := p.db.WithContext(ctx).Where("id = ?", id).Delete(&UnsTimeNamespaceRecord{}).Error
	return stores.ErrFmt(err)
}
func (p TimeNamespaceRecordRepo) FindOne(ctx context.Context, id int64) (*UnsTimeNamespaceRecord, error) {
	var result UnsTimeNamespaceRecord
	err := p.db.WithContext(ctx).Where("id = ?", id).First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}

// 批量插入 LightStrategyDevice 记录
func (p TimeNamespaceRecordRepo) MultiInsert(ctx context.Context, data []*UnsTimeNamespaceRecord) error {
	err := p.db.WithContext(ctx).Clauses(clause.OnConflict{UpdateAll: true}).Model(&UnsTimeNamespaceRecord{}).Create(data).Error
	return stores.ErrFmt(err)
}

func (d TimeNamespaceRecordRepo) UpdateWithField(ctx context.Context, f TimeNamespaceRecordFilter, updates map[string]any) error {
	db := d.fmtFilter(ctx, f)
	err := db.Model(&UnsTimeNamespaceRecord{}).Updates(updates).Error
	return stores.ErrFmt(err)
}
