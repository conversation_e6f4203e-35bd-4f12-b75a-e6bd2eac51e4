package relationDB

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type NamespaceLabelInfoRepo struct {
	db *gorm.DB
}

func NewNamespaceLabelInfoRepo(in any) *NamespaceLabelInfoRepo {
	return &NamespaceLabelInfoRepo{db: stores.GetCommonConn(in)}
}

type NamespaceLabelInfoFilter struct {
	//todo 添加过滤字段
	ID   int64
	Name string
	IDS  []int64
}

func (p NamespaceLabelInfoRepo) fmtFilter(ctx context.Context, f NamespaceLabelInfoFilter) *gorm.DB {
	db := p.db.WithContext(ctx)
	//todo 添加条件
	if f.ID != 0 {
		db = db.Where("id = ?", f.ID)
	}
	if f.Name != "" {
		db = db.Where("name = ?", f.Name)
	}
	if len(f.IDS) > 0 {
		db = db.Where("id", f.IDS)
	}
	return db
}

func (p NamespaceLabelInfoRepo) Insert(ctx context.Context, data *UnsNamespaceLabelInfo) error {
	result := p.db.WithContext(ctx).Create(data)
	return stores.ErrFmt(result.Error)
}

func (p NamespaceLabelInfoRepo) FindOneByFilter(ctx context.Context, f NamespaceLabelInfoFilter) (*UnsNamespaceLabelInfo, error) {
	var result UnsNamespaceLabelInfo
	db := p.fmtFilter(ctx, f)
	err := db.First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}
func (p NamespaceLabelInfoRepo) FindByFilter(ctx context.Context, f NamespaceLabelInfoFilter, page *stores.PageInfo) ([]*UnsNamespaceLabelInfo, error) {
	var results []*UnsNamespaceLabelInfo
	db := p.fmtFilter(ctx, f).Model(&UnsNamespaceLabelInfo{})
	db = page.ToGorm(db)
	err := db.Find(&results).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return results, nil
}

func (p NamespaceLabelInfoRepo) CountByFilter(ctx context.Context, f NamespaceLabelInfoFilter) (size int64, err error) {
	db := p.fmtFilter(ctx, f).Model(&UnsNamespaceLabelInfo{})
	err = db.Count(&size).Error
	return size, stores.ErrFmt(err)
}

func (p NamespaceLabelInfoRepo) Update(ctx context.Context, data *UnsNamespaceLabelInfo) error {
	err := p.db.WithContext(ctx).Where("id = ?", data.ID).Save(data).Error
	return stores.ErrFmt(err)
}

func (p NamespaceLabelInfoRepo) DeleteByFilter(ctx context.Context, f NamespaceLabelInfoFilter) error {
	db := p.fmtFilter(ctx, f)
	err := db.Delete(&UnsNamespaceLabelInfo{}).Error
	return stores.ErrFmt(err)
}

func (p NamespaceLabelInfoRepo) Delete(ctx context.Context, id int64) error {
	err := p.db.WithContext(ctx).Where("id = ?", id).Delete(&UnsNamespaceLabelInfo{}).Error
	return stores.ErrFmt(err)
}
func (p NamespaceLabelInfoRepo) FindOne(ctx context.Context, id int64) (*UnsNamespaceLabelInfo, error) {
	var result UnsNamespaceLabelInfo
	err := p.db.WithContext(ctx).Where("id = ?", id).First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}

// 批量插入 LightStrategyDevice 记录
func (p NamespaceLabelInfoRepo) MultiInsert(ctx context.Context, data []*UnsNamespaceLabelInfo) error {
	err := p.db.WithContext(ctx).Clauses(clause.OnConflict{UpdateAll: true}).Model(&UnsNamespaceLabelInfo{}).Create(data).Error
	return stores.ErrFmt(err)
}

func (d NamespaceLabelInfoRepo) UpdateWithField(ctx context.Context, f NamespaceLabelInfoFilter, updates map[string]any) error {
	db := d.fmtFilter(ctx, f)
	err := db.Model(&UnsNamespaceLabelInfo{}).Updates(updates).Error
	return stores.ErrFmt(err)
}
