package relationDB

import (
	"context"

	"gitee.com/unitedrhino/share/conf"
	"gitee.com/unitedrhino/share/stores"
	"github.com/FREEZONEX/Tier0-Backend/share/ctxs"
)

var NeedInitColumn bool

func Migrate(c conf.Database) error {
	if c.IsInitTable == false {
		return nil
	}
	db := stores.GetSchemaTenantConn(ctxs.BindTenantCode(context.Background(), "xxx"))
	if !db.Migrator().HasTable(&UnsNamespaceNodeInfo{}) {
		//需要初始化表
		NeedInitColumn = true
	}
	err := db.AutoMigrate(
		&UnsNamespaceNodeInfo{},
		&UnsNamespaceTemplateInfo{},
		&UnsNamespaceLabelInfo{},
		&UnsNamespaceLabelNodeID{},
		&UnsNamespaceNodeAttachment{},
		&UnsConnectNodeInfo{},
		&UnsTimeNamespaceRecord{},
	)
	if err != nil {
		return err
	}

	return err
}

func migrateTableColumn() error {
	return nil
}
