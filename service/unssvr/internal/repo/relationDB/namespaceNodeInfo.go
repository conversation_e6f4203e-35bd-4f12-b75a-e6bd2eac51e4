package relationDB

import (
	"context"
	"fmt"

	"gitee.com/unitedrhino/share/stores"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

type NamespaceNodeInfoRepo struct {
	db *gorm.DB
}

func NewNamespaceNodeInfoRepo(in any) *NamespaceNodeInfoRepo {
	return &NamespaceNodeInfoRepo{db: stores.GetCommonConn(in)}
}

type NamespaceNodeInfoFilter struct {
	ID              int64
	Namespace       string
	Name            string
	ParentID        int64
	WithAttachments bool
	NodeType        int64
	TemplateID      int64
	WithLabels      bool
	LabelID         int64
	Recycle         bool
	Unscoped        bool
}

func (p NamespaceNodeInfoRepo) fmtFilter(ctx context.Context, f NamespaceNodeInfoFilter) *gorm.DB {
	db := p.db.WithContext(ctx)
	if f.ID != 0 {
		db = db.Where("id = ?", f.ID)
	}
	if f.ParentID != 0 {
		db = db.Where("parent_id = ?", f.ParentID)
	}
	if f.Namespace != "" {
		db = db.Where("namespace = ?", f.Namespace)
	}
	if f.Name != "" {
		db = db.Where("name = ?", f.Name)
	}
	if f.NodeType != 0 {
		db = db.Where("node_type = ?", f.NodeType)
	}
	if f.TemplateID != 0 {
		db = db.Where("template_id = ?", f.TemplateID)
	}
	if f.WithLabels {
		db = db.Preload("Labels")
	}
	if f.LabelID > 0 {
		db = db.Where("id IN (?)", db.Table(UnsNamespaceLabelNodeID{}.TableName()).
			Select("node_id").Where("label_id = ?", f.LabelID))
	}
	if f.Unscoped {
		db = db.Unscoped()
	}
	if f.Recycle {
		db = db.Unscoped().
			Where("deleted_time>0").
			Where("recycle_is_del = 2")
	}
	return db
}

func (p NamespaceNodeInfoRepo) Insert(ctx context.Context, data *UnsNamespaceNodeInfo) error {
	result := p.db.WithContext(ctx).Create(data)
	return stores.ErrFmt(result.Error)
}

func (p NamespaceNodeInfoRepo) FindOneByFilter(ctx context.Context, f NamespaceNodeInfoFilter) (*UnsNamespaceNodeInfo, error) {
	var result UnsNamespaceNodeInfo
	db := p.fmtFilter(ctx, f)
	err := db.First(&result).Debug().Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}
func (p NamespaceNodeInfoRepo) FindByFilter(ctx context.Context, f NamespaceNodeInfoFilter, page *stores.PageInfo) ([]*UnsNamespaceNodeInfo, error) {
	var results []*UnsNamespaceNodeInfo
	db := p.fmtFilter(ctx, f).Model(&UnsNamespaceNodeInfo{})
	db = page.ToGorm(db)
	err := db.Find(&results).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return results, nil
}

func (p NamespaceNodeInfoRepo) CountByFilter(ctx context.Context, f NamespaceNodeInfoFilter) (size int64, err error) {
	db := p.fmtFilter(ctx, f).Model(&UnsNamespaceNodeInfo{})
	err = db.Count(&size).Error
	return size, stores.ErrFmt(err)
}

func (p NamespaceNodeInfoRepo) Update(ctx context.Context, data *UnsNamespaceNodeInfo) error {
	err := p.db.WithContext(ctx).Where("id = ?", data.ID).Save(data).Error
	return stores.ErrFmt(err)
}

func (p NamespaceNodeInfoRepo) UpdateWithLabelAssociat(ctx context.Context, data *UnsNamespaceNodeInfo) error {
	labels := data.Labels
	data.Labels = nil
	err := p.db.WithContext(ctx).Where("id = ?", data.ID).Save(data).Error
	if err != nil {
		return stores.ErrFmt(err)
	}
	p.db.Model(&data).Association("Labels").Clear()
	err = p.db.Model(&data).Association("Labels").Append(labels)
	return stores.ErrFmt(err)
}

func (p NamespaceNodeInfoRepo) DeleteByFilter(ctx context.Context, f NamespaceNodeInfoFilter) error {
	db := p.fmtFilter(ctx, f)
	err := db.Delete(&UnsNamespaceNodeInfo{}).Error
	return stores.ErrFmt(err)
}

func (p NamespaceNodeInfoRepo) Delete(ctx context.Context, id int64) error {
	err := p.db.WithContext(ctx).Where("id = ?", id).Delete(&UnsNamespaceNodeInfo{}).Error
	return stores.ErrFmt(err)
}
func (p NamespaceNodeInfoRepo) FindOne(ctx context.Context, id int64) (*UnsNamespaceNodeInfo, error) {
	var result UnsNamespaceNodeInfo
	err := p.db.WithContext(ctx).Where("id = ?", id).First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}

// 批量插入 LightStrategyDevice 记录
func (p NamespaceNodeInfoRepo) MultiInsert(ctx context.Context, data []*UnsNamespaceNodeInfo) error {
	err := p.db.WithContext(ctx).Clauses(clause.OnConflict{UpdateAll: true}).Model(&UnsNamespaceNodeInfo{}).Create(data).Error
	return stores.ErrFmt(err)
}

func (d NamespaceNodeInfoRepo) UpdateWithField(ctx context.Context, f NamespaceNodeInfoFilter, updates map[string]any) error {
	db := d.fmtFilter(ctx, f)
	err := db.Model(&UnsNamespaceNodeInfo{}).Updates(updates).Error
	return stores.ErrFmt(err)
}

func (d NamespaceNodeInfoRepo) AssociationLabelAppend(ctx context.Context, labels ...UnsNamespaceLabelInfo) error {
	db := d.db.WithContext(ctx)
	err := db.Model(&UnsNamespaceNodeInfo{}).Association("Labels").Append(labels)
	return stores.ErrFmt(err)
}

// 软删除撤销-按文件夹撤销
func (d NamespaceNodeInfoRepo) UndoByDir(ctx context.Context, dirIDPath string) error {
	db := d.db.WithContext(ctx)
	err := db.Model(&UnsNamespaceNodeInfo{}).
		Unscoped().
		Where("deleted_time>0").
		Where("recycle_is_del=2").
		Where("id_path LIKE ?", fmt.Sprintf("%s%%", dirIDPath)).
		Update("deleted_time", 0).Error
	return stores.ErrFmt(err)
}

// 修改文件夹name-批量更新namespace
func (d NamespaceNodeInfoRepo) UpdateNamespaceByChangeDirName(ctx context.Context, old, new string) error {
	db := d.db.WithContext(ctx)
	err := db.Model(&UnsNamespaceNodeInfo{}).
		Unscoped().
		Where("namespace LIKE ?", old+"%").
		// 如果类似 a/b/c/a这种情况,后面的a也会replace,只替换出现的第一个
		Update("namespace", gorm.Expr("REGEXP_REPLACE(namespace, ?, ?, '')", old+"/", new+"/")).Error
	return stores.ErrFmt(err)
}
