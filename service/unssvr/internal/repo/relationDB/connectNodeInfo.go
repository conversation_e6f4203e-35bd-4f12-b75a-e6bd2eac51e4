package relationDB

import (
	"context"

	"gitee.com/unitedrhino/share/stores"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

/*
这个是参考样例
使用教程:
1. 将example全局替换为模型的表名
2. 完善todo
*/

type ConnectNodeInfoRepo struct {
	db *gorm.DB
}

func NewConnectNodeInfoRepo(in any) *ConnectNodeInfoRepo {
	return &ConnectNodeInfoRepo{db: stores.GetCommonConn(in)}
}

type ConnectNodeInfoFilter struct {
	//todo 添加过滤字段
	Name string
	ID   int64
}

func (p ConnectNodeInfoRepo) fmtFilter(ctx context.Context, f ConnectNodeInfoFilter) *gorm.DB {
	db := p.db.WithContext(ctx)
	if f.ID != 0 {
		db = db.Where("id = ?", f.ID)
	}
	if f.Name != "" {
		db = db.Where("name = ?", f.Name)
	}
	return db
}

func (p ConnectNodeInfoRepo) Insert(ctx context.Context, data *UnsConnectNodeInfo) error {
	result := p.db.WithContext(ctx).Create(data)
	return stores.ErrFmt(result.Error)
}

func (p ConnectNodeInfoRepo) FindOneByFilter(ctx context.Context, f ConnectNodeInfoFilter) (*UnsConnectNodeInfo, error) {
	var result UnsConnectNodeInfo
	db := p.fmtFilter(ctx, f)
	err := db.First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}
func (p ConnectNodeInfoRepo) FindByFilter(ctx context.Context, f ConnectNodeInfoFilter, page *stores.PageInfo) ([]*UnsConnectNodeInfo, error) {
	var results []*UnsConnectNodeInfo
	db := p.fmtFilter(ctx, f).Model(&UnsConnectNodeInfo{})
	db = page.ToGorm(db)
	err := db.Find(&results).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return results, nil
}

func (p ConnectNodeInfoRepo) CountByFilter(ctx context.Context, f ConnectNodeInfoFilter) (size int64, err error) {
	db := p.fmtFilter(ctx, f).Model(&UnsConnectNodeInfo{})
	err = db.Count(&size).Error
	return size, stores.ErrFmt(err)
}

func (p ConnectNodeInfoRepo) Update(ctx context.Context, data *UnsConnectNodeInfo) error {
	err := p.db.WithContext(ctx).Where("id = ?", data.ID).Save(data).Error
	return stores.ErrFmt(err)
}

func (p ConnectNodeInfoRepo) DeleteByFilter(ctx context.Context, f ConnectNodeInfoFilter) error {
	db := p.fmtFilter(ctx, f)
	err := db.Delete(&UnsConnectNodeInfo{}).Error
	return stores.ErrFmt(err)
}

func (p ConnectNodeInfoRepo) Delete(ctx context.Context, id int64) error {
	err := p.db.WithContext(ctx).Where("id = ?", id).Delete(&UnsConnectNodeInfo{}).Error
	return stores.ErrFmt(err)
}
func (p ConnectNodeInfoRepo) FindOne(ctx context.Context, id int64) (*UnsConnectNodeInfo, error) {
	var result UnsConnectNodeInfo
	err := p.db.WithContext(ctx).Where("id = ?", id).First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}

// 批量插入 LightStrategyDevice 记录
func (p ConnectNodeInfoRepo) MultiInsert(ctx context.Context, data []*UnsConnectNodeInfo) error {
	err := p.db.WithContext(ctx).Clauses(clause.OnConflict{UpdateAll: true}).Model(&UnsConnectNodeInfo{}).Create(data).Error
	return stores.ErrFmt(err)
}

func (d ConnectNodeInfoRepo) UpdateWithField(ctx context.Context, f ConnectNodeInfoFilter, updates map[string]any) error {
	db := d.fmtFilter(ctx, f)
	err := db.Model(&UnsConnectNodeInfo{}).Updates(updates).Error
	return stores.ErrFmt(err)
}
