package startup

import (
	"context"
	"time"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/event/deviceSub"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/event/etl"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/event/subDev"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/share/clients"
	"github.com/zeromicro/go-zero/core/logx"
)

func Init(svcCtx *svc.ServiceContext) {
	mc, err := clients.NewMqttClient(svcCtx.Config.DevLink.Mqtt)
	logx.Must(err)
	svcCtx.MqttClient = mc
	sd, err := subDev.NewSubDev(svcCtx.Config.DevLink)
	logx.Must(err)
	err = sd.SubDevMsg(func(ctx context.Context) subDev.DevSubHandle {
		return deviceSub.NewDeviceSubServer(svcCtx, ctx)
	})
	logx.Must(err)
	etlInstance, err := etl.NewETLService(svcCtx, 1, 20*time.Second, 30*time.Second)
	if err != nil {
		logx.Errorv(err)
	} else {
		etlInstance.Run(context.Background())
	}
}
