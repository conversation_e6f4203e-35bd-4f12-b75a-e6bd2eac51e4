package svc

import (
	"os"

	"gitee.com/unitedrhino/share/oss"

	"github.com/FREEZONEX/Tier0-Backend/share/clients"

	"gitee.com/unitedrhino/share/caches"
	"gitee.com/unitedrhino/share/stores"
	"gitee.com/unitedrhino/share/utils"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/config"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/repo/relationDB"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
)

type ServiceContext struct {
	NodeID     *utils.SnowFlake
	Config     config.Config
	MqttClient *clients.MqttClient
	OssClient  *oss.Client
	Redis      *redis.Redis
}

func NewServiceContext(c config.Config) *ServiceContext {
	stores.InitConn(c.Database)
	caches.InitStore(c.CacheRedis)
	err := relationDB.Migrate(c.Database)
	if err != nil {
		logx.Error("syssvr 数据库初始化失败 err", err)
		os.Exit(-1)
	}
	nodeID := utils.GetNodeID(c.CacheRedis, c.Name)
	ossClient, err := oss.NewOssClient(c.OssConf)
	if err != nil {
		logx.Errorf("NewOss err err:%v", err)
		os.Exit(-1)
	}
	return &ServiceContext{
		Config:    c,
		OssClient: ossClient,
		NodeID:    utils.NewSnowFlake(nodeID),
		Redis:     redis.MustNewRedis(c.CacheRedis[0].RedisConf),
	}
}
