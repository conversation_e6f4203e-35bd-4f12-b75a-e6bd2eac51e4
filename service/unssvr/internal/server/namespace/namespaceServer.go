// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.1
// Source: uns.proto

package server

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/logic/namespace"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"
)

type NamespaceServer struct {
	svcCtx *svc.ServiceContext
	uns.UnimplementedNamespaceServer
}

func NewNamespaceServer(svcCtx *svc.ServiceContext) *NamespaceServer {
	return &NamespaceServer{
		svcCtx: svcCtx,
	}
}

func (s *NamespaceServer) NodeInfoCreate(ctx context.Context, in *uns.NamespaceNodeInfo) (*uns.WithID, error) {
	l := namespacelogic.NewNodeInfoCreateLogic(ctx, s.svcCtx)
	return l.NodeInfoCreate(in)
}

func (s *NamespaceServer) NodeInfoUpdate(ctx context.Context, in *uns.NamespaceNodeInfo) (*uns.Empty, error) {
	l := namespacelogic.NewNodeInfoUpdateLogic(ctx, s.svcCtx)
	return l.NodeInfoUpdate(in)
}

func (s *NamespaceServer) NodeInfoDelete(ctx context.Context, in *uns.WithID) (*uns.Empty, error) {
	l := namespacelogic.NewNodeInfoDeleteLogic(ctx, s.svcCtx)
	return l.NodeInfoDelete(in)
}

func (s *NamespaceServer) NodeInfoGetOne(ctx context.Context, in *uns.NamespaceNodeInfoGetOneReq) (*uns.NamespaceNodeInfo, error) {
	l := namespacelogic.NewNodeInfoGetOneLogic(ctx, s.svcCtx)
	return l.NodeInfoGetOne(in)
}

func (s *NamespaceServer) NodeInfoGetList(ctx context.Context, in *uns.NamespaceNodeInfoGetListReq) (*uns.NamespaceNodeInfoGetListResp, error) {
	l := namespacelogic.NewNodeInfoGetListLogic(ctx, s.svcCtx)
	return l.NodeInfoGetList(in)
}

// 获取指定命名空间下的文件列表
func (s *NamespaceServer) NodeInfoGetFileList(ctx context.Context, in *uns.NamespaceNodeInfoGetFileListReq) (*uns.NamespaceNodeInfoGetFileListResp, error) {
	l := namespacelogic.NewNodeInfoGetFileListLogic(ctx, s.svcCtx)
	return l.NodeInfoGetFileList(in)
}

// 回收站撤销
func (s *NamespaceServer) NodeInfoRecycleUndo(ctx context.Context, in *uns.WithID) (*uns.Empty, error) {
	l := namespacelogic.NewNodeInfoRecycleUndoLogic(ctx, s.svcCtx)
	return l.NodeInfoRecycleUndo(in)
}

// 回收站删除
func (s *NamespaceServer) NodeInfoRecycleDelete(ctx context.Context, in *uns.WithID) (*uns.Empty, error) {
	l := namespacelogic.NewNodeInfoRecycleDeleteLogic(ctx, s.svcCtx)
	return l.NodeInfoRecycleDelete(in)
}

func (s *NamespaceServer) TemplateInfoCreate(ctx context.Context, in *uns.NamespaceTemplateInfo) (*uns.WithID, error) {
	l := namespacelogic.NewTemplateInfoCreateLogic(ctx, s.svcCtx)
	return l.TemplateInfoCreate(in)
}

func (s *NamespaceServer) TemplateInfoUpdate(ctx context.Context, in *uns.NamespaceTemplateInfo) (*uns.Empty, error) {
	l := namespacelogic.NewTemplateInfoUpdateLogic(ctx, s.svcCtx)
	return l.TemplateInfoUpdate(in)
}

func (s *NamespaceServer) TemplateInfoDelete(ctx context.Context, in *uns.WithID) (*uns.Empty, error) {
	l := namespacelogic.NewTemplateInfoDeleteLogic(ctx, s.svcCtx)
	return l.TemplateInfoDelete(in)
}

func (s *NamespaceServer) TemplateInfoGetOne(ctx context.Context, in *uns.WithID) (*uns.NamespaceTemplateInfo, error) {
	l := namespacelogic.NewTemplateInfoGetOneLogic(ctx, s.svcCtx)
	return l.TemplateInfoGetOne(in)
}

func (s *NamespaceServer) TemplateInfoGetList(ctx context.Context, in *uns.NamespaceTemplateInfoGetListReq) (*uns.NamespaceTemplateInfoGetListResp, error) {
	l := namespacelogic.NewTemplateInfoGetListLogic(ctx, s.svcCtx)
	return l.TemplateInfoGetList(in)
}

func (s *NamespaceServer) LabelInfoCreate(ctx context.Context, in *uns.NamespaceLabelInfo) (*uns.Empty, error) {
	l := namespacelogic.NewLabelInfoCreateLogic(ctx, s.svcCtx)
	return l.LabelInfoCreate(in)
}

func (s *NamespaceServer) LabelInfoUpdate(ctx context.Context, in *uns.NamespaceLabelInfo) (*uns.Empty, error) {
	l := namespacelogic.NewLabelInfoUpdateLogic(ctx, s.svcCtx)
	return l.LabelInfoUpdate(in)
}

func (s *NamespaceServer) LabelInfoDelete(ctx context.Context, in *uns.WithID) (*uns.Empty, error) {
	l := namespacelogic.NewLabelInfoDeleteLogic(ctx, s.svcCtx)
	return l.LabelInfoDelete(in)
}

func (s *NamespaceServer) LabelInfoGetOne(ctx context.Context, in *uns.WithID) (*uns.NamespaceLabelInfo, error) {
	l := namespacelogic.NewLabelInfoGetOneLogic(ctx, s.svcCtx)
	return l.LabelInfoGetOne(in)
}

func (s *NamespaceServer) LabelInfoGetList(ctx context.Context, in *uns.NamespaceLabelInfoGetListReq) (*uns.NamespaceLabelInfoGetListResp, error) {
	l := namespacelogic.NewLabelInfoGetListLogic(ctx, s.svcCtx)
	return l.LabelInfoGetList(in)
}

func (s *NamespaceServer) LabelNodeIDCreate(ctx context.Context, in *uns.NamespaceLabelNodeID) (*uns.Empty, error) {
	l := namespacelogic.NewLabelNodeIDCreateLogic(ctx, s.svcCtx)
	return l.LabelNodeIDCreate(in)
}

// 已弃用,用文件列表传labelid方式获取
func (s *NamespaceServer) LabelNodeIDDelete(ctx context.Context, in *uns.WithID) (*uns.Empty, error) {
	l := namespacelogic.NewLabelNodeIDDeleteLogic(ctx, s.svcCtx)
	return l.LabelNodeIDDelete(in)
}

// json转definition
func (s *NamespaceServer) Json2Fs(ctx context.Context, in *uns.NamespaceJson2FsReq) (*uns.NamespaceJson2FsResp, error) {
	l := namespacelogic.NewJson2fsLogic(ctx, s.svcCtx)
	return l.Json2Fs(in)
}

// dashboard
func (s *NamespaceServer) Dashboard(ctx context.Context, in *uns.DashboardReq) (*uns.DashboardResp, error) {
	l := namespacelogic.NewDashboardLogic(ctx, s.svcCtx)
	return l.Dashboard(in)
}
