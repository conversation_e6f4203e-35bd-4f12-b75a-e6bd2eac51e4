// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.1
// Source: uns.proto

package server

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/logic/connectmanage"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/unssvr/pb/uns"
)

type ConnectManageServer struct {
	svcCtx *svc.ServiceContext
	uns.UnimplementedConnectManageServer
}

func NewConnectManageServer(svcCtx *svc.ServiceContext) *ConnectManageServer {
	return &ConnectManageServer{
		svcCtx: svcCtx,
	}
}

func (s *ConnectManageServer) NodeInfoCreate(ctx context.Context, in *uns.ConnectNodeInfo) (*uns.WithID, error) {
	l := connectmanagelogic.NewNodeInfoCreateLogic(ctx, s.svcCtx)
	return l.NodeInfoCreate(in)
}

func (s *ConnectManageServer) NodeInfoUpdate(ctx context.Context, in *uns.ConnectNodeInfo) (*uns.Empty, error) {
	l := connectmanagelogic.NewNodeInfoUpdateLogic(ctx, s.svcCtx)
	return l.NodeInfoUpdate(in)
}

func (s *ConnectManageServer) NodeInfoDelete(ctx context.Context, in *uns.WithID) (*uns.Empty, error) {
	l := connectmanagelogic.NewNodeInfoDeleteLogic(ctx, s.svcCtx)
	return l.NodeInfoDelete(in)
}

func (s *ConnectManageServer) NodeInfoGetOne(ctx context.Context, in *uns.ConnectNodeInfoGetOneReq) (*uns.ConnectNodeInfo, error) {
	l := connectmanagelogic.NewNodeInfoGetOneLogic(ctx, s.svcCtx)
	return l.NodeInfoGetOne(in)
}

func (s *ConnectManageServer) NodeInfoGetList(ctx context.Context, in *uns.ConnectNodeInfoListReq) (*uns.ConnectNodeInfoListResp, error) {
	l := connectmanagelogic.NewNodeInfoGetListLogic(ctx, s.svcCtx)
	return l.NodeInfoGetList(in)
}

// 先不做
func (s *ConnectManageServer) MappingInfoUpdate(ctx context.Context, in *uns.Empty) (*uns.Empty, error) {
	l := connectmanagelogic.NewMappingInfoUpdateLogic(ctx, s.svcCtx)
	return l.MappingInfoUpdate(in)
}

// 全部挂载在根节点1下面
func (s *ConnectManageServer) MappingInfoGetList(ctx context.Context, in *uns.Empty) (*uns.Empty, error) {
	l := connectmanagelogic.NewMappingInfoGetListLogic(ctx, s.svcCtx)
	return l.MappingInfoGetList(in)
}

// 映射操作日志获取
func (s *ConnectManageServer) MappingOperationLogGetList(ctx context.Context, in *uns.Empty) (*uns.Empty, error) {
	l := connectmanagelogic.NewMappingOperationLogGetListLogic(ctx, s.svcCtx)
	return l.MappingOperationLogGetList(in)
}

// 从边缘节点同步节点
func (s *ConnectManageServer) MappingSyncUns(ctx context.Context, in *uns.Empty) (*uns.Empty, error) {
	l := connectmanagelogic.NewMappingSyncUnsLogic(ctx, s.svcCtx)
	return l.MappingSyncUns(in)
}

// 部署到边缘节点
func (s *ConnectManageServer) MappingDeployToEdge(ctx context.Context, in *uns.Empty) (*uns.Empty, error) {
	l := connectmanagelogic.NewMappingDeployToEdgeLogic(ctx, s.svcCtx)
	return l.MappingDeployToEdge(in)
}
