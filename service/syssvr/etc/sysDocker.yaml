#系统管理模块-syssvr
Name: sys.rpc
ListenOn: 0.0.0.0:9540
CpuThreshold: 0
Etcd:
  Hosts:
    - etcd:2379
  Key: sys.rpc
Database:
  DBType: pgsql
  DSN: ******************************************/postgres
CacheRedis:
  - Host: redis:6379
    Pass:
    Type: node
UserToken:
  AccessSecret: ad879037-c7a4-4063-9236-6bfc35d54b7d
  AccessExpire: 360000000 #有效期为一小时
UserOpt:
  NeedUserName: true
  NeedPassWord: true
  PassLevel: 2

Email:
  From:  <EMAIL>     # 发件人  你自己要发邮件的邮箱
  Host: smtpdm.aliyun.com      # 服务器地址 例如 smtp.qq.com  请前往QQ或者你要发邮件的邮箱查看其smtp协议
  Secret:   Supos1304Free  # 密钥    用于登录的密钥 最好不要用邮箱密码 去邮箱smtp申请一个用于登录的密钥
  Nickname: tier0  # 昵称    发件人昵称 通常为自己的邮箱
  Port:   465    # 端口     请前往QQ或者你要发邮件的邮箱查看其smtp协议 大多为 465
  IsSSL:    true  # 是否SSL   是否开启SSL

OssConf:
  OssType: aws # 如果不需要minio,可以填写local,默认存储路径为 ../oss
  PublicBucketName: tier0-upload-public
  PrivateBucketName: tier0-upload
  TemporaryBucketName: tier0-upload-temp
  AccessKeyID: ${ossAk}
  AccessKeySecret: ${ossSk}
  Location: s3.ap-southeast-1.amazonaws.com
  Region: ap-southeast-1