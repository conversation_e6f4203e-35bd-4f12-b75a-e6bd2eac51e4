package sysExport

import (
	"context"
	"fmt"
	"gitee.com/unitedrhino/core/share/topics"
	"gitee.com/unitedrhino/share/caches"
	"gitee.com/unitedrhino/share/ctxs"
	"gitee.com/unitedrhino/share/eventBus"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/usermanage"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"
)

type UserCacheT = *caches.Cache[usermanage.UserInfo, int64]

func NewUserInfoCache(pm usermanage.UserManage, fastEvent *eventBus.FastEvent) (UserCacheT, error) {
	return caches.NewCache(caches.CacheConfig[usermanage.UserInfo, int64]{
		KeyType:   topics.ServerCacheKeySysUserInfo,
		FastEvent: fastEvent,
		GetData: func(ctx context.Context, key int64) (*usermanage.UserInfo, error) {
			ret, err := pm.UserInfoRead(ctx, &sys.UserInfoReadReq{UserID: key})
			return ret, err
		},
	})
}

func GenSlotCacheKey(code string, subCode string) string {
	return fmt.Sprintf("%s:%s", code, subCode)
}

func GenWebhookCacheKey(ctx context.Context, code string) string {
	tenantCode := ctxs.GetUserCtxNoNil(ctx).TenantCode
	return fmt.Sprintf("%s:%s", tenantCode, code)
}

func GenApiCacheKey(method, route string) string {
	return fmt.Sprintf("%s:%s", method, route)
}
