// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.1
// Source: sys.proto

package server

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/logic/usermanage"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"
)

type UserManageServer struct {
	svcCtx *svc.ServiceContext
	sys.UnimplementedUserManageServer
}

func NewUserManageServer(svcCtx *svc.ServiceContext) *UserManageServer {
	return &UserManageServer{
		svcCtx: svcCtx,
	}
}

func (s *UserManageServer) UserInfoUpdate(ctx context.Context, in *sys.UserInfoUpdateReq) (*sys.Empty, error) {
	l := usermanagelogic.NewUserInfoUpdateLogic(ctx, s.svcCtx)
	return l.UserInfoUpdate(in)
}

func (s *UserManageServer) UserInfoGetOne(ctx context.Context, in *sys.UserInfoGetOneReq) (*sys.UserInfo, error) {
	l := usermanagelogic.NewUserInfoGetOneLogic(ctx, s.svcCtx)
	return l.UserInfoGetOne(in)
}

func (s *UserManageServer) UserInfoDelete(ctx context.Context, in *sys.UserInfoDeleteReq) (*sys.Empty, error) {
	l := usermanagelogic.NewUserInfoDeleteLogic(ctx, s.svcCtx)
	return l.UserInfoDelete(in)
}

func (s *UserManageServer) UserLogin(ctx context.Context, in *sys.UserLoginReq) (*sys.UserLoginResp, error) {
	l := usermanagelogic.NewUserLoginLogic(ctx, s.svcCtx)
	return l.UserLogin(in)
}

func (s *UserManageServer) UserForgetPwd(ctx context.Context, in *sys.UserForgetPwdReq) (*sys.Empty, error) {
	l := usermanagelogic.NewUserForgetPwdLogic(ctx, s.svcCtx)
	return l.UserForgetPwd(in)
}

func (s *UserManageServer) UserCaptcha(ctx context.Context, in *sys.UserCaptchaReq) (*sys.UserCaptchaResp, error) {
	l := usermanagelogic.NewUserCaptchaLogic(ctx, s.svcCtx)
	return l.UserCaptcha(in)
}

func (s *UserManageServer) UserCheckToken(ctx context.Context, in *sys.UserCheckTokenReq) (*sys.UserCheckTokenResp, error) {
	l := usermanagelogic.NewUserCheckTokenLogic(ctx, s.svcCtx)
	return l.UserCheckToken(in)
}

func (s *UserManageServer) UserRegister(ctx context.Context, in *sys.UserRegisterReq) (*sys.UserRegisterResp, error) {
	l := usermanagelogic.NewUserRegisterLogic(ctx, s.svcCtx)
	return l.UserRegister(in)
}

func (s *UserManageServer) UserChangePwd(ctx context.Context, in *sys.UserChangePwdReq) (*sys.Empty, error) {
	l := usermanagelogic.NewUserChangePwdLogic(ctx, s.svcCtx)
	return l.UserChangePwd(in)
}

func (s *UserManageServer) UserCodeToUserID(ctx context.Context, in *sys.UserCodeToUserIDReq) (*sys.UserCodeToUserIDResp, error) {
	l := usermanagelogic.NewUserCodeToUserIDLogic(ctx, s.svcCtx)
	return l.UserCodeToUserID(in)
}

func (s *UserManageServer) UserBindAccount(ctx context.Context, in *sys.UserBindAccountReq) (*sys.Empty, error) {
	l := usermanagelogic.NewUserBindAccountLogic(ctx, s.svcCtx)
	return l.UserBindAccount(in)
}
