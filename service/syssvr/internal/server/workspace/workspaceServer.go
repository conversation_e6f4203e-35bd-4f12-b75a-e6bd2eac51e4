// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.1
// Source: sys.proto

package server

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/logic/workspace"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"
)

type WorkspaceServer struct {
	svcCtx *svc.ServiceContext
	sys.UnimplementedWorkspaceServer
}

func NewWorkspaceServer(svcCtx *svc.ServiceContext) *WorkspaceServer {
	return &WorkspaceServer{
		svcCtx: svcCtx,
	}
}

func (s *WorkspaceServer) WorkspaceUpdate(ctx context.Context, in *sys.Empty) (*sys.Empty, error) {
	l := workspacelogic.NewWorkspaceUpdateLogic(ctx, s.svcCtx)
	return l.WorkspaceUpdate(in)
}
