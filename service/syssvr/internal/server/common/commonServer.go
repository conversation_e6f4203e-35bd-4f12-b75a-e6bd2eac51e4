// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.1
// Source: sys.proto

package server

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/logic/common"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"
)

type CommonServer struct {
	svcCtx *svc.ServiceContext
	sys.UnimplementedCommonServer
}

func NewCommonServer(svcCtx *svc.ServiceContext) *CommonServer {
	return &CommonServer{
		svcCtx: svcCtx,
	}
}

func (s *CommonServer) UploadUrlGetMap(ctx context.Context, in *sys.CommonUploadUrlGetMapReq) (*sys.CommonUploadUrlGetMapResp, error) {
	l := commonlogic.NewUploadUrlGetMapLogic(ctx, s.svcCtx)
	return l.UploadUrlGetMap(in)
}

func (s *CommonServer) UploadUrlGetOne(ctx context.Context, in *sys.CommonUploadUrlGetOneReq) (*sys.CommonUploadUrlGetOneResp, error) {
	l := commonlogic.NewUploadUrlGetOneLogic(ctx, s.svcCtx)
	return l.UploadUrlGetOne(in)
}
