package commonlogic

import (
	"context"
	"fmt"
	"gitee.com/unitedrhino/share/ctxs"
	"gitee.com/unitedrhino/share/errors"
	"gitee.com/unitedrhino/share/oss/common"
	"gitee.com/unitedrhino/share/utils"
	"path/filepath"
	"strings"
	"time"

	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"

	"github.com/zeromicro/go-zero/core/logx"
)

type UploadUrlGetOneLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUploadUrlGetOneLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UploadUrlGetOneLogic {
	return &UploadUrlGetOneLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UploadUrlGetOneLogic) UploadUrlGetOne(in *sys.CommonUploadUrlGetOneReq) (*sys.CommonUploadUrlGetOneResp, error) {
	if isForbiddenExtension(in.FileName) {
		return nil, errors.Parameter.AddDetail(fmt.Sprintf("有客户上传危险文件 文件名:%s uc:%#v", in.FileName, ctxs.GetUserCtx(l.ctx)))
	}
	path, _ := GetFilePath2(ctxs.WithRoot(l.ctx), in.FileName)
	fileUrl, err := l.svcCtx.OssClient.TemporaryBucket().SignedPutUrl(l.ctx, path, 3600, common.OptionKv{})
	if err != nil {
		return nil, err
	}
	return &sys.CommonUploadUrlGetOneResp{UploadUrl: fileUrl, FilePath: path}, nil
}

func GetFilePath2(ctx context.Context, fileName string) (string, error) {
	spcChar := []string{`,`, `?`, `*`, `|`, `{`, `}`, `\`, `$`, `、`, `·`, "`", `'`, `"`}
	if strings.ContainsAny(fileName, strings.Join(spcChar, "")) {
		return "", errors.Parameter.WithMsg("包含特殊字符")
	}
	uc := ctxs.GetUserCtx(ctx)
	if uc == nil {
		return "", errors.Permissions.WithMsg("需要登录")
	}
	return fmt.Sprintf("%s/%s/%d/%s/%s", utils.ToYYMMdd2(time.Now().UnixMilli()), uc.TenantCode, uc.UserID,
		utils.ToddHHSS2(time.Now().UnixMilli()), fileName), nil

}

// 定义一个禁止上传的文件后缀集合
var forbiddenExtensions = map[string]struct{}{
	"html": {}, "htm": {},
	"php": {}, "php5": {}, "php4": {}, "php3": {}, "php2": {}, "phtml": {}, "pht": {},
	"asp": {}, "aspx": {}, "asa": {}, "asax": {}, "ascx": {}, "ashx": {}, "asmx": {}, "cer": {},
	"jsp": {}, "jspa": {}, "jspx": {}, "jsw": {}, "jsv": {}, "jspf": {}, "jhtml": {},
	"htaccess": {}, "swf": {},
}

// 检查文件后缀是否被禁止
func isForbiddenExtension(filename string) bool {
	// 获取文件后缀
	ext := strings.ToLower(strings.TrimPrefix(filepath.Ext(filename), "."))
	// 检查是否在禁止集合中
	_, exists := forbiddenExtensions[ext]
	return exists
}
