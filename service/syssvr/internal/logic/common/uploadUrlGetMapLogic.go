package commonlogic

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"

	"github.com/zeromicro/go-zero/core/logx"
)

type UploadUrlGetMapLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUploadUrlGetMapLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UploadUrlGetMapLogic {
	return &UploadUrlGetMapLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UploadUrlGetMapLogic) UploadUrlGetMap(in *sys.CommonUploadUrlGetMapReq) (*sys.CommonUploadUrlGetMapResp, error) {
	var ret = map[string]*sys.CommonUploadUrlGetOneResp{}
	for k, v := range in.FileInfo {
		r, err := NewUploadUrlGetOneLogic(l.ctx, l.svcCtx).UploadUrlGetOne(&sys.CommonUploadUrlGetOneReq{FileName: v})
		if err != nil {
			return nil, err
		}
		ret[k] = r
	}
	return &sys.CommonUploadUrlGetMapResp{FileInfo: ret}, nil
}
