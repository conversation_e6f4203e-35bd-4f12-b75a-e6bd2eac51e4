package logic

import (
	"gitee.com/unitedrhino/share/stores"
	"gitee.com/unitedrhino/share/utils"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"
)

func ToPageInfo(info *sys.PageInfo) *stores.PageInfo {
	return utils.Copy[stores.PageInfo](info)
}

func ToPageInfoWithDefault(info *sys.PageInfo, defau *stores.PageInfo) *stores.PageInfo {
	if page := ToPageInfo(info); page == nil {
		return defau
	} else {
		if page.Page == 0 {
			page.Page = defau.Page
		}
		if page.Size == 0 {
			page.Size = defau.Size
		}
		if len(page.Orders) == 0 {
			page.Orders = defau.Orders
		}
		return page
	}
}
