package usermanagelogic

import (
	"context"
	"gitee.com/unitedrhino/share/ctxs"
	"gitee.com/unitedrhino/share/def"
	"gitee.com/unitedrhino/share/errors"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/share/users"

	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"

	"github.com/zeromicro/go-zero/core/logx"
)

type UserBindAccountLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUserBindAccountLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UserBindAccountLogic {
	return &UserBindAccountLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UserBindAccountLogic) UserBindAccount(in *sys.UserBindAccountReq) (*sys.Empty, error) {
	//cli, er := l.svcCtx.Cm.GetClients(l.ctx, "")
	//if er != nil {
	//	return nil, errors.System.AddDetail(er)
	//}

	uc := ctxs.GetUserCtx(l.ctx)
	ui, err := relationDB.NewUserInfoRepo(l.ctx).FindOne(l.ctx, uc.UserID)
	if err != nil {
		return nil, err
	}
	switch in.Type {
	case users.RegEmail:
		email := l.svcCtx.Captcha.Verify(l.ctx, def.CaptchaTypeEmail, def.CaptchaUseBindAccount, in.CodeID, in.Code)
		if email == "" || email != in.Account {
			return nil, errors.Captcha
		}
		ui.Email = in.Account
	}
	err = relationDB.NewUserInfoRepo(l.ctx).Update(l.ctx, ui)
	return &sys.Empty{}, err
}
