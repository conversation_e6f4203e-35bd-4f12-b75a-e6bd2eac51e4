package usermanagelogic

import (
	"context"
	"fmt"
	"strconv"

	"gitee.com/unitedrhino/share/ctxs"
	"gitee.com/unitedrhino/share/def"
	"gitee.com/unitedrhino/share/errors"
	"gitee.com/unitedrhino/share/stores"
	"gitee.com/unitedrhino/share/utils"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/domain/workspace"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"
	"github.com/FREEZONEX/Tier0-Backend/share/users"
	"gorm.io/gorm"

	"github.com/zeromicro/go-zero/core/logx"
)

type UserRegisterLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
	uc *ctxs.UserCtx
}

func NewUserRegisterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UserRegisterLogic {
	return &UserRegisterLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
		uc:     ctxs.GetUserCtx(ctx),
	}
}

func (l *UserRegisterLogic) UserRegister(in *sys.UserRegisterReq) (*sys.UserRegisterResp, error) {
	return l.handle(in)
}

/*
将密码的md5和uid进行md5
*/
func MakePwd(salt string, pwd string, uid int64, isSha bool) string {
	if !isSha {
		pwd = utils.Sha256([]byte(pwd))
	}
	strUid := strconv.FormatInt(uid, 8)
	return utils.Sha256([]byte(pwd + strUid + salt))
}

func (l *UserRegisterLogic) handle(in *sys.UserRegisterReq) (*sys.UserRegisterResp, error) {
	ui := relationDB.SysUserInfo{
		UserID: l.svcCtx.UserID.GetSnowflakeId(),
	}
	ui.Password = MakePwd(l.svcCtx.Config.Login.PasswordSalt, in.Info.Password, ui.UserID, false)
	if in.Info == nil {
		return nil, errors.Parameter.AddMsgf("info must set")
	}
	if in.Info.UserName == "" {
		ui.UserName = utils.AnyToNullString(in.Info.UserName)
	}
	switch in.RegType {
	case users.RegEmail:
		email := l.svcCtx.Captcha.Verify(l.ctx, def.CaptchaTypeEmail, def.CaptchaUseRegister, in.CodeID, in.Code)
		if email == "" || email != in.Info.Email {
			return nil, errors.Captcha
		}
		ui.Email = in.Info.Email
		if !ui.UserName.Valid {
			ui.UserName = utils.AnyToNullString(ui.Email)
		}
		if in.Info.Password != "" {
			err := CheckPwd(l.svcCtx, in.Info.Password)
			if err != nil {
				return nil, err
			}
		}
	case users.RegGithub, users.RegGoogle:
		if in.Info == nil || in.Info.Email == "" {
			return nil, errors.Parameter.AddMsgf("email must set")
		}
		info := in.Info
		switch in.RegType {
		case users.RegGithub:
			if info.GithubPid == "" {
				return nil, errors.Parameter.AddMsgf("githubPid must set")
			}
			ui.GithubPid = info.GithubPid
		case users.RegGoogle:
			if info.GooglePid == "" {
				return nil, errors.Parameter.AddMsgf("googlePid must set")
			}
			ui.GooglePid = info.GooglePid
		}
		ui.Email = in.Info.Email
		if !ui.UserName.Valid {
			ui.UserName = utils.AnyToNullString(ui.Email)
		}
	default:
		return nil, errors.NotRealize.AddMsgf(in.RegType)
	}
	wsi := relationDB.SysWorkspaceInfo{
		Code:        fmt.Sprintf("u-%v", ui.UserID),
		Name:        "my workspace",
		Type:        workspace.TypeUser,
		AdminUserID: ui.UserID,
	}
	wu := relationDB.SysWorkspaceUser{
		WorkspaceCode: wsi.Code,
		UserID:        ui.UserID,
	}
	err := stores.GetCommonConn(l.ctx).Transaction(func(tx *gorm.DB) error {
		uidb := relationDB.NewUserInfoRepo(tx)
		err := uidb.Insert(l.ctx, &ui)
		if err != nil {
			return err
		}
		err = relationDB.NewWorkspaceInfoRepo(tx).Insert(l.ctx, &wsi)
		if err != nil {
			return err
		}
		err = relationDB.NewWorkspaceUserRepo(tx).Insert(l.ctx, &wu)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &sys.UserRegisterResp{UserID: ui.UserID}, nil
}
