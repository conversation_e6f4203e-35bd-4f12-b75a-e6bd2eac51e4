package usermanagelogic

import (
	"context"
	"gitee.com/unitedrhino/share/oss/common"
	"gitee.com/unitedrhino/share/utils"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"
	"github.com/zeromicro/go-zero/core/logx"
)

func UserInfoToPb(ctx context.Context, ui *relationDB.SysUserInfo, svcCtx *svc.ServiceContext) *sys.UserInfo {
	if ui.HeadImg != "" {
		var err error
		ui.HeadImg, err = svcCtx.OssClient.PrivateBucket().SignedGetUrl(ctx, ui.HeadImg, 24*60*60, common.OptionKv{})
		if err != nil {
			logx.WithContext(ctx).Errorf("%s.SignedGetUrl err:%v", utils.FuncName(), err)
		}
	}
	ui.Password = ""
	return utils.Copy[sys.UserInfo](ui)
}
