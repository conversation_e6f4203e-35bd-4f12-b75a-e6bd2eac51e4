package usermanagelogic

import (
	"context"
	"encoding/json"
	"fmt"
	"gitee.com/unitedrhino/share/caches"
	"gitee.com/unitedrhino/share/def"
	"gitee.com/unitedrhino/share/errors"
	"gitee.com/unitedrhino/share/utils"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/domain/user"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"
	"github.com/FREEZONEX/Tier0-Backend/share/users"
	"github.com/silenceper/wechat/v2/officialaccount/oauth"
	"github.com/spf13/cast"
	"github.com/zeromicro/go-zero/core/logx"
	"sync/atomic"
)

type LoginLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
	UiDB *relationDB.UserInfoRepo
}

func NewUserLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LoginLogic {
	return &LoginLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
		UiDB:   relationDB.NewUserInfoRepo(ctx),
	}
}

func (l *LoginLogic) getPwd(in *sys.UserLoginReq, uc *relationDB.SysUserInfo) error {

	//md5加密后的密码则通过二次md5加密再对比库中的密码
	password1 := MakePwd(l.svcCtx.Config.Login.PasswordSalt, in.Password, uc.UserID, true)
	if password1 != uc.Password {
		return errors.Password
	}

	return nil
}

var randID atomic.Uint32

func genID(ctx context.Context, nodeID int64) string {
	var token = uint32(nodeID) & 0xff
	token += randID.Add(1) << 8 & 0xfff00
	return fmt.Sprintf("%x", token)
}

func (l *LoginLogic) getRet(in *sys.UserLoginReq, ui *relationDB.SysUserInfo) (*sys.UserLoginResp, error) {
	id := genID(l.ctx, l.svcCtx.NodeID)
	jwtToken, _, err := user.GetTokenClaims(l.svcCtx.Config.Login.AccessSecret, ui.UserID, in.Ip, id)
	if err != nil {
		l.Error(err)
		return nil, errors.System.AddDetail(err)
	}
	ws, err := relationDB.NewWorkspaceInfoRepo(l.ctx).FindByFilter(l.ctx, relationDB.WorkspaceInfoFilter{UserID: ui.UserID}, nil)
	resp := &sys.UserLoginResp{
		Info:       UserInfoToPb(l.ctx, ui, l.svcCtx),
		Token:      jwtToken,
		Workspaces: utils.CopySlice[sys.WorkspaceInfo](ws),
	}
	err = l.svcCtx.UserToken.Login(l.ctx, ui.UserID, jwtToken)
	if err != nil {
		return nil, err
	}
	l.Infof("%s getRet=%+v", utils.FuncName(), resp)
	return resp, nil
}

func (l *LoginLogic) GetUserInfo(in *sys.UserLoginReq) (uc *relationDB.SysUserInfo, err error) {
	switch in.LoginType {
	case users.RegPwd:
		//todo: 安全校验防刷需要加上

		//if l.svcCtx.Captcha.Verify(l.ctx, def.CaptchaTypeImage, def.CaptchaUseLogin, in.CodeID, in.Code) == "" {
		//	return nil, errors.Captcha
		//}
		//if l.svcCtx.LoginLimit.PwdAccount.CheckLimit(l.ctx, in.Account) {
		//	return nil, errors.AccountOrIpForbidden.WithMsg("错误次数过多,请稍后再试")
		//}
		//ip := ctxs.GetUserCtxNoNil(l.ctx).IP
		//if ip != "" && l.svcCtx.LoginLimit.PwdIp.CheckLimit(l.ctx, ip) {
		//	return nil, errors.AccountOrIpForbidden.WithMsg("错误次数过多,请稍后再试")
		//}
		//limit := func() {
		//	l.svcCtx.LoginLimit.PwdAccount.LimitIt(l.ctx, in.Account)
		//	if ip != "" {
		//		l.svcCtx.LoginLimit.PwdIp.LimitIt(l.ctx, in.Ip)
		//	}
		//}
		uc, err = l.UiDB.FindOneByFilter(l.ctx, relationDB.UserInfoFilter{Accounts: []string{in.Account}})
		if err != nil {
			if errors.Cmp(err, errors.NotFind) { //未注册,自动注册
				return nil, errors.UnRegister
			}
			return nil, err
		}
		if err = l.getPwd(in, uc); err != nil {
			return nil, err
		}
		l.svcCtx.LoginLimit.PwdAccount.CleanLimit(l.ctx, in.Account)
	case users.RegGithub, users.RegGoogle:
		uc, err = l.UiDB.FindOneByFilter(l.ctx, relationDB.UserInfoFilter{Email: in.Account})
		if err != nil {
			if errors.Cmp(err, errors.NotFind) { //未注册,自动注册
				return nil, errors.UnRegister
			}
			return nil, err
		}
		switch in.LoginType {
		case users.RegGithub:
			if uc.GithubPid == "" {
				return nil, errors.UnBindAccount
			}
		case users.RegGoogle:
			if uc.GooglePid == "" {
				return nil, errors.UnBindAccount
			}
		}

	case users.RegEmail:
		email := l.svcCtx.Captcha.Verify(l.ctx, def.CaptchaTypeEmail, def.CaptchaUseLogin, in.CodeID, in.Code)
		if email == "" || email != in.Account {
			return nil, errors.Captcha
		}
		uc, err = l.UiDB.FindOneByFilter(l.ctx, relationDB.UserInfoFilter{Emails: []string{in.Account}})
		if errors.Cmp(err, errors.NotFind) { //未注册,自动注册
			return nil, errors.UnRegister
		}
	default:
		l.Error("%s LoginType=%s not support", utils.FuncName(), in.LoginType)
		return nil, errors.Parameter
	}
	l.Infof("%s uc=%#v err=%+v", utils.FuncName(), uc, err)
	return uc, err
}

func (l *LoginLogic) UserLogin(in *sys.UserLoginReq) (*sys.UserLoginResp, error) {
	l.Infof("%s req=%v", utils.FuncName(), utils.Fmt(in))

	ui, err := l.GetUserInfo(in)
	if err == nil {
		return l.getRet(in, ui)
	}
	if errors.Cmp(err, errors.NotFind) {
		return nil, errors.UnRegister
	}
	return nil, err
}

func GetAccount(ui *relationDB.SysUserInfo) string {
	var account = ui.UserName.String

	if account == "" {
		account = ui.Email
	}
	if account == "" {
		account = cast.ToString(ui.UserID)
	}
	return account
}

func gentLoginKey(code string) string {
	return fmt.Sprintf("sys:user:wxak:login:%s", code)
}

func StoreWxLoginResAccessToken(ctx context.Context, code string, tk *oauth.ResAccessToken) error {
	return caches.GetStore().SetexCtx(ctx, gentLoginKey(code), utils.MarshalNoErr(tk), 10*60)
}

func DelWxLoginResAccessToken(ctx context.Context, code string) error {
	_, err := caches.GetStore().DelCtx(ctx, gentLoginKey(code))
	return err
}

func GetWxLoginResAccessToken(ctx context.Context, code string) (*oauth.ResAccessToken, error) {
	ret, err := caches.GetStore().GetCtx(ctx, gentLoginKey(code))
	if err != nil {
		return nil, err
	}
	var val oauth.ResAccessToken
	err = json.Unmarshal([]byte(ret), &val)
	return &val, err
}

func gentRegisterKey(code string) string {
	return fmt.Sprintf("sys:user:wxak:register:%s", code)
}

func DelWxRegisterResAccessToken(ctx context.Context, code string) error {
	_, err := caches.GetStore().DelCtx(ctx, gentRegisterKey(code))
	return err
}

func StoreWxRegisterResAccessToken(ctx context.Context, code string, tk *oauth.ResAccessToken) error {
	DelWxLoginResAccessToken(ctx, code)
	return caches.GetStore().SetexCtx(ctx, gentRegisterKey(code), utils.MarshalNoErr(tk), 10*60)
}
func GetWxRegisterResAccessToken(ctx context.Context, code string) (*oauth.ResAccessToken, error) {
	ret, err := caches.GetStore().GetCtx(ctx, gentRegisterKey(code))
	if err != nil {
		return nil, err
	}
	var val oauth.ResAccessToken
	err = json.Unmarshal([]byte(ret), &val)
	return &val, err
}
