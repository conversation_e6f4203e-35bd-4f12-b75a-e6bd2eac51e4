package usermanagelogic

import (
	"context"
	"gitee.com/unitedrhino/share/errors"
	"gitee.com/unitedrhino/share/utils"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/domain/user"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"
	"github.com/FREEZONEX/Tier0-Backend/share/users"
	"github.com/maypok86/otter"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type CheckTokenLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
	UiDB *relationDB.UserInfoRepo
}

func NewUserCheckTokenLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CheckTokenLogic {
	return &CheckTokenLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
		UiDB:   relationDB.NewUserInfoRepo(ctx),
	}
}

func (l *CheckTokenLogic) UserCheckToken(in *sys.UserCheckTokenReq) (*sys.UserCheckTokenResp, error) {
	return l.userCheckToken(in)
}

var (
	openAuthCache otter.Cache[string, *sys.UserCheckTokenResp]
)

func init() {
	cache, err := otter.MustBuilder[string, *sys.UserCheckTokenResp](10_000).
		CollectStats().
		Cost(func(key string, value *sys.UserCheckTokenResp) uint32 {
			return 1
		}).
		WithTTL(time.Minute * 1).
		Build()
	logx.Must(err)
	openAuthCache = cache
}

func (l *CheckTokenLogic) userCheckToken(in *sys.UserCheckTokenReq) (*sys.UserCheckTokenResp, error) {
	var claim user.TokenClaims
	err := users.ParseToken(&claim, in.Token, l.svcCtx.Config.Login.AccessSecret)
	if err != nil {
		l.Errorf("%s parse token fail err=%s", utils.FuncName(), err.Error())
		return nil, err
	}
	err = l.svcCtx.UserToken.CheckToken(l.ctx, claim, in.Token)
	if err != nil {
		return nil, err
	}
	ui, err := relationDB.NewUserInfoRepo(l.ctx).FindOne(l.ctx, claim.UserID)
	if err != nil {
		return nil, err
	}
	wi, err := relationDB.NewWorkspaceInfoRepo(l.ctx).FindOneByFilter(l.ctx, relationDB.WorkspaceInfoFilter{UserID: claim.UserID, Code: in.WorkspaceCode})
	if err != nil {
		if errors.Cmp(err, errors.NotFind) {
			return nil, errors.Permissions.AddMsg("namespace has no permission")
		}
		return nil, err
	}
	ret := utils.Copy[sys.UserCheckTokenResp](ui)
	ret.WorkspaceCode = in.WorkspaceCode
	if wi.AdminUserID == ui.UserID {
		ret.IsWorkspaceAdmin = true
		ret.IsWorkspaceSuperAdmin = true
	}
	return ret, nil
}
