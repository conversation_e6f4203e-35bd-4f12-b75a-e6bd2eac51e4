package usermanagelogic

import (
	"context"
	"gitee.com/unitedrhino/share/ctxs"
	"gitee.com/unitedrhino/share/errors"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/repo/relationDB"

	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"

	"github.com/zeromicro/go-zero/core/logx"
)

type UserInfoGetOneLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUserInfoGetOneLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UserInfoGetOneLogic {
	return &UserInfoGetOneLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UserInfoGetOneLogic) UserInfoGetOne(in *sys.UserInfoGetOneReq) (*sys.UserInfo, error) {
	if err := ctxs.IsRoot(l.ctx); err == nil {
		ctxs.GetUserCtx(l.ctx).AllTenant = true
		defer func() {
			ctxs.GetUserCtx(l.ctx).AllTenant = false
		}()
	}
	if in.UserID == 0 && in.Account == "" {
		return &sys.UserInfo{}, errors.Parameter.AddMsg("userID with account must set ont")
	}
	ui, err := relationDB.NewUserInfoRepo(l.ctx).FindOneByFilter(l.ctx, relationDB.UserInfoFilter{UserIDs: []int64{in.UserID}, Accounts: []string{in.Account}})
	if err != nil {
		l.Logger.Error("UserInfoModel.FindOne err:%v", err)
		return nil, err
	}

	return UserInfoToPb(l.ctx, ui, l.svcCtx), nil
}
