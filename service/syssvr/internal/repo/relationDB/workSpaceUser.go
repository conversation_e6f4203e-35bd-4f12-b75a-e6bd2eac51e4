package relationDB

import (
	"context"
	"gitee.com/unitedrhino/share/stores"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

/*
这个是参考样例
使用教程:
1. 将example全局替换为模型的表名
2. 完善todo
*/

type WorkspaceUserRepo struct {
	db *gorm.DB
}

func NewWorkspaceUserRepo(in any) *WorkspaceUserRepo {
	return &WorkspaceUserRepo{db: stores.GetCommonConn(in)}
}

type WorkspaceUserFilter struct {
	UserID            int64
	WithWorkSpaceInfo bool
}

func (p WorkspaceUserRepo) fmtFilter(ctx context.Context, f WorkspaceUserFilter) *gorm.DB {
	db := p.db.WithContext(ctx)
	if f.UserID != 0 {
		db = db.Where("user_id = ?", f.UserID)
	}
	if f.WithWorkSpaceInfo {
		db = db.Preload("WorkSpaceInfo")
	}
	return db
}

func (p WorkspaceUserRepo) Insert(ctx context.Context, data *SysWorkspaceUser) error {
	result := p.db.WithContext(ctx).Create(data)
	return stores.ErrFmt(result.Error)
}

func (p WorkspaceUserRepo) FindOneByFilter(ctx context.Context, f WorkspaceUserFilter) (*SysWorkspaceUser, error) {
	var result SysWorkspaceUser
	db := p.fmtFilter(ctx, f)
	err := db.First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}
func (p WorkspaceUserRepo) FindByFilter(ctx context.Context, f WorkspaceUserFilter, page *stores.PageInfo) ([]*SysWorkspaceUser, error) {
	var results []*SysWorkspaceUser
	db := p.fmtFilter(ctx, f).Model(&SysWorkspaceUser{})
	db = page.ToGorm(db)
	err := db.Find(&results).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return results, nil
}

func (p WorkspaceUserRepo) CountByFilter(ctx context.Context, f WorkspaceUserFilter) (size int64, err error) {
	db := p.fmtFilter(ctx, f).Model(&SysWorkspaceUser{})
	err = db.Count(&size).Error
	return size, stores.ErrFmt(err)
}

func (p WorkspaceUserRepo) DeleteByFilter(ctx context.Context, f WorkspaceUserFilter) error {
	db := p.fmtFilter(ctx, f)
	err := db.Delete(&SysWorkspaceUser{}).Error
	return stores.ErrFmt(err)
}

func (p WorkspaceUserRepo) Delete(ctx context.Context, id int64) error {
	err := p.db.WithContext(ctx).Where("id = ?", id).Delete(&SysWorkspaceUser{}).Error
	return stores.ErrFmt(err)
}
func (p WorkspaceUserRepo) FindOne(ctx context.Context, id int64) (*SysWorkspaceUser, error) {
	var result SysWorkspaceUser
	err := p.db.WithContext(ctx).Where("id = ?", id).First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}

// 批量插入 LightStrategyDevice 记录
func (p WorkspaceUserRepo) MultiInsert(ctx context.Context, data []*SysWorkspaceUser) error {
	err := p.db.WithContext(ctx).Clauses(clause.OnConflict{UpdateAll: true}).Model(&SysWorkspaceUser{}).Create(data).Error
	return stores.ErrFmt(err)
}

func (d WorkspaceUserRepo) UpdateWithField(ctx context.Context, f WorkspaceUserFilter, updates map[string]any) error {
	db := d.fmtFilter(ctx, f)
	err := db.Model(&SysWorkspaceUser{}).Updates(updates).Error
	return stores.ErrFmt(err)
}
