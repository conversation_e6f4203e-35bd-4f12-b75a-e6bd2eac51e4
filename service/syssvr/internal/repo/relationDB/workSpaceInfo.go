package relationDB

import (
	"context"
	"gitee.com/unitedrhino/share/stores"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

/*
这个是参考样例
使用教程:
1. 将example全局替换为模型的表名
2. 完善todo
*/

type WorkspaceInfoRepo struct {
	db *gorm.DB
}

func NewWorkspaceInfoRepo(in any) *WorkspaceInfoRepo {
	return &WorkspaceInfoRepo{db: stores.GetCommonConn(in)}
}

type WorkspaceInfoFilter struct {
	UserID int64
	Code   string
}

func (p WorkspaceInfoRepo) fmtFilter(ctx context.Context, f WorkspaceInfoFilter) *gorm.DB {
	db := p.db.WithContext(ctx)
	if f.UserID != 0 {
		subQuery := p.db.Model(&SysWorkspaceUser{}).Select("code").Where("user_id = ?", f.UserID)
		db = db.Where("code in (?)",
			subQuery)
	}
	if f.Code != "" {
		db = db.Where("code = ?", f.Code)
	}
	return db
}

func (p WorkspaceInfoRepo) Insert(ctx context.Context, data *SysWorkspaceInfo) error {
	result := p.db.WithContext(ctx).Create(data)
	return stores.ErrFmt(result.Error)
}

func (p WorkspaceInfoRepo) FindOneByFilter(ctx context.Context, f WorkspaceInfoFilter) (*SysWorkspaceInfo, error) {
	var result SysWorkspaceInfo
	db := p.fmtFilter(ctx, f)
	err := db.First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}
func (p WorkspaceInfoRepo) FindByFilter(ctx context.Context, f WorkspaceInfoFilter, page *stores.PageInfo) ([]*SysWorkspaceInfo, error) {
	var results []*SysWorkspaceInfo
	db := p.fmtFilter(ctx, f).Model(&SysWorkspaceInfo{})
	db = page.ToGorm(db)
	err := db.Find(&results).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return results, nil
}

func (p WorkspaceInfoRepo) CountByFilter(ctx context.Context, f WorkspaceInfoFilter) (size int64, err error) {
	db := p.fmtFilter(ctx, f).Model(&SysWorkspaceInfo{})
	err = db.Count(&size).Error
	return size, stores.ErrFmt(err)
}

func (p WorkspaceInfoRepo) Update(ctx context.Context, data *SysWorkspaceInfo) error {
	err := p.db.WithContext(ctx).Where("id = ?", data.ID).Save(data).Error
	return stores.ErrFmt(err)
}

func (p WorkspaceInfoRepo) DeleteByFilter(ctx context.Context, f WorkspaceInfoFilter) error {
	db := p.fmtFilter(ctx, f)
	err := db.Delete(&SysWorkspaceInfo{}).Error
	return stores.ErrFmt(err)
}

func (p WorkspaceInfoRepo) Delete(ctx context.Context, id int64) error {
	err := p.db.WithContext(ctx).Where("id = ?", id).Delete(&SysWorkspaceInfo{}).Error
	return stores.ErrFmt(err)
}
func (p WorkspaceInfoRepo) FindOne(ctx context.Context, id int64) (*SysWorkspaceInfo, error) {
	var result SysWorkspaceInfo
	err := p.db.WithContext(ctx).Where("id = ?", id).First(&result).Error
	if err != nil {
		return nil, stores.ErrFmt(err)
	}
	return &result, nil
}

// 批量插入 LightStrategyDevice 记录
func (p WorkspaceInfoRepo) MultiInsert(ctx context.Context, data []*SysWorkspaceInfo) error {
	err := p.db.WithContext(ctx).Clauses(clause.OnConflict{UpdateAll: true}).Model(&SysWorkspaceInfo{}).Create(data).Error
	return stores.ErrFmt(err)
}

func (d WorkspaceInfoRepo) UpdateWithField(ctx context.Context, f WorkspaceInfoFilter, updates map[string]any) error {
	db := d.fmtFilter(ctx, f)
	err := db.Model(&SysWorkspaceInfo{}).Updates(updates).Error
	return stores.ErrFmt(err)
}
