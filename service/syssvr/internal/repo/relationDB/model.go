package relationDB

import (
	"database/sql"
	"gitee.com/unitedrhino/share/stores"
)

// 示例
type SysExample struct {
	ID int64 `gorm:"column:id;type:bigint;primary_key;AUTO_INCREMENT"` // id编号
}

// 用户登录信息表
type SysUserInfo struct {
	UserID        int64               `gorm:"column:user_id;primary_key;AUTO_INCREMENT;type:BIGINT;NOT NULL"`                  // 用户id
	UserName      sql.NullString      `gorm:"column:user_name;uniqueIndex:idx_sys_user_info_tc_un;type:VARCHAR(60)"`           // 登录用户名
	NickName      string              `gorm:"column:nick_name;type:VARCHAR(60);NOT NULL"`                                      // 用户的昵称
	Password      string              `gorm:"column:password;type:VARCHAR(128);NOT NULL"`                                      // 登录密码
	Email         string              `gorm:"column:email;uniqueIndex:idx_sys_user_info_tc_email;type:VARCHAR(255)"`           // 邮箱
	GithubPid     string              `gorm:"column:github_pid;uniqueIndex:idx_sys_user_info_tc_github_pid;type:VARCHAR(128)"` // 微信union id
	GooglePid     string              `gorm:"column:google_pid;uniqueIndex:idx_sys_user_info_tc_google_pid;type:VARCHAR(128)"` // 微信union id
	LastIP        string              `gorm:"column:last_ip;type:VARCHAR(128);NOT NULL"`                                       // 最后登录ip
	RegIP         string              `gorm:"column:reg_ip;type:VARCHAR(128);NOT NULL"`                                        // 注册ip
	HeadImg       string              `gorm:"column:head_img;type:VARCHAR(256);NOT NULL"`                                      // 用户头像
	WorkspaceUser []*SysWorkspaceUser `gorm:"foreignKey:UserID;references:UserID;"`
	stores.NoDelTime
	DeletedTime stores.DeletedTime `gorm:"column:deleted_time;default:0;uniqueIndex:idx_sys_user_info_tc_un;uniqueIndex:idx_sys_user_info_tc_email;uniqueIndex:idx_sys_user_info_tc_github_pid;uniqueIndex:idx_sys_user_info_tc_google_pid"`
}

func (m *SysUserInfo) TableName() string {
	return "sys_user_info"
}

// 租户信息表
type SysWorkspaceInfo struct {
	ID            int64               `gorm:"column:id;type:BIGINT;primary_key;AUTO_INCREMENT"`                                 // id编号
	Code          string              `gorm:"column:code;uniqueIndex:idx_sys_workspace_info_code;type:VARCHAR(100);NOT NULL"`   // 编码 用户空间为 u-userID 企业空间为c-随机数
	Name          string              `gorm:"column:name;uniqueIndex:idx_sys_workspace_info_name;type:VARCHAR(100);default:''"` // 名称
	Type          string              `gorm:"column:type;type:VARCHAR(100);NOT NULL"`                                           // 空间类型: user:用户空间 company:企业空间
	AdminUserID   int64               `gorm:"column:admin_user_id;type:BIGINT;NOT NULL"`                                        // 超级管理员id
	Desc          string              `gorm:"column:desc;type:VARCHAR(100);default:''"`                                         //应用描述
	WorkspaceUser []*SysWorkspaceUser `gorm:"foreignKey:WorkspaceCode;references:Code;"`
	stores.NoDelTime
	DeletedTime stores.DeletedTime `gorm:"column:deleted_time;default:0;uniqueIndex:idx_sys_workspace_info_code;uniqueIndex:idx_sys_workspace_info_name"`
}

func (m *SysWorkspaceInfo) TableName() string {
	return "sys_workspace_info"
}

type SysWorkspaceUser struct {
	ID            int64  `gorm:"column:id;type:BIGINT;primary_key;AUTO_INCREMENT"`                             // id编号
	WorkspaceCode string `gorm:"column:code;uniqueIndex:idx_sys_workspace_user_wu;type:VARCHAR(100);NOT NULL"` // id编号
	UserID        int64  `gorm:"column:user_id;uniqueIndex:idx_sys_workspace_user_wu;type:BIGINT;NOT NULL"`    // 超级管理员id
	stores.NoDelTime
	WorkSpaceInfo *SysWorkspaceInfo  `gorm:"foreignKey:Code;references:WorkspaceCode;"`
	UserInfo      *SysUserInfo       `gorm:"foreignKey:UserID;references:UserID;"`
	DeletedTime   stores.DeletedTime `gorm:"column:deleted_time;default:0;uniqueIndex:idx_sys_workspace_user_wu;"`
}

func (m *SysWorkspaceUser) TableName() string {
	return "sys_workspace_user"
}
