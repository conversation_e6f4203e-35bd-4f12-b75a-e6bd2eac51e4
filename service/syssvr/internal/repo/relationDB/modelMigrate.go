package relationDB

import (
	"context"

	"gitee.com/unitedrhino/share/conf"
	"gitee.com/unitedrhino/share/stores"
)

var NeedInitColumn bool

func Migrate(c conf.Database) error {
	if c.IsInitTable == false {
		return nil
	}
	db := stores.GetCommonConn(context.TODO())
	if !db.Migrator().HasTable(&SysWorkspaceInfo{}) {
		//需要初始化表
		NeedInitColumn = true
	}
	err := db.AutoMigrate(
		&SysWorkspaceInfo{},
		&SysWorkspaceUser{},
		&SysUserInfo{},
	)
	if err != nil {
		return err
	}

	return err
}

func migrateTableColumn() error {
	return nil
}
