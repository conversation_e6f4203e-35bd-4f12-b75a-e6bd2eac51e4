package cache

import (
	"context"
	"fmt"
	"gitee.com/unitedrhino/share/caches"
	"gitee.com/unitedrhino/share/errors"
	"gitee.com/unitedrhino/share/utils"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/domain/user"
	"time"
)

type UserToken struct {
}

func NewUserToken() *UserToken {
	return &UserToken{}
}

func (u *UserToken) GenUserKey(userID int64) string {
	return fmt.Sprintf("userToken:%v", userID)
}

func (u *UserToken) GenKey3(userID int64) string {
	return fmt.Sprintf("userToken:%v", userID)
}

func (u *UserToken) Login(ctx context.Context, userID int64, token string) error {
	err := caches.GetStore().Hset(u.GenUserKey(userID), token, utils.ToTimeStr(time.Now()))
	if err != nil {
		return errors.System.AddDetail(err)
	}
	return nil
}

func (u *UserToken) CheckToken(ctx context.Context, claims user.TokenClaims, token string) error {
	tk, err := caches.GetStore().Hget(u.GenUserKey(claims.UserID), token)
	if err != nil {
		return errors.NotLogin
	}
	t := utils.FmtDateStr(tk)
	if t.Before(time.Now().Add(-time.Hour * 24 * 30)) {
		return errors.TokenExpired
	}
	_ = caches.GetStore().Hset(u.GenUserKey(claims.UserID), token, utils.ToTimeStr(time.Now()))
	return nil
}

func (u *UserToken) KickedOut(ctx context.Context, userID int64) error {
	_, err := caches.GetStore().Del(u.GenUserKey(userID))
	if err != nil {
		return errors.System.AddDetail(err)
	}
	return nil
}
