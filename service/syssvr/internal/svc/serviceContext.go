package svc

import (
	"gitee.com/unitedrhino/core/share/domain/tenant"
	"gitee.com/unitedrhino/share/caches"
	"gitee.com/unitedrhino/share/clients/dingClient"
	"gitee.com/unitedrhino/share/oss"
	"gitee.com/unitedrhino/share/stores"
	"gitee.com/unitedrhino/share/tools"
	"gitee.com/unitedrhino/share/utils"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/config"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/repo/cache"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/repo/relationDB"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/kv"
	"os"
	"sync"
)

type CaptchaLimit struct {
	PhoneIp      *tools.Limit
	PhoneAccount *tools.Limit
	EmailIp      *tools.Limit
	EmailAccount *tools.Limit
}

type LoginLimit struct {
	PwdIp      *tools.Limit
	PwdAccount *tools.Limit
}

type ServiceContext struct {
	Config             config.Config
	ProjectID          *utils.SnowFlake
	AreaID             *utils.SnowFlake
	UserID             *utils.SnowFlake
	OssClient          *oss.Client
	Store              kv.Store
	Captcha            *cache.Captcha
	CaptchaLimit       CaptchaLimit
	LoginLimit         LoginLimit
	Cm                 *ClientsManage
	UsersCache         *cache.UserCache
	TenantCache        *caches.Cache[tenant.Info, string]
	UserCache          *caches.Cache[sys.UserInfo, int64]
	UserToken          *cache.UserToken
	DingStreamMap      map[string]*dingClient.StreamClient //key是租户号,value是需要同步的stream
	DingStreamMapMutex sync.RWMutex
	NodeID             int64
}

func NewServiceContext(c config.Config) *ServiceContext {
	stores.InitConn(c.Database)
	caches.InitStore(c.CacheRedis)
	err := relationDB.Migrate(c.Database)
	if err != nil {
		logx.Error("syssvr 数据库初始化失败 err", err)
		os.Exit(-1)
	}
	// 自动迁移数据库
	nodeID := utils.GetNodeID(c.CacheRedis, c.Name)
	ProjectID := utils.NewSnowFlake(nodeID)
	AreaID := utils.NewSnowFlake(nodeID)
	UserID := utils.NewSnowFlake(nodeID)
	store := kv.NewStore(c.CacheRedis)
	ossClient, err := oss.NewOssClient(c.OssConf)
	if err != nil {
		logx.Errorf("NewOss err err:%v", err)
		os.Exit(-1)
	}

	cl := CaptchaLimit{
		PhoneIp:      tools.NewLimit(c.CaptchaPhoneIpLimit, "captcha", "phone:ip", config.DefaultIpLimit),
		PhoneAccount: tools.NewLimit(c.CaptchaPhoneIpLimit, "captcha", "phone:account", config.DefaultAccountLimit),
		EmailIp:      tools.NewLimit(c.CaptchaPhoneIpLimit, "captcha", "email:ip", config.DefaultIpLimit),
		EmailAccount: tools.NewLimit(c.CaptchaPhoneIpLimit, "captcha", "email:account", config.DefaultAccountLimit),
	}
	ll := LoginLimit{
		PwdIp:      tools.NewLimit(c.LoginPwdIpLimit, "login", "pwd:ip", config.DefaultIpLimit),
		PwdAccount: tools.NewLimit(c.LoginPwdAccountLimit, "login", "pwd:account", config.DefaultAccountLimit),
	}
	return &ServiceContext{
		Captcha:       cache.NewCaptcha(store),
		UserToken:     cache.NewUserToken(),
		Cm:            NewClients(c),
		Config:        c,
		CaptchaLimit:  cl,
		LoginLimit:    ll,
		ProjectID:     ProjectID,
		OssClient:     ossClient,
		AreaID:        AreaID,
		UserID:        UserID,
		Store:         store,
		NodeID:        nodeID,
		DingStreamMap: make(map[string]*dingClient.StreamClient),
	}
}
