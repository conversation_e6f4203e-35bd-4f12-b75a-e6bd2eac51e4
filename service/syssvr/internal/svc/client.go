package svc

import (
	"context"
	"gitee.com/unitedrhino/share/clients/dingClient"
	"gitee.com/unitedrhino/share/clients/wxClient"
	"gitee.com/unitedrhino/share/ctxs"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/config"
	"github.com/zeromicro/go-zero/core/syncx"
	"sync"
)

type Clients struct {
	WxOfficial  *wxClient.WxOfficialAccount
	MiniProgram *wxClient.MiniProgram
	DingMini    *dingClient.DingTalk
}
type ClientsManage struct {
	Config config.Config
	sf     syncx.SingleFlight
}

var (
	tc = sync.Map{}
)

func NewClients(c config.Config) *ClientsManage {
	return &ClientsManage{Config: c, sf: syncx.NewSingleFlight()}
}

func (c *ClientsManage) ClearClients(ctx context.Context, appCode string) error {
	uc := ctxs.GetUserCtx(ctx)
	if appCode == "" {
		appCode = uc.AppCode
	}
	var tenantCode = uc.TenantCode
	tc.Delete(tenantCode + appCode)
	return nil
}

func (c *ClientsManage) GetClients(ctx context.Context, appCode string) (Clients, error) {
	uc := ctxs.GetUserCtx(ctx)
	if appCode == "" {
		appCode = uc.AppCode
	}
	var tenantCode = uc.TenantCode
	var key = tenantCode + ":" + appCode
	val, ok := tc.Load(tenantCode + appCode)
	if ok {
		return val.(Clients), nil
	}
	cli, err := c.sf.Do(key, func() (any, error) {

		var cli Clients
		tc.Store(tenantCode, cli)
		return cli, nil
	})

	return cli.(Clients), err
}
