package user

import (
	"github.com/golang-jwt/jwt/v5"
	"time"
)

type Token struct {
	UserID      int64
	CreatedTime int64
	CreatedIp   string
	ExpiredTime int64
	Token       string
}

type TokenClaims struct {
	UserID int64
	IP     string
	jwt.RegisteredClaims
}

func GetTokenClaims(secretKey string, userID int64, ip string, id string) (string, TokenClaims, error) {
	IssuedAt := jwt.NewNumericDate(time.Now())
	claims := TokenClaims{
		UserID: userID,
		IP:     ip,
		RegisteredClaims: jwt.RegisteredClaims{
			IssuedAt: IssuedAt,
			ID:       id,
		},
	}
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tk, err := token.SignedString([]byte(secretKey))
	return tk, claims, err
}
