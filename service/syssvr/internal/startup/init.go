package startup

import (
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
)

func Init(svcCtx *svc.ServiceContext) {
	//ctx := ctxs.WithRoot(context.Background())
	//utils.Go(ctx, func() {
	//	list, err := relationDB.NewTenantInfoRepo(ctx).FindByFilter(ctx, relationDB.TenantInfoFilter{}, nil)
	//	logx.Must(err)
	//	err = coreCache.InitTenant(ctx, logic.ToTenantInfoCaches(list)...)
	//	logx.Must(err)
	//})
	VersionUpdate(svcCtx)
	InitCache(svcCtx)
	TableInit(svcCtx)
	InitEventBus(svcCtx)
	InitSync(svcCtx)
}

func VersionUpdate(svcCtx *svc.ServiceContext) {

}

func TableInit(svcCtx *svc.ServiceContext) {
	return
}

func InitSync(svcCtx *svc.ServiceContext) {

}

func InitCache(svcCtx *svc.ServiceContext) {

}

func InitEventBus(svcCtx *svc.ServiceContext) {
}
