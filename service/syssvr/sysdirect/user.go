package sysdirect

import (
	//"github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/ops"
	client "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/usermanage"
	//opsServer "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/ops"
	server "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/usermanage"
	//clientNotify "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/notifymanage"
	//serverNotify "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/notifymanage"
	//
	//clientDeptM "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/departmentmanage"
	//serverDeptM "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/departmentmanage"
	//
	//clientRole "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/rolemanage"
	//serverRole "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/rolemanage"
	//
	//clientAccess "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/accessmanage"
	//serverAccess "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/accessmanage"
	//
	//clientData "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/datamanage"
	//serverData "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/datamanage"
	//
	//clientDict "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/dictmanage"
	//serverDict "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/dictmanage"
	//
	//clientModule "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/modulemanage"
	//serverModule "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/modulemanage"
	//
	//clientLog "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/log"
	//serverLog "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/log"
	//
	//clientCommon "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/common"
	//serverCommon "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/common"
	//
	//clientApp "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/appmanage"
	//serverApp "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/appmanage"
	//
	//clientTenant "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/tenantmanage"
	//serverTenant "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/tenantmanage"
	//
	//clientProject "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/projectmanage"
	//serverProject "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/projectmanage"
	//
	//clientArea "github.com/FREEZONEX/Tier0-Backend/service/syssvr/client/areamanage"
	//serverArea "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/areamanage"
)

func NewUser(runSvr bool) client.UserManage {
	svcCtx := GetSvcCtx()
	if runSvr {
		RunServer(svcCtx)
	}
	return client.NewDirectUserManage(svcCtx, server.NewUserManageServer(svcCtx))
}

//
//func NewRole(runSvr bool) clientRole.RoleManage {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientRole.NewDirectRoleManage(svcCtx, serverRole.NewRoleManageServer(svcCtx))
//}
//func NewAccess(runSvr bool) clientAccess.AccessManage {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientAccess.NewDirectAccessManage(svcCtx, serverAccess.NewAccessManageServer(svcCtx))
//}
//
//func NewData(runSvr bool) clientData.DataManage {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientData.NewDirectDataManage(svcCtx, serverData.NewDataManageServer(svcCtx))
//}
//
//func NewDict(runSvr bool) clientDict.DictManage {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientDict.NewDirectDictManage(svcCtx, serverDict.NewDictManageServer(svcCtx))
//}
//
//func NewModule(runSvr bool) clientModule.ModuleManage {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientModule.NewDirectModuleManage(svcCtx, serverModule.NewModuleManageServer(svcCtx))
//}
//
//func NewCommon(runSvr bool) clientCommon.Common {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientCommon.NewDirectCommon(svcCtx, serverCommon.NewCommonServer(svcCtx))
//}
//
//func NewLog(runSvr bool) clientLog.Log {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientLog.NewDirectLog(svcCtx, serverLog.NewLogServer(svcCtx))
//}
//
//func NewApp(runSvr bool) clientApp.AppManage {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientApp.NewDirectAppManage(svcCtx, serverApp.NewAppManageServer(svcCtx))
//}
//
//func NewTenantManage(runSvr bool) clientTenant.TenantManage {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientTenant.NewDirectTenantManage(svcCtx, serverTenant.NewTenantManageServer(svcCtx))
//}
//
//func NewProjectManage(runSvr bool) clientProject.ProjectManage {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientProject.NewDirectProjectManage(svcCtx, serverProject.NewProjectManageServer(svcCtx))
//}
//func NewAreaManage(runSvr bool) clientArea.AreaManage {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientArea.NewDirectAreaManage(svcCtx, serverArea.NewAreaManageServer(svcCtx))
//}
//
//func NewOps(runSvr bool) ops.Ops {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return ops.NewDirectOps(svcCtx, opsServer.NewOpsServer(svcCtx))
//}
//
//func NewNotify(runSvr bool) clientNotify.NotifyManage {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientNotify.NewDirectNotifyManage(svcCtx, serverNotify.NewNotifyManageServer(svcCtx))
//}
//
//func NewDeptM(runSvr bool) clientDeptM.DepartmentManage {
//	svcCtx := GetSvcCtx()
//	if runSvr {
//		RunServer(svcCtx)
//	}
//	return clientDeptM.NewDirectDepartmentManage(svcCtx, serverDeptM.NewDepartmentManageServer(svcCtx))
//}
