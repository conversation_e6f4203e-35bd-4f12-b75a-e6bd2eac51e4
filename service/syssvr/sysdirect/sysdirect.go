package sysdirect

import (
	"context"
	"fmt"
	"gitee.com/unitedrhino/share/services"
	"gitee.com/unitedrhino/share/utils"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/config"
	commonServer "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/common"
	usermanageServer "github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/server/usermanage"
	"github.com/FREEZONEX/Tier0-Backend/share/interceptors"

	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/startup"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"
	"github.com/zeromicro/go-zero/core/logx"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"
	"sync"
)

type Config = config.Config

var (
	ctxSvc     *svc.ServiceContext
	svcOnce    sync.Once
	runSvrOnce sync.Once
	c          config.Config
)

func GetSvcCtx() *svc.ServiceContext {
	svcOnce.Do(func() {
		utils.ConfMustLoad("etc/sys.yaml", &c)
		ctxSvc = svc.NewServiceContext(c)
		startup.Init(ctxSvc)
		logx.Infof("enabled syssvr")
	})
	return ctxSvc
}

// RunServer 如果是直连模式,同时提供Grpc的能力
func RunServer(svcCtx *svc.ServiceContext) {
	runSvrOnce.Do(func() {
		utils.Go(context.Background(), func() {
			Run(svcCtx)
		})
	})
}

func Run(svcCtx *svc.ServiceContext) {
	c := svcCtx.Config
	s := services.MustNewServer(c.RpcServerConf, func(grpcServer *grpc.Server) {
		sys.RegisterUserManageServer(grpcServer, usermanageServer.NewUserManageServer(svcCtx))
		sys.RegisterCommonServer(grpcServer, commonServer.NewCommonServer(svcCtx))
		//sys.RegisterAccessManageServer(grpcServer, accessmanageServer.NewAccessManageServer(svcCtx))
		//sys.RegisterRoleManageServer(grpcServer, rolemanageServer.NewRoleManageServer(svcCtx))
		//sys.RegisterAppManageServer(grpcServer, appmanageServer.NewAppManageServer(svcCtx))
		//sys.RegisterModuleManageServer(grpcServer, modulemanageServer.NewModuleManageServer(svcCtx))
		//sys.RegisterCommonServer(grpcServer, commonServer.NewCommonServer(svcCtx))
		//sys.RegisterLogServer(grpcServer, logServer.NewLogServer(svcCtx))
		//sys.RegisterProjectManageServer(grpcServer, projectmanageServer.NewProjectManageServer(svcCtx))
		//sys.RegisterAreaManageServer(grpcServer, areamanageServer.NewAreaManageServer(svcCtx))
		//sys.RegisterTenantManageServer(grpcServer, tenantmanageServer.NewTenantManageServer(svcCtx))
		//sys.RegisterDataManageServer(grpcServer, datamanageServer.NewDataManageServer(svcCtx))
		//sys.RegisterOpsServer(grpcServer, opsServer.NewOpsServer(svcCtx))
		//sys.RegisterNotifyManageServer(grpcServer, notifymanageServer.NewNotifyManageServer(svcCtx))
		//sys.RegisterDepartmentManageServer(grpcServer, deptMServer.NewDepartmentManageServer(svcCtx))
		//sys.RegisterDictManageServer(grpcServer, dictMServer.NewDictManageServer(svcCtx))
		//if c.Mode == service.DevMode || c.Mode == service.TestMode {
		reflection.Register(grpcServer)
		//}
	})
	defer s.Stop()
	s.AddUnaryInterceptors(interceptors.Ctxs, interceptors.Error)
	fmt.Printf("Starting rpc server at %s...\n", c.ListenOn)
	s.Start()
}
