syntax = "proto3";
option go_package = "pb/sys";
import "google/protobuf/wrappers.proto";

package sys;

service Common{
  rpc uploadUrlGetMap(CommonUploadUrlGetMapReq)returns(CommonUploadUrlGetMapResp);
  rpc uploadUrlGetOne(CommonUploadUrlGetOneReq)returns(CommonUploadUrlGetOneResp);
}


service UserManage {
  rpc userInfoUpdate(userInfoUpdateReq) returns(Empty);//更新用户基本数据
  rpc userInfoGetOne(UserInfoGetOneReq) returns(UserInfo);//获取用户信息
  rpc userInfoDelete(UserInfoDeleteReq) returns(Empty);//刪除用戶
  rpc userLogin(UserLoginReq) returns(UserLoginResp);
  rpc userForgetPwd(UserForgetPwdReq)returns(Empty);
  rpc userCaptcha(UserCaptchaReq)returns(UserCaptchaResp);
  rpc userCheckToken(UserCheckTokenReq) returns(UserCheckTokenResp);
  rpc userRegister(UserRegisterReq) returns(UserRegisterResp);
  rpc userChangePwd(UserChangePwdReq) returns(Empty);
  rpc userCodeToUserID(UserCodeToUserIDReq)returns(UserCodeToUserIDResp);//第三方的code获取用户的id
  rpc userBindAccount(UserBindAccountReq)returns(Empty);
}

service Workspace{
  rpc workspaceUpdate(Empty) returns(Empty);//更新用户基本数据

}

message DateRange{
  string start = 1;
  string end = 2;
}

message TimeRange{
  int64 start = 1;
  int64 end = 2;
}

message Empty {
}

message PageInfo {
  int64 page = 1;
  int64 size = 2;
  //排序信息
  repeated OrderBy orders = 3;
  message OrderBy {
    //排序的字段名
    string field = 1;
    //排序方式：0 aes, 1 desc
    int64 sort = 2;
  }
}

message CompareString{
  string CmpType =1;//"=":相等 "!=":不相等 ">":大于">=":大于等于"<":小于"<=":小于等于 "like":模糊查询
  string value =2;//值
}
message CompareInt64{
  string CmpType =1;//"=":相等 "!=":不相等 ">":大于">=":大于等于"<":小于"<=":小于等于 "like":模糊查询
  int64 value =2;//值
}

message WithID{
  int64 id = 1;
}

message WithIDCode{
  int64 id = 1;
  string code=2;
}

message WithCode{
  string code=1;
}

message WithAppCodeID{
  int64 id = 1;
  string appCode=2;
  string code =3;//租户code
}



message    CommonUploadUrlGetMapReq{
  map<string,string> fileInfo =1;//key 为唯一id, value为fileName
}
message CommonUploadUrlGetMapResp {
  map<string,CommonUploadUrlGetOneResp> fileInfo =2;//key 为唯一id
}

message  CommonUploadUrlGetOneReq{
  string fileName =2;//文件名称
}

message  CommonUploadUrlGetOneResp{
  string uploadUrl =1; //上传的文件地址
  string filePath =2;//oss的路径,用来传给业务
}



message WorkspaceInfo{
  int64 id =1; //工作空间id
  string name =2;//名称
  string code =3;//编码 用户空间为 u-userID 企业空间为c-随机数
  string type =4;//空间类型: user:用户空间 company:企业空间
  int64 adminUserID =5;//管理员的用户ID
  google.protobuf.StringValue desc =6;
}




message UserInfoCreateReq{
  UserInfo info = 1;
  repeated int64 roleIDs =2;
}




message userInfoUpdateReq{
  UserInfo info =1;
  bool WithRoot =2;//root可以修改所有字段
}

message UserInfoGetOneReq{
  int64 userID=1;
  string account =2;//完全匹配
}


message UserInfoDeleteReq{
  int64 userID = 1;
}

message JwtToken{
  string accessToken = 1;
  int64 accessExpire = 2;
  int64 refreshAfter = 3;
}

message UserCaptchaReq{
  string account = 1;//短信验证时填写手机号,邮箱验证时填写邮箱
  string type = 2;//验证方式:短信验证,图片验证码,邮箱验证(email)
  string use = 3;//用途 login 登录,register 注册,changePwd 修改密码,bindAccount 绑定账号,forgetPwd 忘记密码
  string codeID = 4;//验证码编码
  string code = 5;//验证码
  string ip = 6;//透传请求的 ip
}

message UserCaptchaResp{
  string codeID = 1;//验证码编码
  string code = 2;//验证码--只有图片验证码由apisvr生成,其他方式返回空
  int64 expire = 3;//过期时间
}


message UserForgetPwdReq{
  string account = 1; //登录账号(支持用户名,邮箱,手机号登录) 账号密码登录时需要填写
  string  type = 2;//账号类型: email:邮箱 phone:手机号
  string password =3;//密码: 原始明文
  string code = 5;//验证码    微信登录填code
  string codeID = 6;//验证码编号 微信登录填state
}

message UserCodeToUserIDReq{
  string loginType =4;//验证类型 phone 手机号 wxOpen 微信开放平台 wxIn 微信内 wxMiniP 微信小程序 pwd 账号密码
  string code = 5;//验证码    微信登录填code
}

message UserCodeToUserIDResp{
  string openID =1;
  string unionID =2;
}

message  UserChangePwdReq{
  string  type = 1;//验证类型: email:邮箱 phone:手机号 pwd:原密码
  string oldPassword=2;//输入原密码修改密码
  string password =3;//新密码
  string code = 5;//验证码    微信登录填code
  string codeID = 6;//验证码编号 微信登录填state
}

message UserLoginReq{
  string account = 1; //登录账号(支持用户名,邮箱登录)  oauth2(google, github) 登录填写 邮箱
  string password =3;//密码，sha256 密码登录时需要填写
  string loginType =4;//验证类型 email 邮箱验证码 google Google账号 github  pwd 账号密码
  string code = 5;//验证码    微信登录填code
  string codeID = 6;//验证码编号 微信登录填state
  string ip = 7;//透传请求的 ip
}

message UserLoginResp{
  UserInfo info = 1;
  string token =2;
  repeated WorkspaceInfo workspaces =3;//拥有的工作空间列表
}

//登录信息注册,注册第一步(注册核心登录信息) 返回一个jwt用来第二步注册
message UserRegisterReq{
  string regType =1; //注册方式:	 email 邮箱验证码 google Google账号 github
  string code =2;    //验证码 邮箱的方式需要填写
  string codeID =3;  //验证码编号 邮箱的方式需要填写
  RegUserInfo info = 5;//填写用户信息
}

message UserRegisterResp{
  int64 userID = 1;
}

message UserBindAccountReq{
  string type =1;//验证方式:phone手机号注册 wxOpen 微信开放平台登录 wxIn 微信内 wxMiniP 微信小程序
  string account = 2; //手机号,邮箱需要填写
  string code = 3;//验证码    微信登录填code
  string codeID = 4;//验证码编号 微信登录填state
}

//用户属性信息
message RegUserInfo{
  string userName =2;
  string password =3;//密码原文
  string email = 4;
  string githubPid =5;//如果是github自动注册的需要填写,填写过之后不可修改
  string googlePid =6;//如果是google自动注册的需要填写,填写过之后不可修改
  string regIP =8;//只读
}

//用户属性信息
message UserInfo{
  int64  userID = 1;
  string userName =2;
  string nickName =9;//昵称
  string password =3;//只读
  string email = 4;
  string githubPid =5;//如果是github自动注册的需要填写,填写过之后不可修改
  string googlePid =6;//如果是google自动注册的需要填写,填写过之后不可修改
  string lastIP =7;//只读
  string regIP =8;//只读
  int64  createdTime =15;//只读
  string headImg =19;
}



message UserCreateResp{
  int64 userID = 1;//用户id
}

message UserCheckTokenReq{
  string token = 1;
  string workspaceCode =2;
  string ip = 3;
}

message UserCheckTokenResp{
    string workspaceCode =1;
    bool   isSuperAdmin =2;//全局超管
    bool   isAdmin =3;//全局管理员
    bool   isWorkspaceSuperAdmin =4;//工作空间超管,拥有者才能是超管
    bool   isWorkspaceAdmin =5;//工作空间管理员
    int64  userID =6;//用户id
    string userName =7;
    string email =8;
}