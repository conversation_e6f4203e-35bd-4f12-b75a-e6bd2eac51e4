// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.4
// source: proto/sys.proto

package sys

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	wrapperspb "google.golang.org/protobuf/types/known/wrapperspb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DateRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start string `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`
	End   string `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *DateRange) Reset() {
	*x = DateRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateRange) ProtoMessage() {}

func (x *DateRange) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateRange.ProtoReflect.Descriptor instead.
func (*DateRange) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{0}
}

func (x *DateRange) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *DateRange) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

type TimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Start int64 `protobuf:"varint,1,opt,name=start,proto3" json:"start,omitempty"`
	End   int64 `protobuf:"varint,2,opt,name=end,proto3" json:"end,omitempty"`
}

func (x *TimeRange) Reset() {
	*x = TimeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRange) ProtoMessage() {}

func (x *TimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRange.ProtoReflect.Descriptor instead.
func (*TimeRange) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{1}
}

func (x *TimeRange) GetStart() int64 {
	if x != nil {
		return x.Start
	}
	return 0
}

func (x *TimeRange) GetEnd() int64 {
	if x != nil {
		return x.End
	}
	return 0
}

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{2}
}

type PageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size int64 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	// 排序信息
	Orders []*PageInfo_OrderBy `protobuf:"bytes,3,rep,name=orders,proto3" json:"orders,omitempty"`
}

func (x *PageInfo) Reset() {
	*x = PageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageInfo) ProtoMessage() {}

func (x *PageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageInfo.ProtoReflect.Descriptor instead.
func (*PageInfo) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{3}
}

func (x *PageInfo) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageInfo) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *PageInfo) GetOrders() []*PageInfo_OrderBy {
	if x != nil {
		return x.Orders
	}
	return nil
}

type CompareString struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CmpType string `protobuf:"bytes,1,opt,name=CmpType,proto3" json:"CmpType,omitempty"` //"=":相等 "!=":不相等 ">":大于">=":大于等于"<":小于"<=":小于等于 "like":模糊查询
	Value   string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`     //值
}

func (x *CompareString) Reset() {
	*x = CompareString{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompareString) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompareString) ProtoMessage() {}

func (x *CompareString) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompareString.ProtoReflect.Descriptor instead.
func (*CompareString) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{4}
}

func (x *CompareString) GetCmpType() string {
	if x != nil {
		return x.CmpType
	}
	return ""
}

func (x *CompareString) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type CompareInt64 struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CmpType string `protobuf:"bytes,1,opt,name=CmpType,proto3" json:"CmpType,omitempty"` //"=":相等 "!=":不相等 ">":大于">=":大于等于"<":小于"<=":小于等于 "like":模糊查询
	Value   int64  `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`    //值
}

func (x *CompareInt64) Reset() {
	*x = CompareInt64{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CompareInt64) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CompareInt64) ProtoMessage() {}

func (x *CompareInt64) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CompareInt64.ProtoReflect.Descriptor instead.
func (*CompareInt64) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{5}
}

func (x *CompareInt64) GetCmpType() string {
	if x != nil {
		return x.CmpType
	}
	return ""
}

func (x *CompareInt64) GetValue() int64 {
	if x != nil {
		return x.Value
	}
	return 0
}

type WithID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *WithID) Reset() {
	*x = WithID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithID) ProtoMessage() {}

func (x *WithID) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithID.ProtoReflect.Descriptor instead.
func (*WithID) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{6}
}

func (x *WithID) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type WithIDCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id   int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Code string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *WithIDCode) Reset() {
	*x = WithIDCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithIDCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithIDCode) ProtoMessage() {}

func (x *WithIDCode) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithIDCode.ProtoReflect.Descriptor instead.
func (*WithIDCode) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{7}
}

func (x *WithIDCode) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WithIDCode) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type WithCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *WithCode) Reset() {
	*x = WithCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithCode) ProtoMessage() {}

func (x *WithCode) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithCode.ProtoReflect.Descriptor instead.
func (*WithCode) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{8}
}

func (x *WithCode) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type WithAppCodeID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id      int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	AppCode string `protobuf:"bytes,2,opt,name=appCode,proto3" json:"appCode,omitempty"`
	Code    string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"` //租户code
}

func (x *WithAppCodeID) Reset() {
	*x = WithAppCodeID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithAppCodeID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithAppCodeID) ProtoMessage() {}

func (x *WithAppCodeID) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithAppCodeID.ProtoReflect.Descriptor instead.
func (*WithAppCodeID) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{9}
}

func (x *WithAppCodeID) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WithAppCodeID) GetAppCode() string {
	if x != nil {
		return x.AppCode
	}
	return ""
}

func (x *WithAppCodeID) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type CommonUploadUrlGetMapReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileInfo map[string]string `protobuf:"bytes,1,rep,name=fileInfo,proto3" json:"fileInfo,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //key 为唯一id, value为fileName
}

func (x *CommonUploadUrlGetMapReq) Reset() {
	*x = CommonUploadUrlGetMapReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonUploadUrlGetMapReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonUploadUrlGetMapReq) ProtoMessage() {}

func (x *CommonUploadUrlGetMapReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonUploadUrlGetMapReq.ProtoReflect.Descriptor instead.
func (*CommonUploadUrlGetMapReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{10}
}

func (x *CommonUploadUrlGetMapReq) GetFileInfo() map[string]string {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

type CommonUploadUrlGetMapResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileInfo map[string]*CommonUploadUrlGetOneResp `protobuf:"bytes,2,rep,name=fileInfo,proto3" json:"fileInfo,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //key 为唯一id
}

func (x *CommonUploadUrlGetMapResp) Reset() {
	*x = CommonUploadUrlGetMapResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonUploadUrlGetMapResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonUploadUrlGetMapResp) ProtoMessage() {}

func (x *CommonUploadUrlGetMapResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonUploadUrlGetMapResp.ProtoReflect.Descriptor instead.
func (*CommonUploadUrlGetMapResp) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{11}
}

func (x *CommonUploadUrlGetMapResp) GetFileInfo() map[string]*CommonUploadUrlGetOneResp {
	if x != nil {
		return x.FileInfo
	}
	return nil
}

type CommonUploadUrlGetOneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FileName string `protobuf:"bytes,2,opt,name=fileName,proto3" json:"fileName,omitempty"` //文件名称
}

func (x *CommonUploadUrlGetOneReq) Reset() {
	*x = CommonUploadUrlGetOneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonUploadUrlGetOneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonUploadUrlGetOneReq) ProtoMessage() {}

func (x *CommonUploadUrlGetOneReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonUploadUrlGetOneReq.ProtoReflect.Descriptor instead.
func (*CommonUploadUrlGetOneReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{12}
}

func (x *CommonUploadUrlGetOneReq) GetFileName() string {
	if x != nil {
		return x.FileName
	}
	return ""
}

type CommonUploadUrlGetOneResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UploadUrl string `protobuf:"bytes,1,opt,name=uploadUrl,proto3" json:"uploadUrl,omitempty"` //上传的文件地址
	FilePath  string `protobuf:"bytes,2,opt,name=filePath,proto3" json:"filePath,omitempty"`   //oss的路径,用来传给业务
}

func (x *CommonUploadUrlGetOneResp) Reset() {
	*x = CommonUploadUrlGetOneResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CommonUploadUrlGetOneResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonUploadUrlGetOneResp) ProtoMessage() {}

func (x *CommonUploadUrlGetOneResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonUploadUrlGetOneResp.ProtoReflect.Descriptor instead.
func (*CommonUploadUrlGetOneResp) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{13}
}

func (x *CommonUploadUrlGetOneResp) GetUploadUrl() string {
	if x != nil {
		return x.UploadUrl
	}
	return ""
}

func (x *CommonUploadUrlGetOneResp) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

type WorkspaceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64                   `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                   //工作空间id
	Name        string                  `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                //名称
	Code        string                  `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`                //编码 用户空间为 u-userID 企业空间为c-随机数
	Type        string                  `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`                //空间类型: user:用户空间 company:企业空间
	AdminUserID int64                   `protobuf:"varint,5,opt,name=adminUserID,proto3" json:"adminUserID,omitempty"` //管理员的用户ID
	Desc        *wrapperspb.StringValue `protobuf:"bytes,6,opt,name=desc,proto3" json:"desc,omitempty"`
}

func (x *WorkspaceInfo) Reset() {
	*x = WorkspaceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WorkspaceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WorkspaceInfo) ProtoMessage() {}

func (x *WorkspaceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WorkspaceInfo.ProtoReflect.Descriptor instead.
func (*WorkspaceInfo) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{14}
}

func (x *WorkspaceInfo) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *WorkspaceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *WorkspaceInfo) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *WorkspaceInfo) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *WorkspaceInfo) GetAdminUserID() int64 {
	if x != nil {
		return x.AdminUserID
	}
	return 0
}

func (x *WorkspaceInfo) GetDesc() *wrapperspb.StringValue {
	if x != nil {
		return x.Desc
	}
	return nil
}

type UserWorkspace struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"` //名称
	Code string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"` //编码 用户空间为 u-userID 企业空间为c-随机数
	Type string `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"` //空间类型: user:用户空间 company:企业空间
}

func (x *UserWorkspace) Reset() {
	*x = UserWorkspace{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserWorkspace) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserWorkspace) ProtoMessage() {}

func (x *UserWorkspace) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserWorkspace.ProtoReflect.Descriptor instead.
func (*UserWorkspace) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{15}
}

func (x *UserWorkspace) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserWorkspace) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserWorkspace) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type UserInfoCreateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info    *UserInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	RoleIDs []int64   `protobuf:"varint,2,rep,packed,name=roleIDs,proto3" json:"roleIDs,omitempty"`
}

func (x *UserInfoCreateReq) Reset() {
	*x = UserInfoCreateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfoCreateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoCreateReq) ProtoMessage() {}

func (x *UserInfoCreateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoCreateReq.ProtoReflect.Descriptor instead.
func (*UserInfoCreateReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{16}
}

func (x *UserInfoCreateReq) GetInfo() *UserInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *UserInfoCreateReq) GetRoleIDs() []int64 {
	if x != nil {
		return x.RoleIDs
	}
	return nil
}

type UserInfoUpdateReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info     *UserInfo `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	WithRoot bool      `protobuf:"varint,2,opt,name=WithRoot,proto3" json:"WithRoot,omitempty"` //root可以修改所有字段
}

func (x *UserInfoUpdateReq) Reset() {
	*x = UserInfoUpdateReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfoUpdateReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoUpdateReq) ProtoMessage() {}

func (x *UserInfoUpdateReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoUpdateReq.ProtoReflect.Descriptor instead.
func (*UserInfoUpdateReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{17}
}

func (x *UserInfoUpdateReq) GetInfo() *UserInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *UserInfoUpdateReq) GetWithRoot() bool {
	if x != nil {
		return x.WithRoot
	}
	return false
}

type UserInfoGetOneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID  int64  `protobuf:"varint,1,opt,name=userID,proto3" json:"userID,omitempty"`
	Account string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"` //完全匹配
}

func (x *UserInfoGetOneReq) Reset() {
	*x = UserInfoGetOneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfoGetOneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoGetOneReq) ProtoMessage() {}

func (x *UserInfoGetOneReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoGetOneReq.ProtoReflect.Descriptor instead.
func (*UserInfoGetOneReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{18}
}

func (x *UserInfoGetOneReq) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *UserInfoGetOneReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

type UserInfoDeleteReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID int64 `protobuf:"varint,1,opt,name=userID,proto3" json:"userID,omitempty"`
}

func (x *UserInfoDeleteReq) Reset() {
	*x = UserInfoDeleteReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfoDeleteReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoDeleteReq) ProtoMessage() {}

func (x *UserInfoDeleteReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoDeleteReq.ProtoReflect.Descriptor instead.
func (*UserInfoDeleteReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{19}
}

func (x *UserInfoDeleteReq) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

type JwtToken struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	AccessToken  string `protobuf:"bytes,1,opt,name=accessToken,proto3" json:"accessToken,omitempty"`
	AccessExpire int64  `protobuf:"varint,2,opt,name=accessExpire,proto3" json:"accessExpire,omitempty"`
	RefreshAfter int64  `protobuf:"varint,3,opt,name=refreshAfter,proto3" json:"refreshAfter,omitempty"`
}

func (x *JwtToken) Reset() {
	*x = JwtToken{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *JwtToken) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*JwtToken) ProtoMessage() {}

func (x *JwtToken) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use JwtToken.ProtoReflect.Descriptor instead.
func (*JwtToken) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{20}
}

func (x *JwtToken) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *JwtToken) GetAccessExpire() int64 {
	if x != nil {
		return x.AccessExpire
	}
	return 0
}

func (x *JwtToken) GetRefreshAfter() int64 {
	if x != nil {
		return x.RefreshAfter
	}
	return 0
}

type UserCaptchaReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"` //短信验证时填写手机号,邮箱验证时填写邮箱
	Type    string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`       //验证方式:短信验证,图片验证码,邮箱验证(email)
	Use     string `protobuf:"bytes,3,opt,name=use,proto3" json:"use,omitempty"`         //用途 login 登录,register 注册,changePwd 修改密码,bindAccount 绑定账号,forgetPwd 忘记密码
	CodeID  string `protobuf:"bytes,4,opt,name=codeID,proto3" json:"codeID,omitempty"`   //验证码编码
	Code    string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`       //验证码
	Ip      string `protobuf:"bytes,6,opt,name=ip,proto3" json:"ip,omitempty"`           //透传请求的 ip
}

func (x *UserCaptchaReq) Reset() {
	*x = UserCaptchaReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCaptchaReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCaptchaReq) ProtoMessage() {}

func (x *UserCaptchaReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCaptchaReq.ProtoReflect.Descriptor instead.
func (*UserCaptchaReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{21}
}

func (x *UserCaptchaReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UserCaptchaReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UserCaptchaReq) GetUse() string {
	if x != nil {
		return x.Use
	}
	return ""
}

func (x *UserCaptchaReq) GetCodeID() string {
	if x != nil {
		return x.CodeID
	}
	return ""
}

func (x *UserCaptchaReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserCaptchaReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type UserCaptchaResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	CodeID string `protobuf:"bytes,1,opt,name=codeID,proto3" json:"codeID,omitempty"`  //验证码编码
	Code   string `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`      //验证码--只有图片验证码由apisvr生成,其他方式返回空
	Expire int64  `protobuf:"varint,3,opt,name=expire,proto3" json:"expire,omitempty"` //过期时间
}

func (x *UserCaptchaResp) Reset() {
	*x = UserCaptchaResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCaptchaResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCaptchaResp) ProtoMessage() {}

func (x *UserCaptchaResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCaptchaResp.ProtoReflect.Descriptor instead.
func (*UserCaptchaResp) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{22}
}

func (x *UserCaptchaResp) GetCodeID() string {
	if x != nil {
		return x.CodeID
	}
	return ""
}

func (x *UserCaptchaResp) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserCaptchaResp) GetExpire() int64 {
	if x != nil {
		return x.Expire
	}
	return 0
}

type UserForgetPwdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account  string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`   //登录账号(支持用户名,邮箱,手机号登录) 账号密码登录时需要填写
	Type     string `protobuf:"bytes,2,opt,name=type,proto3" json:"type,omitempty"`         //账号类型: email:邮箱 phone:手机号
	Password string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"` //密码: 原始明文
	Code     string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`         //验证码    微信登录填code
	CodeID   string `protobuf:"bytes,6,opt,name=codeID,proto3" json:"codeID,omitempty"`     //验证码编号 微信登录填state
}

func (x *UserForgetPwdReq) Reset() {
	*x = UserForgetPwdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserForgetPwdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserForgetPwdReq) ProtoMessage() {}

func (x *UserForgetPwdReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserForgetPwdReq.ProtoReflect.Descriptor instead.
func (*UserForgetPwdReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{23}
}

func (x *UserForgetPwdReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UserForgetPwdReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UserForgetPwdReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *UserForgetPwdReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserForgetPwdReq) GetCodeID() string {
	if x != nil {
		return x.CodeID
	}
	return ""
}

type UserCodeToUserIDReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	LoginType string `protobuf:"bytes,4,opt,name=loginType,proto3" json:"loginType,omitempty"` //验证类型 phone 手机号 wxOpen 微信开放平台 wxIn 微信内 wxMiniP 微信小程序 pwd 账号密码
	Code      string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`           //验证码    微信登录填code
}

func (x *UserCodeToUserIDReq) Reset() {
	*x = UserCodeToUserIDReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCodeToUserIDReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCodeToUserIDReq) ProtoMessage() {}

func (x *UserCodeToUserIDReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCodeToUserIDReq.ProtoReflect.Descriptor instead.
func (*UserCodeToUserIDReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{24}
}

func (x *UserCodeToUserIDReq) GetLoginType() string {
	if x != nil {
		return x.LoginType
	}
	return ""
}

func (x *UserCodeToUserIDReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type UserCodeToUserIDResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OpenID  string `protobuf:"bytes,1,opt,name=openID,proto3" json:"openID,omitempty"`
	UnionID string `protobuf:"bytes,2,opt,name=unionID,proto3" json:"unionID,omitempty"`
}

func (x *UserCodeToUserIDResp) Reset() {
	*x = UserCodeToUserIDResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCodeToUserIDResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCodeToUserIDResp) ProtoMessage() {}

func (x *UserCodeToUserIDResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCodeToUserIDResp.ProtoReflect.Descriptor instead.
func (*UserCodeToUserIDResp) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{25}
}

func (x *UserCodeToUserIDResp) GetOpenID() string {
	if x != nil {
		return x.OpenID
	}
	return ""
}

func (x *UserCodeToUserIDResp) GetUnionID() string {
	if x != nil {
		return x.UnionID
	}
	return ""
}

type UserChangePwdReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type        string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`               //验证类型: email:邮箱 phone:手机号 pwd:原密码
	OldPassword string `protobuf:"bytes,2,opt,name=oldPassword,proto3" json:"oldPassword,omitempty"` //输入原密码修改密码
	Password    string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`       //新密码
	Code        string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`               //验证码    微信登录填code
	CodeID      string `protobuf:"bytes,6,opt,name=codeID,proto3" json:"codeID,omitempty"`           //验证码编号 微信登录填state
}

func (x *UserChangePwdReq) Reset() {
	*x = UserChangePwdReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserChangePwdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserChangePwdReq) ProtoMessage() {}

func (x *UserChangePwdReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserChangePwdReq.ProtoReflect.Descriptor instead.
func (*UserChangePwdReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{26}
}

func (x *UserChangePwdReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UserChangePwdReq) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *UserChangePwdReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *UserChangePwdReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserChangePwdReq) GetCodeID() string {
	if x != nil {
		return x.CodeID
	}
	return ""
}

type UserLoginReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account   string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`     //登录账号(支持用户名,邮箱登录)  oauth2(google, github) 登录填写 邮箱
	Password  string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"`   //密码，sha256 密码登录时需要填写
	LoginType string `protobuf:"bytes,4,opt,name=loginType,proto3" json:"loginType,omitempty"` //验证类型 email 邮箱验证码 google Google账号 github  pwd 账号密码
	Code      string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`           //验证码    微信登录填code
	CodeID    string `protobuf:"bytes,6,opt,name=codeID,proto3" json:"codeID,omitempty"`       //验证码编号 微信登录填state
	Ip        string `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`               //透传请求的 ip
}

func (x *UserLoginReq) Reset() {
	*x = UserLoginReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserLoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserLoginReq) ProtoMessage() {}

func (x *UserLoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserLoginReq.ProtoReflect.Descriptor instead.
func (*UserLoginReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{27}
}

func (x *UserLoginReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UserLoginReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *UserLoginReq) GetLoginType() string {
	if x != nil {
		return x.LoginType
	}
	return ""
}

func (x *UserLoginReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserLoginReq) GetCodeID() string {
	if x != nil {
		return x.CodeID
	}
	return ""
}

func (x *UserLoginReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type UserLoginResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Info       *UserInfo        `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty"`
	Token      string           `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	Workspaces []*WorkspaceInfo `protobuf:"bytes,3,rep,name=workspaces,proto3" json:"workspaces,omitempty"` //拥有的工作空间列表
}

func (x *UserLoginResp) Reset() {
	*x = UserLoginResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserLoginResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserLoginResp) ProtoMessage() {}

func (x *UserLoginResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserLoginResp.ProtoReflect.Descriptor instead.
func (*UserLoginResp) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{28}
}

func (x *UserLoginResp) GetInfo() *UserInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

func (x *UserLoginResp) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *UserLoginResp) GetWorkspaces() []*WorkspaceInfo {
	if x != nil {
		return x.Workspaces
	}
	return nil
}

// 登录信息注册,注册第一步(注册核心登录信息) 返回一个jwt用来第二步注册
type UserRegisterReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	RegType string       `protobuf:"bytes,1,opt,name=regType,proto3" json:"regType,omitempty"` //注册方式:	 email 邮箱验证码 google Google账号 github
	Code    string       `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`       //验证码 邮箱的方式需要填写
	CodeID  string       `protobuf:"bytes,3,opt,name=codeID,proto3" json:"codeID,omitempty"`   //验证码编号 邮箱的方式需要填写
	Info    *RegUserInfo `protobuf:"bytes,5,opt,name=info,proto3" json:"info,omitempty"`       //填写用户信息
}

func (x *UserRegisterReq) Reset() {
	*x = UserRegisterReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserRegisterReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRegisterReq) ProtoMessage() {}

func (x *UserRegisterReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRegisterReq.ProtoReflect.Descriptor instead.
func (*UserRegisterReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{29}
}

func (x *UserRegisterReq) GetRegType() string {
	if x != nil {
		return x.RegType
	}
	return ""
}

func (x *UserRegisterReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserRegisterReq) GetCodeID() string {
	if x != nil {
		return x.CodeID
	}
	return ""
}

func (x *UserRegisterReq) GetInfo() *RegUserInfo {
	if x != nil {
		return x.Info
	}
	return nil
}

type UserRegisterResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID int64 `protobuf:"varint,1,opt,name=userID,proto3" json:"userID,omitempty"`
}

func (x *UserRegisterResp) Reset() {
	*x = UserRegisterResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserRegisterResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserRegisterResp) ProtoMessage() {}

func (x *UserRegisterResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserRegisterResp.ProtoReflect.Descriptor instead.
func (*UserRegisterResp) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{30}
}

func (x *UserRegisterResp) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

type UserBindAccountReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Type    string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`       //验证方式:phone手机号注册 wxOpen 微信开放平台登录 wxIn 微信内 wxMiniP 微信小程序
	Account string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty"` //手机号,邮箱需要填写
	Code    string `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`       //验证码    微信登录填code
	CodeID  string `protobuf:"bytes,4,opt,name=codeID,proto3" json:"codeID,omitempty"`   //验证码编号 微信登录填state
}

func (x *UserBindAccountReq) Reset() {
	*x = UserBindAccountReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserBindAccountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBindAccountReq) ProtoMessage() {}

func (x *UserBindAccountReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBindAccountReq.ProtoReflect.Descriptor instead.
func (*UserBindAccountReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{31}
}

func (x *UserBindAccountReq) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *UserBindAccountReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UserBindAccountReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UserBindAccountReq) GetCodeID() string {
	if x != nil {
		return x.CodeID
	}
	return ""
}

// 用户属性信息
type RegUserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserName  string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName,omitempty"`
	Password  string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"` //密码原文
	Email     string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	GithubPid string `protobuf:"bytes,5,opt,name=githubPid,proto3" json:"githubPid,omitempty"` //如果是github自动注册的需要填写,填写过之后不可修改
	GooglePid string `protobuf:"bytes,6,opt,name=googlePid,proto3" json:"googlePid,omitempty"` //如果是google自动注册的需要填写,填写过之后不可修改
	RegIP     string `protobuf:"bytes,8,opt,name=regIP,proto3" json:"regIP,omitempty"`         //只读
}

func (x *RegUserInfo) Reset() {
	*x = RegUserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RegUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegUserInfo) ProtoMessage() {}

func (x *RegUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegUserInfo.ProtoReflect.Descriptor instead.
func (*RegUserInfo) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{32}
}

func (x *RegUserInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *RegUserInfo) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *RegUserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *RegUserInfo) GetGithubPid() string {
	if x != nil {
		return x.GithubPid
	}
	return ""
}

func (x *RegUserInfo) GetGooglePid() string {
	if x != nil {
		return x.GooglePid
	}
	return ""
}

func (x *RegUserInfo) GetRegIP() string {
	if x != nil {
		return x.RegIP
	}
	return ""
}

// 用户属性信息
type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID      int64  `protobuf:"varint,1,opt,name=userID,proto3" json:"userID,omitempty"`
	UserName    string `protobuf:"bytes,2,opt,name=userName,proto3" json:"userName,omitempty"`
	NickName    string `protobuf:"bytes,9,opt,name=nickName,proto3" json:"nickName,omitempty"` //昵称
	Password    string `protobuf:"bytes,3,opt,name=password,proto3" json:"password,omitempty"` //只读
	Email       string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty"`
	GithubPid   string `protobuf:"bytes,5,opt,name=githubPid,proto3" json:"githubPid,omitempty"`       //如果是github自动注册的需要填写,填写过之后不可修改
	GooglePid   string `protobuf:"bytes,6,opt,name=googlePid,proto3" json:"googlePid,omitempty"`       //如果是google自动注册的需要填写,填写过之后不可修改
	LastIP      string `protobuf:"bytes,7,opt,name=lastIP,proto3" json:"lastIP,omitempty"`             //只读
	RegIP       string `protobuf:"bytes,8,opt,name=regIP,proto3" json:"regIP,omitempty"`               //只读
	CreatedTime int64  `protobuf:"varint,15,opt,name=createdTime,proto3" json:"createdTime,omitempty"` //只读
	HeadImg     string `protobuf:"bytes,19,opt,name=headImg,proto3" json:"headImg,omitempty"`
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{33}
}

func (x *UserInfo) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *UserInfo) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *UserInfo) GetNickName() string {
	if x != nil {
		return x.NickName
	}
	return ""
}

func (x *UserInfo) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *UserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInfo) GetGithubPid() string {
	if x != nil {
		return x.GithubPid
	}
	return ""
}

func (x *UserInfo) GetGooglePid() string {
	if x != nil {
		return x.GooglePid
	}
	return ""
}

func (x *UserInfo) GetLastIP() string {
	if x != nil {
		return x.LastIP
	}
	return ""
}

func (x *UserInfo) GetRegIP() string {
	if x != nil {
		return x.RegIP
	}
	return ""
}

func (x *UserInfo) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *UserInfo) GetHeadImg() string {
	if x != nil {
		return x.HeadImg
	}
	return ""
}

type UserCreateResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserID int64 `protobuf:"varint,1,opt,name=userID,proto3" json:"userID,omitempty"` //用户id
}

func (x *UserCreateResp) Reset() {
	*x = UserCreateResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCreateResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCreateResp) ProtoMessage() {}

func (x *UserCreateResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCreateResp.ProtoReflect.Descriptor instead.
func (*UserCreateResp) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{34}
}

func (x *UserCreateResp) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

type UserCheckTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token         string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	WorkspaceCode string `protobuf:"bytes,2,opt,name=workspaceCode,proto3" json:"workspaceCode,omitempty"`
	Ip            string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *UserCheckTokenReq) Reset() {
	*x = UserCheckTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCheckTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCheckTokenReq) ProtoMessage() {}

func (x *UserCheckTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCheckTokenReq.ProtoReflect.Descriptor instead.
func (*UserCheckTokenReq) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{35}
}

func (x *UserCheckTokenReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *UserCheckTokenReq) GetWorkspaceCode() string {
	if x != nil {
		return x.WorkspaceCode
	}
	return ""
}

func (x *UserCheckTokenReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type UserCheckTokenResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	WorkspaceCode         string `protobuf:"bytes,1,opt,name=workspaceCode,proto3" json:"workspaceCode,omitempty"`
	IsSuperAdmin          bool   `protobuf:"varint,2,opt,name=isSuperAdmin,proto3" json:"isSuperAdmin,omitempty"`                   //全局超管
	IsAdmin               bool   `protobuf:"varint,3,opt,name=isAdmin,proto3" json:"isAdmin,omitempty"`                             //全局管理员
	IsWorkspaceSuperAdmin bool   `protobuf:"varint,4,opt,name=isWorkspaceSuperAdmin,proto3" json:"isWorkspaceSuperAdmin,omitempty"` //工作空间超管,拥有者才能是超管
	IsWorkspaceAdmin      bool   `protobuf:"varint,5,opt,name=isWorkspaceAdmin,proto3" json:"isWorkspaceAdmin,omitempty"`           //工作空间管理员
	UserID                int64  `protobuf:"varint,6,opt,name=userID,proto3" json:"userID,omitempty"`                               //用户id
	UserName              string `protobuf:"bytes,7,opt,name=userName,proto3" json:"userName,omitempty"`
	Email                 string `protobuf:"bytes,8,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *UserCheckTokenResp) Reset() {
	*x = UserCheckTokenResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserCheckTokenResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserCheckTokenResp) ProtoMessage() {}

func (x *UserCheckTokenResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserCheckTokenResp.ProtoReflect.Descriptor instead.
func (*UserCheckTokenResp) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{36}
}

func (x *UserCheckTokenResp) GetWorkspaceCode() string {
	if x != nil {
		return x.WorkspaceCode
	}
	return ""
}

func (x *UserCheckTokenResp) GetIsSuperAdmin() bool {
	if x != nil {
		return x.IsSuperAdmin
	}
	return false
}

func (x *UserCheckTokenResp) GetIsAdmin() bool {
	if x != nil {
		return x.IsAdmin
	}
	return false
}

func (x *UserCheckTokenResp) GetIsWorkspaceSuperAdmin() bool {
	if x != nil {
		return x.IsWorkspaceSuperAdmin
	}
	return false
}

func (x *UserCheckTokenResp) GetIsWorkspaceAdmin() bool {
	if x != nil {
		return x.IsWorkspaceAdmin
	}
	return false
}

func (x *UserCheckTokenResp) GetUserID() int64 {
	if x != nil {
		return x.UserID
	}
	return 0
}

func (x *UserCheckTokenResp) GetUserName() string {
	if x != nil {
		return x.UserName
	}
	return ""
}

func (x *UserCheckTokenResp) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type PageInfo_OrderBy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 排序的字段名
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	// 排序方式：0 aes, 1 desc
	Sort int64 `protobuf:"varint,2,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *PageInfo_OrderBy) Reset() {
	*x = PageInfo_OrderBy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_sys_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageInfo_OrderBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageInfo_OrderBy) ProtoMessage() {}

func (x *PageInfo_OrderBy) ProtoReflect() protoreflect.Message {
	mi := &file_proto_sys_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageInfo_OrderBy.ProtoReflect.Descriptor instead.
func (*PageInfo_OrderBy) Descriptor() ([]byte, []int) {
	return file_proto_sys_proto_rawDescGZIP(), []int{3, 0}
}

func (x *PageInfo_OrderBy) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *PageInfo_OrderBy) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

var File_proto_sys_proto protoreflect.FileDescriptor

var file_proto_sys_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x73, 0x79, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x03, 0x73, 0x79, 0x73, 0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x77, 0x72, 0x61, 0x70, 0x70, 0x65, 0x72, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x33, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x65, 0x6e, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x65, 0x6e, 0x64, 0x22, 0x33, 0x0a, 0x09, 0x54,
	0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x72, 0x74, 0x12, 0x10,
	0x0a, 0x03, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x03, 0x65, 0x6e, 0x64,
	0x22, 0x07, 0x0a, 0x05, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x96, 0x01, 0x0a, 0x08, 0x50, 0x61,
	0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x2d,
	0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x73, 0x79, 0x73, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x42, 0x79, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x1a, 0x33, 0x0a,
	0x07, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x6f,
	0x72, 0x74, 0x22, 0x3f, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x3e, 0x0a, 0x0c, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x72, 0x65, 0x49, 0x6e,
	0x74, 0x36, 0x34, 0x12, 0x18, 0x0a, 0x07, 0x43, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x43, 0x6d, 0x70, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x18, 0x0a, 0x06, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x30, 0x0a,
	0x0a, 0x57, 0x69, 0x74, 0x68, 0x49, 0x44, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22,
	0x1e, 0x0a, 0x08, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22,
	0x4d, 0x0a, 0x0d, 0x57, 0x69, 0x74, 0x68, 0x41, 0x70, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x49, 0x44,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x70, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0xa0,
	0x01, 0x0a, 0x18, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55,
	0x72, 0x6c, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x12, 0x47, 0x0a, 0x08, 0x66,
	0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e,
	0x73, 0x79, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64,
	0x55, 0x72, 0x6c, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x2e, 0x46, 0x69, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x3b, 0x0a, 0x0d, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0xc2, 0x01, 0x0a, 0x19, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f,
	0x61, 0x64, 0x55, 0x72, 0x6c, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12,
	0x48, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x70,
	0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x2e, 0x46, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x08, 0x66, 0x69, 0x6c, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x5b, 0x0a, 0x0d, 0x46, 0x69, 0x6c,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x34, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x79,
	0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72,
	0x6c, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x36, 0x0a, 0x18, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x55,
	0x0a, 0x19, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72,
	0x6c, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x1c, 0x0a, 0x09, 0x75,
	0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x75, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x66, 0x69, 0x6c,
	0x65, 0x50, 0x61, 0x74, 0x68, 0x22, 0xaf, 0x01, 0x0a, 0x0d, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x44, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x65, 0x73, 0x63, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x56, 0x61, 0x6c, 0x75,
	0x65, 0x52, 0x04, 0x64, 0x65, 0x73, 0x63, 0x22, 0x4b, 0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x57,
	0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x50, 0x0a, 0x11, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x04, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07,
	0x72, 0x6f, 0x6c, 0x65, 0x49, 0x44, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x03, 0x52, 0x07, 0x72,
	0x6f, 0x6c, 0x65, 0x49, 0x44, 0x73, 0x22, 0x52, 0x0a, 0x11, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x04, 0x69,
	0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x73, 0x79, 0x73, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x12, 0x1a,
	0x0a, 0x08, 0x57, 0x69, 0x74, 0x68, 0x52, 0x6f, 0x6f, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x08, 0x57, 0x69, 0x74, 0x68, 0x52, 0x6f, 0x6f, 0x74, 0x22, 0x45, 0x0a, 0x11, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0x2b, 0x0a, 0x11, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x65, 0x71, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x22, 0x74,
	0x0a, 0x08, 0x4a, 0x77, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x20, 0x0a, 0x0b, 0x61, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x22, 0x0a, 0x0c,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65,
	0x12, 0x22, 0x0a, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41, 0x66, 0x74, 0x65, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c, 0x72, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x41,
	0x66, 0x74, 0x65, 0x72, 0x22, 0x8c, 0x01, 0x0a, 0x0e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x61, 0x70,
	0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x64, 0x65, 0x49,
	0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x02, 0x69, 0x70, 0x22, 0x55, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72, 0x43, 0x61, 0x70, 0x74, 0x63,
	0x68, 0x61, 0x52, 0x65, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x64, 0x65, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x12, 0x12,
	0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x10, 0x55,
	0x73, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x67, 0x65, 0x74, 0x50, 0x77, 0x64, 0x52, 0x65, 0x71, 0x12,
	0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x6f, 0x64, 0x65, 0x49, 0x44, 0x22, 0x47, 0x0a, 0x13, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09,
	0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f,
	0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x48,
	0x0a, 0x14, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6f, 0x70, 0x65, 0x6e, 0x49, 0x44, 0x12, 0x18,
	0x0a, 0x07, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x75, 0x6e, 0x69, 0x6f, 0x6e, 0x49, 0x44, 0x22, 0x90, 0x01, 0x0a, 0x10, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x77, 0x64, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x20, 0x0a, 0x0b, 0x6f, 0x6c, 0x64, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x6c, 0x64, 0x50, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x22, 0x9e, 0x01, 0x0a, 0x0c,
	0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x67, 0x69, 0x6e, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0x7c, 0x0a, 0x0d,
	0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x21, 0x0a,
	0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x73, 0x79,
	0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x32, 0x0a, 0x0a, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x73, 0x79, 0x73,
	0x2e, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0a,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x22, 0x7d, 0x0a, 0x0f, 0x55, 0x73,
	0x65, 0x72, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x12, 0x18, 0x0a,
	0x07, 0x72, 0x65, 0x67, 0x54, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x72, 0x65, 0x67, 0x54, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63,
	0x6f, 0x64, 0x65, 0x49, 0x44, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x64,
	0x65, 0x49, 0x44, 0x12, 0x24, 0x0a, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x10, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x52, 0x65, 0x67, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x04, 0x69, 0x6e, 0x66, 0x6f, 0x22, 0x2a, 0x0a, 0x10, 0x55, 0x73, 0x65,
	0x72, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x12, 0x16, 0x0a,
	0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75,
	0x73, 0x65, 0x72, 0x49, 0x44, 0x22, 0x6e, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e,
	0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12,
	0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x6f, 0x64, 0x65, 0x49, 0x44, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x6f, 0x64, 0x65, 0x49, 0x44, 0x22, 0xad, 0x01, 0x0a, 0x0b, 0x52, 0x65, 0x67, 0x55, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x14, 0x0a,
	0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x50, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x50, 0x69,
	0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x69, 0x64, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x69, 0x64, 0x12,
	0x14, 0x0a, 0x05, 0x72, 0x65, 0x67, 0x49, 0x50, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x72, 0x65, 0x67, 0x49, 0x50, 0x22, 0xb2, 0x02, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x50, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x50,
	0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x50, 0x69, 0x64,
	0x12, 0x16, 0x0a, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x50, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6c, 0x61, 0x73, 0x74, 0x49, 0x50, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x67, 0x49,
	0x50, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x67, 0x49, 0x50, 0x12, 0x20,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x68, 0x65, 0x61, 0x64, 0x49, 0x6d, 0x67, 0x18, 0x13, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x68, 0x65, 0x61, 0x64, 0x49, 0x6d, 0x67, 0x22, 0x28, 0x0a, 0x0e, 0x55, 0x73,
	0x65, 0x72, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x73, 0x70, 0x12, 0x16, 0x0a, 0x06,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x44, 0x22, 0x5f, 0x0a, 0x11, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x24, 0x0a, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0xa4, 0x02, 0x0a, 0x12, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x12, 0x24, 0x0a, 0x0d,
	0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x22, 0x0a, 0x0c, 0x69, 0x73, 0x53, 0x75, 0x70, 0x65, 0x72, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x53, 0x75, 0x70, 0x65,
	0x72, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69,
	0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x41, 0x64, 0x6d, 0x69, 0x6e,
	0x12, 0x34, 0x0a, 0x15, 0x69, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53,
	0x75, 0x70, 0x65, 0x72, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x15, 0x69, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x75, 0x70, 0x65,
	0x72, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x12, 0x2a, 0x0a, 0x10, 0x69, 0x73, 0x57, 0x6f, 0x72, 0x6b,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x10, 0x69, 0x73, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65, 0x41, 0x64, 0x6d,
	0x69, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x32, 0xac, 0x01, 0x0a,
	0x06, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x12, 0x50, 0x0a, 0x0f, 0x75, 0x70, 0x6c, 0x6f, 0x61,
	0x64, 0x55, 0x72, 0x6c, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x70, 0x12, 0x1d, 0x2e, 0x73, 0x79, 0x73,
	0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x73, 0x79, 0x73, 0x2e,
	0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x47,
	0x65, 0x74, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x12, 0x50, 0x0a, 0x0f, 0x75, 0x70, 0x6c,
	0x6f, 0x61, 0x64, 0x55, 0x72, 0x6c, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x12, 0x1d, 0x2e, 0x73,
	0x79, 0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55,
	0x72, 0x6c, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1e, 0x2e, 0x73, 0x79,
	0x73, 0x2e, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x55, 0x70, 0x6c, 0x6f, 0x61, 0x64, 0x55, 0x72,
	0x6c, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x70, 0x32, 0x88, 0x05, 0x0a, 0x0a,
	0x55, 0x73, 0x65, 0x72, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x12, 0x34, 0x0a, 0x0e, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x16, 0x2e, 0x73,
	0x79, 0x73, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x1a, 0x0a, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x12, 0x37, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x47, 0x65, 0x74, 0x4f,
	0x6e, 0x65, 0x12, 0x16, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x0d, 0x2e, 0x73, 0x79, 0x73,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x34, 0x0a, 0x0e, 0x75, 0x73, 0x65,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x16, 0x2e, 0x73, 0x79,
	0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x65, 0x71, 0x1a, 0x0a, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12,
	0x32, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x11, 0x2e, 0x73,
	0x79, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52, 0x65, 0x71, 0x1a,
	0x12, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x12, 0x32, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x46, 0x6f, 0x72, 0x67, 0x65,
	0x74, 0x50, 0x77, 0x64, 0x12, 0x15, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x46,
	0x6f, 0x72, 0x67, 0x65, 0x74, 0x50, 0x77, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x0a, 0x2e, 0x73, 0x79,
	0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x38, 0x0a, 0x0b, 0x75, 0x73, 0x65, 0x72, 0x43,
	0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x12, 0x13, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x71, 0x1a, 0x14, 0x2e, 0x73, 0x79,
	0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x61, 0x70, 0x74, 0x63, 0x68, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x41, 0x0a, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x16, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x73, 0x79,
	0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0c, 0x75, 0x73, 0x65, 0x72, 0x52, 0x65, 0x67, 0x69,
	0x73, 0x74, 0x65, 0x72, 0x12, 0x14, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x52,
	0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x71, 0x1a, 0x15, 0x2e, 0x73, 0x79, 0x73,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x65, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x12, 0x32, 0x0a, 0x0d, 0x75, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50,
	0x77, 0x64, 0x12, 0x15, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x50, 0x77, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x0a, 0x2e, 0x73, 0x79, 0x73, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x12, 0x47, 0x0a, 0x10, 0x75, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x64,
	0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x12, 0x18, 0x2e, 0x73, 0x79, 0x73, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x43, 0x6f, 0x64, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44,
	0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x43, 0x6f,
	0x64, 0x65, 0x54, 0x6f, 0x55, 0x73, 0x65, 0x72, 0x49, 0x44, 0x52, 0x65, 0x73, 0x70, 0x12, 0x36,
	0x0a, 0x0f, 0x75, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x17, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x42, 0x69, 0x6e, 0x64,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x0a, 0x2e, 0x73, 0x79, 0x73,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x32, 0x36, 0x0a, 0x09, 0x57, 0x6f, 0x72, 0x6b, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x29, 0x0a, 0x0f, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x0a, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x1a, 0x0a, 0x2e, 0x73, 0x79, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x08,
	0x5a, 0x06, 0x70, 0x62, 0x2f, 0x73, 0x79, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_sys_proto_rawDescOnce sync.Once
	file_proto_sys_proto_rawDescData = file_proto_sys_proto_rawDesc
)

func file_proto_sys_proto_rawDescGZIP() []byte {
	file_proto_sys_proto_rawDescOnce.Do(func() {
		file_proto_sys_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_sys_proto_rawDescData)
	})
	return file_proto_sys_proto_rawDescData
}

var file_proto_sys_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_proto_sys_proto_goTypes = []interface{}{
	(*DateRange)(nil),                 // 0: sys.DateRange
	(*TimeRange)(nil),                 // 1: sys.TimeRange
	(*Empty)(nil),                     // 2: sys.Empty
	(*PageInfo)(nil),                  // 3: sys.PageInfo
	(*CompareString)(nil),             // 4: sys.CompareString
	(*CompareInt64)(nil),              // 5: sys.CompareInt64
	(*WithID)(nil),                    // 6: sys.WithID
	(*WithIDCode)(nil),                // 7: sys.WithIDCode
	(*WithCode)(nil),                  // 8: sys.WithCode
	(*WithAppCodeID)(nil),             // 9: sys.WithAppCodeID
	(*CommonUploadUrlGetMapReq)(nil),  // 10: sys.CommonUploadUrlGetMapReq
	(*CommonUploadUrlGetMapResp)(nil), // 11: sys.CommonUploadUrlGetMapResp
	(*CommonUploadUrlGetOneReq)(nil),  // 12: sys.CommonUploadUrlGetOneReq
	(*CommonUploadUrlGetOneResp)(nil), // 13: sys.CommonUploadUrlGetOneResp
	(*WorkspaceInfo)(nil),             // 14: sys.WorkspaceInfo
	(*UserWorkspace)(nil),             // 15: sys.UserWorkspace
	(*UserInfoCreateReq)(nil),         // 16: sys.UserInfoCreateReq
	(*UserInfoUpdateReq)(nil),         // 17: sys.userInfoUpdateReq
	(*UserInfoGetOneReq)(nil),         // 18: sys.UserInfoGetOneReq
	(*UserInfoDeleteReq)(nil),         // 19: sys.UserInfoDeleteReq
	(*JwtToken)(nil),                  // 20: sys.JwtToken
	(*UserCaptchaReq)(nil),            // 21: sys.UserCaptchaReq
	(*UserCaptchaResp)(nil),           // 22: sys.UserCaptchaResp
	(*UserForgetPwdReq)(nil),          // 23: sys.UserForgetPwdReq
	(*UserCodeToUserIDReq)(nil),       // 24: sys.UserCodeToUserIDReq
	(*UserCodeToUserIDResp)(nil),      // 25: sys.UserCodeToUserIDResp
	(*UserChangePwdReq)(nil),          // 26: sys.UserChangePwdReq
	(*UserLoginReq)(nil),              // 27: sys.UserLoginReq
	(*UserLoginResp)(nil),             // 28: sys.UserLoginResp
	(*UserRegisterReq)(nil),           // 29: sys.UserRegisterReq
	(*UserRegisterResp)(nil),          // 30: sys.UserRegisterResp
	(*UserBindAccountReq)(nil),        // 31: sys.UserBindAccountReq
	(*RegUserInfo)(nil),               // 32: sys.RegUserInfo
	(*UserInfo)(nil),                  // 33: sys.UserInfo
	(*UserCreateResp)(nil),            // 34: sys.UserCreateResp
	(*UserCheckTokenReq)(nil),         // 35: sys.UserCheckTokenReq
	(*UserCheckTokenResp)(nil),        // 36: sys.UserCheckTokenResp
	(*PageInfo_OrderBy)(nil),          // 37: sys.PageInfo.OrderBy
	nil,                               // 38: sys.CommonUploadUrlGetMapReq.FileInfoEntry
	nil,                               // 39: sys.CommonUploadUrlGetMapResp.FileInfoEntry
	(*wrapperspb.StringValue)(nil),    // 40: google.protobuf.StringValue
}
var file_proto_sys_proto_depIdxs = []int32{
	37, // 0: sys.PageInfo.orders:type_name -> sys.PageInfo.OrderBy
	38, // 1: sys.CommonUploadUrlGetMapReq.fileInfo:type_name -> sys.CommonUploadUrlGetMapReq.FileInfoEntry
	39, // 2: sys.CommonUploadUrlGetMapResp.fileInfo:type_name -> sys.CommonUploadUrlGetMapResp.FileInfoEntry
	40, // 3: sys.WorkspaceInfo.desc:type_name -> google.protobuf.StringValue
	33, // 4: sys.UserInfoCreateReq.info:type_name -> sys.UserInfo
	33, // 5: sys.userInfoUpdateReq.info:type_name -> sys.UserInfo
	33, // 6: sys.UserLoginResp.info:type_name -> sys.UserInfo
	14, // 7: sys.UserLoginResp.workspaces:type_name -> sys.WorkspaceInfo
	32, // 8: sys.UserRegisterReq.info:type_name -> sys.RegUserInfo
	13, // 9: sys.CommonUploadUrlGetMapResp.FileInfoEntry.value:type_name -> sys.CommonUploadUrlGetOneResp
	10, // 10: sys.Common.uploadUrlGetMap:input_type -> sys.CommonUploadUrlGetMapReq
	12, // 11: sys.Common.uploadUrlGetOne:input_type -> sys.CommonUploadUrlGetOneReq
	17, // 12: sys.UserManage.userInfoUpdate:input_type -> sys.userInfoUpdateReq
	18, // 13: sys.UserManage.userInfoGetOne:input_type -> sys.UserInfoGetOneReq
	19, // 14: sys.UserManage.userInfoDelete:input_type -> sys.UserInfoDeleteReq
	27, // 15: sys.UserManage.userLogin:input_type -> sys.UserLoginReq
	23, // 16: sys.UserManage.userForgetPwd:input_type -> sys.UserForgetPwdReq
	21, // 17: sys.UserManage.userCaptcha:input_type -> sys.UserCaptchaReq
	35, // 18: sys.UserManage.userCheckToken:input_type -> sys.UserCheckTokenReq
	29, // 19: sys.UserManage.userRegister:input_type -> sys.UserRegisterReq
	26, // 20: sys.UserManage.userChangePwd:input_type -> sys.UserChangePwdReq
	24, // 21: sys.UserManage.userCodeToUserID:input_type -> sys.UserCodeToUserIDReq
	31, // 22: sys.UserManage.userBindAccount:input_type -> sys.UserBindAccountReq
	2,  // 23: sys.Workspace.workspaceUpdate:input_type -> sys.Empty
	11, // 24: sys.Common.uploadUrlGetMap:output_type -> sys.CommonUploadUrlGetMapResp
	13, // 25: sys.Common.uploadUrlGetOne:output_type -> sys.CommonUploadUrlGetOneResp
	2,  // 26: sys.UserManage.userInfoUpdate:output_type -> sys.Empty
	33, // 27: sys.UserManage.userInfoGetOne:output_type -> sys.UserInfo
	2,  // 28: sys.UserManage.userInfoDelete:output_type -> sys.Empty
	28, // 29: sys.UserManage.userLogin:output_type -> sys.UserLoginResp
	2,  // 30: sys.UserManage.userForgetPwd:output_type -> sys.Empty
	22, // 31: sys.UserManage.userCaptcha:output_type -> sys.UserCaptchaResp
	36, // 32: sys.UserManage.userCheckToken:output_type -> sys.UserCheckTokenResp
	30, // 33: sys.UserManage.userRegister:output_type -> sys.UserRegisterResp
	2,  // 34: sys.UserManage.userChangePwd:output_type -> sys.Empty
	25, // 35: sys.UserManage.userCodeToUserID:output_type -> sys.UserCodeToUserIDResp
	2,  // 36: sys.UserManage.userBindAccount:output_type -> sys.Empty
	2,  // 37: sys.Workspace.workspaceUpdate:output_type -> sys.Empty
	24, // [24:38] is the sub-list for method output_type
	10, // [10:24] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_proto_sys_proto_init() }
func file_proto_sys_proto_init() {
	if File_proto_sys_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_sys_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompareString); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CompareInt64); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithIDCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithAppCodeID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonUploadUrlGetMapReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonUploadUrlGetMapResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonUploadUrlGetOneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CommonUploadUrlGetOneResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WorkspaceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserWorkspace); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfoCreateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfoUpdateReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfoGetOneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfoDeleteReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*JwtToken); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserCaptchaReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserCaptchaResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserForgetPwdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserCodeToUserIDReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserCodeToUserIDResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserChangePwdReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserLoginReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserLoginResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserRegisterReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserRegisterResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserBindAccountReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RegUserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserCreateResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserCheckTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserCheckTokenResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_sys_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageInfo_OrderBy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_sys_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   40,
			NumExtensions: 0,
			NumServices:   3,
		},
		GoTypes:           file_proto_sys_proto_goTypes,
		DependencyIndexes: file_proto_sys_proto_depIdxs,
		MessageInfos:      file_proto_sys_proto_msgTypes,
	}.Build()
	File_proto_sys_proto = out.File
	file_proto_sys_proto_rawDesc = nil
	file_proto_sys_proto_goTypes = nil
	file_proto_sys_proto_depIdxs = nil
}
