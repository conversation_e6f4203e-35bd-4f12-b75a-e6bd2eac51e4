// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: proto/sys.proto

package sys

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Common_UploadUrlGetMap_FullMethodName = "/sys.Common/uploadUrlGetMap"
	Common_UploadUrlGetOne_FullMethodName = "/sys.Common/uploadUrlGetOne"
)

// CommonClient is the client API for Common service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CommonClient interface {
	UploadUrlGetMap(ctx context.Context, in *CommonUploadUrlGetMapReq, opts ...grpc.CallOption) (*CommonUploadUrlGetMapResp, error)
	UploadUrlGetOne(ctx context.Context, in *CommonUploadUrlGetOneReq, opts ...grpc.CallOption) (*CommonUploadUrlGetOneResp, error)
}

type commonClient struct {
	cc grpc.ClientConnInterface
}

func NewCommonClient(cc grpc.ClientConnInterface) CommonClient {
	return &commonClient{cc}
}

func (c *commonClient) UploadUrlGetMap(ctx context.Context, in *CommonUploadUrlGetMapReq, opts ...grpc.CallOption) (*CommonUploadUrlGetMapResp, error) {
	out := new(CommonUploadUrlGetMapResp)
	err := c.cc.Invoke(ctx, Common_UploadUrlGetMap_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *commonClient) UploadUrlGetOne(ctx context.Context, in *CommonUploadUrlGetOneReq, opts ...grpc.CallOption) (*CommonUploadUrlGetOneResp, error) {
	out := new(CommonUploadUrlGetOneResp)
	err := c.cc.Invoke(ctx, Common_UploadUrlGetOne_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CommonServer is the server API for Common service.
// All implementations must embed UnimplementedCommonServer
// for forward compatibility
type CommonServer interface {
	UploadUrlGetMap(context.Context, *CommonUploadUrlGetMapReq) (*CommonUploadUrlGetMapResp, error)
	UploadUrlGetOne(context.Context, *CommonUploadUrlGetOneReq) (*CommonUploadUrlGetOneResp, error)
	mustEmbedUnimplementedCommonServer()
}

// UnimplementedCommonServer must be embedded to have forward compatible implementations.
type UnimplementedCommonServer struct {
}

func (UnimplementedCommonServer) UploadUrlGetMap(context.Context, *CommonUploadUrlGetMapReq) (*CommonUploadUrlGetMapResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadUrlGetMap not implemented")
}
func (UnimplementedCommonServer) UploadUrlGetOne(context.Context, *CommonUploadUrlGetOneReq) (*CommonUploadUrlGetOneResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UploadUrlGetOne not implemented")
}
func (UnimplementedCommonServer) mustEmbedUnimplementedCommonServer() {}

// UnsafeCommonServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CommonServer will
// result in compilation errors.
type UnsafeCommonServer interface {
	mustEmbedUnimplementedCommonServer()
}

func RegisterCommonServer(s grpc.ServiceRegistrar, srv CommonServer) {
	s.RegisterService(&Common_ServiceDesc, srv)
}

func _Common_UploadUrlGetMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonUploadUrlGetMapReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServer).UploadUrlGetMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Common_UploadUrlGetMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServer).UploadUrlGetMap(ctx, req.(*CommonUploadUrlGetMapReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Common_UploadUrlGetOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CommonUploadUrlGetOneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CommonServer).UploadUrlGetOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Common_UploadUrlGetOne_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CommonServer).UploadUrlGetOne(ctx, req.(*CommonUploadUrlGetOneReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Common_ServiceDesc is the grpc.ServiceDesc for Common service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Common_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sys.Common",
	HandlerType: (*CommonServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "uploadUrlGetMap",
			Handler:    _Common_UploadUrlGetMap_Handler,
		},
		{
			MethodName: "uploadUrlGetOne",
			Handler:    _Common_UploadUrlGetOne_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/sys.proto",
}

const (
	UserManage_UserInfoUpdate_FullMethodName   = "/sys.UserManage/userInfoUpdate"
	UserManage_UserInfoGetOne_FullMethodName   = "/sys.UserManage/userInfoGetOne"
	UserManage_UserInfoDelete_FullMethodName   = "/sys.UserManage/userInfoDelete"
	UserManage_UserLogin_FullMethodName        = "/sys.UserManage/userLogin"
	UserManage_UserForgetPwd_FullMethodName    = "/sys.UserManage/userForgetPwd"
	UserManage_UserCaptcha_FullMethodName      = "/sys.UserManage/userCaptcha"
	UserManage_UserCheckToken_FullMethodName   = "/sys.UserManage/userCheckToken"
	UserManage_UserRegister_FullMethodName     = "/sys.UserManage/userRegister"
	UserManage_UserChangePwd_FullMethodName    = "/sys.UserManage/userChangePwd"
	UserManage_UserCodeToUserID_FullMethodName = "/sys.UserManage/userCodeToUserID"
	UserManage_UserBindAccount_FullMethodName  = "/sys.UserManage/userBindAccount"
)

// UserManageClient is the client API for UserManage service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserManageClient interface {
	UserInfoUpdate(ctx context.Context, in *UserInfoUpdateReq, opts ...grpc.CallOption) (*Empty, error)
	UserInfoGetOne(ctx context.Context, in *UserInfoGetOneReq, opts ...grpc.CallOption) (*UserInfo, error)
	UserInfoDelete(ctx context.Context, in *UserInfoDeleteReq, opts ...grpc.CallOption) (*Empty, error)
	UserLogin(ctx context.Context, in *UserLoginReq, opts ...grpc.CallOption) (*UserLoginResp, error)
	UserForgetPwd(ctx context.Context, in *UserForgetPwdReq, opts ...grpc.CallOption) (*Empty, error)
	UserCaptcha(ctx context.Context, in *UserCaptchaReq, opts ...grpc.CallOption) (*UserCaptchaResp, error)
	UserCheckToken(ctx context.Context, in *UserCheckTokenReq, opts ...grpc.CallOption) (*UserCheckTokenResp, error)
	UserRegister(ctx context.Context, in *UserRegisterReq, opts ...grpc.CallOption) (*UserRegisterResp, error)
	UserChangePwd(ctx context.Context, in *UserChangePwdReq, opts ...grpc.CallOption) (*Empty, error)
	UserCodeToUserID(ctx context.Context, in *UserCodeToUserIDReq, opts ...grpc.CallOption) (*UserCodeToUserIDResp, error)
	UserBindAccount(ctx context.Context, in *UserBindAccountReq, opts ...grpc.CallOption) (*Empty, error)
}

type userManageClient struct {
	cc grpc.ClientConnInterface
}

func NewUserManageClient(cc grpc.ClientConnInterface) UserManageClient {
	return &userManageClient{cc}
}

func (c *userManageClient) UserInfoUpdate(ctx context.Context, in *UserInfoUpdateReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserManage_UserInfoUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userManageClient) UserInfoGetOne(ctx context.Context, in *UserInfoGetOneReq, opts ...grpc.CallOption) (*UserInfo, error) {
	out := new(UserInfo)
	err := c.cc.Invoke(ctx, UserManage_UserInfoGetOne_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userManageClient) UserInfoDelete(ctx context.Context, in *UserInfoDeleteReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserManage_UserInfoDelete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userManageClient) UserLogin(ctx context.Context, in *UserLoginReq, opts ...grpc.CallOption) (*UserLoginResp, error) {
	out := new(UserLoginResp)
	err := c.cc.Invoke(ctx, UserManage_UserLogin_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userManageClient) UserForgetPwd(ctx context.Context, in *UserForgetPwdReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserManage_UserForgetPwd_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userManageClient) UserCaptcha(ctx context.Context, in *UserCaptchaReq, opts ...grpc.CallOption) (*UserCaptchaResp, error) {
	out := new(UserCaptchaResp)
	err := c.cc.Invoke(ctx, UserManage_UserCaptcha_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userManageClient) UserCheckToken(ctx context.Context, in *UserCheckTokenReq, opts ...grpc.CallOption) (*UserCheckTokenResp, error) {
	out := new(UserCheckTokenResp)
	err := c.cc.Invoke(ctx, UserManage_UserCheckToken_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userManageClient) UserRegister(ctx context.Context, in *UserRegisterReq, opts ...grpc.CallOption) (*UserRegisterResp, error) {
	out := new(UserRegisterResp)
	err := c.cc.Invoke(ctx, UserManage_UserRegister_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userManageClient) UserChangePwd(ctx context.Context, in *UserChangePwdReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserManage_UserChangePwd_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userManageClient) UserCodeToUserID(ctx context.Context, in *UserCodeToUserIDReq, opts ...grpc.CallOption) (*UserCodeToUserIDResp, error) {
	out := new(UserCodeToUserIDResp)
	err := c.cc.Invoke(ctx, UserManage_UserCodeToUserID_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *userManageClient) UserBindAccount(ctx context.Context, in *UserBindAccountReq, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, UserManage_UserBindAccount_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserManageServer is the server API for UserManage service.
// All implementations must embed UnimplementedUserManageServer
// for forward compatibility
type UserManageServer interface {
	UserInfoUpdate(context.Context, *UserInfoUpdateReq) (*Empty, error)
	UserInfoGetOne(context.Context, *UserInfoGetOneReq) (*UserInfo, error)
	UserInfoDelete(context.Context, *UserInfoDeleteReq) (*Empty, error)
	UserLogin(context.Context, *UserLoginReq) (*UserLoginResp, error)
	UserForgetPwd(context.Context, *UserForgetPwdReq) (*Empty, error)
	UserCaptcha(context.Context, *UserCaptchaReq) (*UserCaptchaResp, error)
	UserCheckToken(context.Context, *UserCheckTokenReq) (*UserCheckTokenResp, error)
	UserRegister(context.Context, *UserRegisterReq) (*UserRegisterResp, error)
	UserChangePwd(context.Context, *UserChangePwdReq) (*Empty, error)
	UserCodeToUserID(context.Context, *UserCodeToUserIDReq) (*UserCodeToUserIDResp, error)
	UserBindAccount(context.Context, *UserBindAccountReq) (*Empty, error)
	mustEmbedUnimplementedUserManageServer()
}

// UnimplementedUserManageServer must be embedded to have forward compatible implementations.
type UnimplementedUserManageServer struct {
}

func (UnimplementedUserManageServer) UserInfoUpdate(context.Context, *UserInfoUpdateReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfoUpdate not implemented")
}
func (UnimplementedUserManageServer) UserInfoGetOne(context.Context, *UserInfoGetOneReq) (*UserInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfoGetOne not implemented")
}
func (UnimplementedUserManageServer) UserInfoDelete(context.Context, *UserInfoDeleteReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserInfoDelete not implemented")
}
func (UnimplementedUserManageServer) UserLogin(context.Context, *UserLoginReq) (*UserLoginResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserLogin not implemented")
}
func (UnimplementedUserManageServer) UserForgetPwd(context.Context, *UserForgetPwdReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserForgetPwd not implemented")
}
func (UnimplementedUserManageServer) UserCaptcha(context.Context, *UserCaptchaReq) (*UserCaptchaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserCaptcha not implemented")
}
func (UnimplementedUserManageServer) UserCheckToken(context.Context, *UserCheckTokenReq) (*UserCheckTokenResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserCheckToken not implemented")
}
func (UnimplementedUserManageServer) UserRegister(context.Context, *UserRegisterReq) (*UserRegisterResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserRegister not implemented")
}
func (UnimplementedUserManageServer) UserChangePwd(context.Context, *UserChangePwdReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserChangePwd not implemented")
}
func (UnimplementedUserManageServer) UserCodeToUserID(context.Context, *UserCodeToUserIDReq) (*UserCodeToUserIDResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserCodeToUserID not implemented")
}
func (UnimplementedUserManageServer) UserBindAccount(context.Context, *UserBindAccountReq) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UserBindAccount not implemented")
}
func (UnimplementedUserManageServer) mustEmbedUnimplementedUserManageServer() {}

// UnsafeUserManageServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserManageServer will
// result in compilation errors.
type UnsafeUserManageServer interface {
	mustEmbedUnimplementedUserManageServer()
}

func RegisterUserManageServer(s grpc.ServiceRegistrar, srv UserManageServer) {
	s.RegisterService(&UserManage_ServiceDesc, srv)
}

func _UserManage_UserInfoUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoUpdateReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserManageServer).UserInfoUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserManage_UserInfoUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserManageServer).UserInfoUpdate(ctx, req.(*UserInfoUpdateReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserManage_UserInfoGetOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoGetOneReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserManageServer).UserInfoGetOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserManage_UserInfoGetOne_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserManageServer).UserInfoGetOne(ctx, req.(*UserInfoGetOneReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserManage_UserInfoDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserInfoDeleteReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserManageServer).UserInfoDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserManage_UserInfoDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserManageServer).UserInfoDelete(ctx, req.(*UserInfoDeleteReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserManage_UserLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserLoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserManageServer).UserLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserManage_UserLogin_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserManageServer).UserLogin(ctx, req.(*UserLoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserManage_UserForgetPwd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserForgetPwdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserManageServer).UserForgetPwd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserManage_UserForgetPwd_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserManageServer).UserForgetPwd(ctx, req.(*UserForgetPwdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserManage_UserCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserCaptchaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserManageServer).UserCaptcha(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserManage_UserCaptcha_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserManageServer).UserCaptcha(ctx, req.(*UserCaptchaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserManage_UserCheckToken_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserCheckTokenReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserManageServer).UserCheckToken(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserManage_UserCheckToken_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserManageServer).UserCheckToken(ctx, req.(*UserCheckTokenReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserManage_UserRegister_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserRegisterReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserManageServer).UserRegister(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserManage_UserRegister_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserManageServer).UserRegister(ctx, req.(*UserRegisterReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserManage_UserChangePwd_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserChangePwdReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserManageServer).UserChangePwd(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserManage_UserChangePwd_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserManageServer).UserChangePwd(ctx, req.(*UserChangePwdReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserManage_UserCodeToUserID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserCodeToUserIDReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserManageServer).UserCodeToUserID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserManage_UserCodeToUserID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserManageServer).UserCodeToUserID(ctx, req.(*UserCodeToUserIDReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _UserManage_UserBindAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UserBindAccountReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserManageServer).UserBindAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: UserManage_UserBindAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserManageServer).UserBindAccount(ctx, req.(*UserBindAccountReq))
	}
	return interceptor(ctx, in, info, handler)
}

// UserManage_ServiceDesc is the grpc.ServiceDesc for UserManage service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var UserManage_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sys.UserManage",
	HandlerType: (*UserManageServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "userInfoUpdate",
			Handler:    _UserManage_UserInfoUpdate_Handler,
		},
		{
			MethodName: "userInfoGetOne",
			Handler:    _UserManage_UserInfoGetOne_Handler,
		},
		{
			MethodName: "userInfoDelete",
			Handler:    _UserManage_UserInfoDelete_Handler,
		},
		{
			MethodName: "userLogin",
			Handler:    _UserManage_UserLogin_Handler,
		},
		{
			MethodName: "userForgetPwd",
			Handler:    _UserManage_UserForgetPwd_Handler,
		},
		{
			MethodName: "userCaptcha",
			Handler:    _UserManage_UserCaptcha_Handler,
		},
		{
			MethodName: "userCheckToken",
			Handler:    _UserManage_UserCheckToken_Handler,
		},
		{
			MethodName: "userRegister",
			Handler:    _UserManage_UserRegister_Handler,
		},
		{
			MethodName: "userChangePwd",
			Handler:    _UserManage_UserChangePwd_Handler,
		},
		{
			MethodName: "userCodeToUserID",
			Handler:    _UserManage_UserCodeToUserID_Handler,
		},
		{
			MethodName: "userBindAccount",
			Handler:    _UserManage_UserBindAccount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/sys.proto",
}

const (
	Workspace_WorkspaceUpdate_FullMethodName = "/sys.Workspace/workspaceUpdate"
)

// WorkspaceClient is the client API for Workspace service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WorkspaceClient interface {
	WorkspaceUpdate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
}

type workspaceClient struct {
	cc grpc.ClientConnInterface
}

func NewWorkspaceClient(cc grpc.ClientConnInterface) WorkspaceClient {
	return &workspaceClient{cc}
}

func (c *workspaceClient) WorkspaceUpdate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Workspace_WorkspaceUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WorkspaceServer is the server API for Workspace service.
// All implementations must embed UnimplementedWorkspaceServer
// for forward compatibility
type WorkspaceServer interface {
	WorkspaceUpdate(context.Context, *Empty) (*Empty, error)
	mustEmbedUnimplementedWorkspaceServer()
}

// UnimplementedWorkspaceServer must be embedded to have forward compatible implementations.
type UnimplementedWorkspaceServer struct {
}

func (UnimplementedWorkspaceServer) WorkspaceUpdate(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method WorkspaceUpdate not implemented")
}
func (UnimplementedWorkspaceServer) mustEmbedUnimplementedWorkspaceServer() {}

// UnsafeWorkspaceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WorkspaceServer will
// result in compilation errors.
type UnsafeWorkspaceServer interface {
	mustEmbedUnimplementedWorkspaceServer()
}

func RegisterWorkspaceServer(s grpc.ServiceRegistrar, srv WorkspaceServer) {
	s.RegisterService(&Workspace_ServiceDesc, srv)
}

func _Workspace_WorkspaceUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WorkspaceServer).WorkspaceUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Workspace_WorkspaceUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WorkspaceServer).WorkspaceUpdate(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// Workspace_ServiceDesc is the grpc.ServiceDesc for Workspace service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Workspace_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sys.Workspace",
	HandlerType: (*WorkspaceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "workspaceUpdate",
			Handler:    _Workspace_WorkspaceUpdate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/sys.proto",
}
