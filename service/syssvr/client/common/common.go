// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.1
// Source: sys.proto

package common

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CommonUploadUrlGetMapReq  = sys.CommonUploadUrlGetMapReq
	CommonUploadUrlGetMapResp = sys.CommonUploadUrlGetMapResp
	CommonUploadUrlGetOneReq  = sys.CommonUploadUrlGetOneReq
	CommonUploadUrlGetOneResp = sys.CommonUploadUrlGetOneResp
	CompareInt64              = sys.CompareInt64
	CompareString             = sys.CompareString
	DateRange                 = sys.DateRange
	Empty                     = sys.Empty
	JwtToken                  = sys.JwtToken
	PageInfo                  = sys.PageInfo
	PageInfo_OrderBy          = sys.PageInfo_OrderBy
	RegUserInfo               = sys.RegUserInfo
	TimeRange                 = sys.TimeRange
	UserBindAccountReq        = sys.UserBindAccountReq
	UserCaptchaReq            = sys.UserCaptchaReq
	UserCaptchaResp           = sys.UserCaptchaResp
	UserChangePwdReq          = sys.UserChangePwdReq
	UserCheckTokenReq         = sys.UserCheckTokenReq
	UserCheckTokenResp        = sys.UserCheckTokenResp
	UserCodeToUserIDReq       = sys.UserCodeToUserIDReq
	UserCodeToUserIDResp      = sys.UserCodeToUserIDResp
	UserCreateResp            = sys.UserCreateResp
	UserForgetPwdReq          = sys.UserForgetPwdReq
	UserInfo                  = sys.UserInfo
	UserInfoCreateReq         = sys.UserInfoCreateReq
	UserInfoDeleteReq         = sys.UserInfoDeleteReq
	UserInfoGetOneReq         = sys.UserInfoGetOneReq
	UserInfoUpdateReq         = sys.UserInfoUpdateReq
	UserLoginReq              = sys.UserLoginReq
	UserLoginResp             = sys.UserLoginResp
	UserRegisterReq           = sys.UserRegisterReq
	UserRegisterResp          = sys.UserRegisterResp
	UserWorkspace             = sys.UserWorkspace
	WithAppCodeID             = sys.WithAppCodeID
	WithCode                  = sys.WithCode
	WithID                    = sys.WithID
	WithIDCode                = sys.WithIDCode
	WorkspaceInfo             = sys.WorkspaceInfo

	Common interface {
		UploadUrlGetMap(ctx context.Context, in *CommonUploadUrlGetMapReq, opts ...grpc.CallOption) (*CommonUploadUrlGetMapResp, error)
		UploadUrlGetOne(ctx context.Context, in *CommonUploadUrlGetOneReq, opts ...grpc.CallOption) (*CommonUploadUrlGetOneResp, error)
	}

	defaultCommon struct {
		cli zrpc.Client
	}

	directCommon struct {
		svcCtx *svc.ServiceContext
		svr    sys.CommonServer
	}
)

func NewCommon(cli zrpc.Client) Common {
	return &defaultCommon{
		cli: cli,
	}
}

func NewDirectCommon(svcCtx *svc.ServiceContext, svr sys.CommonServer) Common {
	return &directCommon{
		svr:    svr,
		svcCtx: svcCtx,
	}
}

func (m *defaultCommon) UploadUrlGetMap(ctx context.Context, in *CommonUploadUrlGetMapReq, opts ...grpc.CallOption) (*CommonUploadUrlGetMapResp, error) {
	client := sys.NewCommonClient(m.cli.Conn())
	return client.UploadUrlGetMap(ctx, in, opts...)
}

func (d *directCommon) UploadUrlGetMap(ctx context.Context, in *CommonUploadUrlGetMapReq, opts ...grpc.CallOption) (*CommonUploadUrlGetMapResp, error) {
	return d.svr.UploadUrlGetMap(ctx, in)
}

func (m *defaultCommon) UploadUrlGetOne(ctx context.Context, in *CommonUploadUrlGetOneReq, opts ...grpc.CallOption) (*CommonUploadUrlGetOneResp, error) {
	client := sys.NewCommonClient(m.cli.Conn())
	return client.UploadUrlGetOne(ctx, in, opts...)
}

func (d *directCommon) UploadUrlGetOne(ctx context.Context, in *CommonUploadUrlGetOneReq, opts ...grpc.CallOption) (*CommonUploadUrlGetOneResp, error) {
	return d.svr.UploadUrlGetOne(ctx, in)
}
