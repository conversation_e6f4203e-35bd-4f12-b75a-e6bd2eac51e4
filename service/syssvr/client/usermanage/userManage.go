// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.1
// Source: sys.proto

package usermanage

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CommonUploadUrlGetMapReq  = sys.CommonUploadUrlGetMapReq
	CommonUploadUrlGetMapResp = sys.CommonUploadUrlGetMapResp
	CommonUploadUrlGetOneReq  = sys.CommonUploadUrlGetOneReq
	CommonUploadUrlGetOneResp = sys.CommonUploadUrlGetOneResp
	CompareInt64              = sys.CompareInt64
	CompareString             = sys.CompareString
	DateRange                 = sys.DateRange
	Empty                     = sys.Empty
	JwtToken                  = sys.JwtToken
	PageInfo                  = sys.PageInfo
	PageInfo_OrderBy          = sys.PageInfo_OrderBy
	RegUserInfo               = sys.RegUserInfo
	TimeRange                 = sys.TimeRange
	UserBindAccountReq        = sys.UserBindAccountReq
	UserCaptchaReq            = sys.UserCaptchaReq
	UserCaptchaResp           = sys.UserCaptchaResp
	UserChangePwdReq          = sys.UserChangePwdReq
	UserCheckTokenReq         = sys.UserCheckTokenReq
	UserCheckTokenResp        = sys.UserCheckTokenResp
	UserCodeToUserIDReq       = sys.UserCodeToUserIDReq
	UserCodeToUserIDResp      = sys.UserCodeToUserIDResp
	UserCreateResp            = sys.UserCreateResp
	UserForgetPwdReq          = sys.UserForgetPwdReq
	UserInfo                  = sys.UserInfo
	UserInfoCreateReq         = sys.UserInfoCreateReq
	UserInfoDeleteReq         = sys.UserInfoDeleteReq
	UserInfoGetOneReq         = sys.UserInfoGetOneReq
	UserInfoUpdateReq         = sys.UserInfoUpdateReq
	UserLoginReq              = sys.UserLoginReq
	UserLoginResp             = sys.UserLoginResp
	UserRegisterReq           = sys.UserRegisterReq
	UserRegisterResp          = sys.UserRegisterResp
	UserWorkspace             = sys.UserWorkspace
	WithAppCodeID             = sys.WithAppCodeID
	WithCode                  = sys.WithCode
	WithID                    = sys.WithID
	WithIDCode                = sys.WithIDCode
	WorkspaceInfo             = sys.WorkspaceInfo

	UserManage interface {
		UserInfoUpdate(ctx context.Context, in *UserInfoUpdateReq, opts ...grpc.CallOption) (*Empty, error)
		UserInfoGetOne(ctx context.Context, in *UserInfoGetOneReq, opts ...grpc.CallOption) (*UserInfo, error)
		UserInfoDelete(ctx context.Context, in *UserInfoDeleteReq, opts ...grpc.CallOption) (*Empty, error)
		UserLogin(ctx context.Context, in *UserLoginReq, opts ...grpc.CallOption) (*UserLoginResp, error)
		UserForgetPwd(ctx context.Context, in *UserForgetPwdReq, opts ...grpc.CallOption) (*Empty, error)
		UserCaptcha(ctx context.Context, in *UserCaptchaReq, opts ...grpc.CallOption) (*UserCaptchaResp, error)
		UserCheckToken(ctx context.Context, in *UserCheckTokenReq, opts ...grpc.CallOption) (*UserCheckTokenResp, error)
		UserRegister(ctx context.Context, in *UserRegisterReq, opts ...grpc.CallOption) (*UserRegisterResp, error)
		UserChangePwd(ctx context.Context, in *UserChangePwdReq, opts ...grpc.CallOption) (*Empty, error)
		UserCodeToUserID(ctx context.Context, in *UserCodeToUserIDReq, opts ...grpc.CallOption) (*UserCodeToUserIDResp, error)
		UserBindAccount(ctx context.Context, in *UserBindAccountReq, opts ...grpc.CallOption) (*Empty, error)
	}

	defaultUserManage struct {
		cli zrpc.Client
	}

	directUserManage struct {
		svcCtx *svc.ServiceContext
		svr    sys.UserManageServer
	}
)

func NewUserManage(cli zrpc.Client) UserManage {
	return &defaultUserManage{
		cli: cli,
	}
}

func NewDirectUserManage(svcCtx *svc.ServiceContext, svr sys.UserManageServer) UserManage {
	return &directUserManage{
		svr:    svr,
		svcCtx: svcCtx,
	}
}

func (m *defaultUserManage) UserInfoUpdate(ctx context.Context, in *UserInfoUpdateReq, opts ...grpc.CallOption) (*Empty, error) {
	client := sys.NewUserManageClient(m.cli.Conn())
	return client.UserInfoUpdate(ctx, in, opts...)
}

func (d *directUserManage) UserInfoUpdate(ctx context.Context, in *UserInfoUpdateReq, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.UserInfoUpdate(ctx, in)
}

func (m *defaultUserManage) UserInfoGetOne(ctx context.Context, in *UserInfoGetOneReq, opts ...grpc.CallOption) (*UserInfo, error) {
	client := sys.NewUserManageClient(m.cli.Conn())
	return client.UserInfoGetOne(ctx, in, opts...)
}

func (d *directUserManage) UserInfoGetOne(ctx context.Context, in *UserInfoGetOneReq, opts ...grpc.CallOption) (*UserInfo, error) {
	return d.svr.UserInfoGetOne(ctx, in)
}

func (m *defaultUserManage) UserInfoDelete(ctx context.Context, in *UserInfoDeleteReq, opts ...grpc.CallOption) (*Empty, error) {
	client := sys.NewUserManageClient(m.cli.Conn())
	return client.UserInfoDelete(ctx, in, opts...)
}

func (d *directUserManage) UserInfoDelete(ctx context.Context, in *UserInfoDeleteReq, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.UserInfoDelete(ctx, in)
}

func (m *defaultUserManage) UserLogin(ctx context.Context, in *UserLoginReq, opts ...grpc.CallOption) (*UserLoginResp, error) {
	client := sys.NewUserManageClient(m.cli.Conn())
	return client.UserLogin(ctx, in, opts...)
}

func (d *directUserManage) UserLogin(ctx context.Context, in *UserLoginReq, opts ...grpc.CallOption) (*UserLoginResp, error) {
	return d.svr.UserLogin(ctx, in)
}

func (m *defaultUserManage) UserForgetPwd(ctx context.Context, in *UserForgetPwdReq, opts ...grpc.CallOption) (*Empty, error) {
	client := sys.NewUserManageClient(m.cli.Conn())
	return client.UserForgetPwd(ctx, in, opts...)
}

func (d *directUserManage) UserForgetPwd(ctx context.Context, in *UserForgetPwdReq, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.UserForgetPwd(ctx, in)
}

func (m *defaultUserManage) UserCaptcha(ctx context.Context, in *UserCaptchaReq, opts ...grpc.CallOption) (*UserCaptchaResp, error) {
	client := sys.NewUserManageClient(m.cli.Conn())
	return client.UserCaptcha(ctx, in, opts...)
}

func (d *directUserManage) UserCaptcha(ctx context.Context, in *UserCaptchaReq, opts ...grpc.CallOption) (*UserCaptchaResp, error) {
	return d.svr.UserCaptcha(ctx, in)
}

func (m *defaultUserManage) UserCheckToken(ctx context.Context, in *UserCheckTokenReq, opts ...grpc.CallOption) (*UserCheckTokenResp, error) {
	client := sys.NewUserManageClient(m.cli.Conn())
	return client.UserCheckToken(ctx, in, opts...)
}

func (d *directUserManage) UserCheckToken(ctx context.Context, in *UserCheckTokenReq, opts ...grpc.CallOption) (*UserCheckTokenResp, error) {
	return d.svr.UserCheckToken(ctx, in)
}

func (m *defaultUserManage) UserRegister(ctx context.Context, in *UserRegisterReq, opts ...grpc.CallOption) (*UserRegisterResp, error) {
	client := sys.NewUserManageClient(m.cli.Conn())
	return client.UserRegister(ctx, in, opts...)
}

func (d *directUserManage) UserRegister(ctx context.Context, in *UserRegisterReq, opts ...grpc.CallOption) (*UserRegisterResp, error) {
	return d.svr.UserRegister(ctx, in)
}

func (m *defaultUserManage) UserChangePwd(ctx context.Context, in *UserChangePwdReq, opts ...grpc.CallOption) (*Empty, error) {
	client := sys.NewUserManageClient(m.cli.Conn())
	return client.UserChangePwd(ctx, in, opts...)
}

func (d *directUserManage) UserChangePwd(ctx context.Context, in *UserChangePwdReq, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.UserChangePwd(ctx, in)
}

func (m *defaultUserManage) UserCodeToUserID(ctx context.Context, in *UserCodeToUserIDReq, opts ...grpc.CallOption) (*UserCodeToUserIDResp, error) {
	client := sys.NewUserManageClient(m.cli.Conn())
	return client.UserCodeToUserID(ctx, in, opts...)
}

func (d *directUserManage) UserCodeToUserID(ctx context.Context, in *UserCodeToUserIDReq, opts ...grpc.CallOption) (*UserCodeToUserIDResp, error) {
	return d.svr.UserCodeToUserID(ctx, in)
}

func (m *defaultUserManage) UserBindAccount(ctx context.Context, in *UserBindAccountReq, opts ...grpc.CallOption) (*Empty, error) {
	client := sys.NewUserManageClient(m.cli.Conn())
	return client.UserBindAccount(ctx, in, opts...)
}

func (d *directUserManage) UserBindAccount(ctx context.Context, in *UserBindAccountReq, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.UserBindAccount(ctx, in)
}
