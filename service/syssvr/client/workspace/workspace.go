// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.1
// Source: sys.proto

package workspace

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/syssvr/pb/sys"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	CommonUploadUrlGetMapReq  = sys.CommonUploadUrlGetMapReq
	CommonUploadUrlGetMapResp = sys.CommonUploadUrlGetMapResp
	CommonUploadUrlGetOneReq  = sys.CommonUploadUrlGetOneReq
	CommonUploadUrlGetOneResp = sys.CommonUploadUrlGetOneResp
	CompareInt64              = sys.CompareInt64
	CompareString             = sys.CompareString
	DateRange                 = sys.DateRange
	Empty                     = sys.Empty
	JwtToken                  = sys.JwtToken
	PageInfo                  = sys.PageInfo
	PageInfo_OrderBy          = sys.PageInfo_OrderBy
	RegUserInfo               = sys.RegUserInfo
	TimeRange                 = sys.TimeRange
	UserBindAccountReq        = sys.UserBindAccountReq
	UserCaptchaReq            = sys.UserCaptchaReq
	UserCaptchaResp           = sys.UserCaptchaResp
	UserChangePwdReq          = sys.UserChangePwdReq
	UserCheckTokenReq         = sys.UserCheckTokenReq
	UserCheckTokenResp        = sys.UserCheckTokenResp
	UserCodeToUserIDReq       = sys.UserCodeToUserIDReq
	UserCodeToUserIDResp      = sys.UserCodeToUserIDResp
	UserCreateResp            = sys.UserCreateResp
	UserForgetPwdReq          = sys.UserForgetPwdReq
	UserInfo                  = sys.UserInfo
	UserInfoCreateReq         = sys.UserInfoCreateReq
	UserInfoDeleteReq         = sys.UserInfoDeleteReq
	UserInfoGetOneReq         = sys.UserInfoGetOneReq
	UserInfoUpdateReq         = sys.UserInfoUpdateReq
	UserLoginReq              = sys.UserLoginReq
	UserLoginResp             = sys.UserLoginResp
	UserRegisterReq           = sys.UserRegisterReq
	UserRegisterResp          = sys.UserRegisterResp
	UserWorkspace             = sys.UserWorkspace
	WithAppCodeID             = sys.WithAppCodeID
	WithCode                  = sys.WithCode
	WithID                    = sys.WithID
	WithIDCode                = sys.WithIDCode
	WorkspaceInfo             = sys.WorkspaceInfo

	Workspace interface {
		WorkspaceUpdate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	}

	defaultWorkspace struct {
		cli zrpc.Client
	}

	directWorkspace struct {
		svcCtx *svc.ServiceContext
		svr    sys.WorkspaceServer
	}
)

func NewWorkspace(cli zrpc.Client) Workspace {
	return &defaultWorkspace{
		cli: cli,
	}
}

func NewDirectWorkspace(svcCtx *svc.ServiceContext, svr sys.WorkspaceServer) Workspace {
	return &directWorkspace{
		svr:    svr,
		svcCtx: svcCtx,
	}
}

func (m *defaultWorkspace) WorkspaceUpdate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	client := sys.NewWorkspaceClient(m.cli.Conn())
	return client.WorkspaceUpdate(ctx, in, opts...)
}

func (d *directWorkspace) WorkspaceUpdate(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.WorkspaceUpdate(ctx, in)
}
