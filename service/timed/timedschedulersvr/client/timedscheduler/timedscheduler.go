// Code generated by goctl. DO NOT EDIT.
// Source: timedscheduler.proto

package timedscheduler

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/timed/timedschedulersvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/timed/timedschedulersvr/pb/timedscheduler"

	"github.com/zeromicro/go-zero/zrpc"
	"google.golang.org/grpc"
)

type (
	Empty = timedscheduler.Empty

	Timedscheduler interface {
		Ping(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
	}

	defaultTimedscheduler struct {
		cli zrpc.Client
	}

	directTimedscheduler struct {
		svcCtx *svc.ServiceContext
		svr    timedscheduler.TimedschedulerServer
	}
)

func NewTimedscheduler(cli zrpc.Client) Timedscheduler {
	return &defaultTimedscheduler{
		cli: cli,
	}
}

func NewDirectTimedscheduler(svcCtx *svc.ServiceContext, svr timedscheduler.TimedschedulerServer) Timedscheduler {
	return &directTimedscheduler{
		svr:    svr,
		svcCtx: svcCtx,
	}
}

func (m *defaultTimedscheduler) Ping(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	client := timedscheduler.NewTimedschedulerClient(m.cli.Conn())
	return client.Ping(ctx, in, opts...)
}

func (d *directTimedscheduler) Ping(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	return d.svr.Ping(ctx, in)
}
