// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.4
// source: proto/timedscheduler.proto

package timedscheduler

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Empty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Empty) Reset() {
	*x = Empty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedscheduler_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Empty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Empty) ProtoMessage() {}

func (x *Empty) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedscheduler_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Empty.ProtoReflect.Descriptor instead.
func (*Empty) Descriptor() ([]byte, []int) {
	return file_proto_timedscheduler_proto_rawDescGZIP(), []int{0}
}

var File_proto_timedscheduler_proto protoreflect.FileDescriptor

var file_proto_timedscheduler_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e, 0x74, 0x69,
	0x6d, 0x65, 0x64, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x22, 0x07, 0x0a, 0x05,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x32, 0x46, 0x0a, 0x0e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x34, 0x0a, 0x04, 0x70, 0x69, 0x6e, 0x67, 0x12,
	0x15, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x1a, 0x15, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x42, 0x13, 0x5a,
	0x11, 0x70, 0x62, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_timedscheduler_proto_rawDescOnce sync.Once
	file_proto_timedscheduler_proto_rawDescData = file_proto_timedscheduler_proto_rawDesc
)

func file_proto_timedscheduler_proto_rawDescGZIP() []byte {
	file_proto_timedscheduler_proto_rawDescOnce.Do(func() {
		file_proto_timedscheduler_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_timedscheduler_proto_rawDescData)
	})
	return file_proto_timedscheduler_proto_rawDescData
}

var file_proto_timedscheduler_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_proto_timedscheduler_proto_goTypes = []interface{}{
	(*Empty)(nil), // 0: timedscheduler.Empty
}
var file_proto_timedscheduler_proto_depIdxs = []int32{
	0, // 0: timedscheduler.timedscheduler.ping:input_type -> timedscheduler.Empty
	0, // 1: timedscheduler.timedscheduler.ping:output_type -> timedscheduler.Empty
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_proto_timedscheduler_proto_init() }
func file_proto_timedscheduler_proto_init() {
	if File_proto_timedscheduler_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_timedscheduler_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Empty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_timedscheduler_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_timedscheduler_proto_goTypes,
		DependencyIndexes: file_proto_timedscheduler_proto_depIdxs,
		MessageInfos:      file_proto_timedscheduler_proto_msgTypes,
	}.Build()
	File_proto_timedscheduler_proto = out.File
	file_proto_timedscheduler_proto_rawDesc = nil
	file_proto_timedscheduler_proto_goTypes = nil
	file_proto_timedscheduler_proto_depIdxs = nil
}
