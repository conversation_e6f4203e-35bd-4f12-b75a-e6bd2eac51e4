// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: proto/timedscheduler.proto

package timedscheduler

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	Timedscheduler_Ping_FullMethodName = "/timedscheduler.timedscheduler/ping"
)

// TimedschedulerClient is the client API for Timedscheduler service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TimedschedulerClient interface {
	Ping(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error)
}

type timedschedulerClient struct {
	cc grpc.ClientConnInterface
}

func NewTimedschedulerClient(cc grpc.ClientConnInterface) TimedschedulerClient {
	return &timedschedulerClient{cc}
}

func (c *timedschedulerClient) Ping(ctx context.Context, in *Empty, opts ...grpc.CallOption) (*Empty, error) {
	out := new(Empty)
	err := c.cc.Invoke(ctx, Timedscheduler_Ping_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TimedschedulerServer is the server API for Timedscheduler service.
// All implementations must embed UnimplementedTimedschedulerServer
// for forward compatibility
type TimedschedulerServer interface {
	Ping(context.Context, *Empty) (*Empty, error)
	mustEmbedUnimplementedTimedschedulerServer()
}

// UnimplementedTimedschedulerServer must be embedded to have forward compatible implementations.
type UnimplementedTimedschedulerServer struct {
}

func (UnimplementedTimedschedulerServer) Ping(context.Context, *Empty) (*Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Ping not implemented")
}
func (UnimplementedTimedschedulerServer) mustEmbedUnimplementedTimedschedulerServer() {}

// UnsafeTimedschedulerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TimedschedulerServer will
// result in compilation errors.
type UnsafeTimedschedulerServer interface {
	mustEmbedUnimplementedTimedschedulerServer()
}

func RegisterTimedschedulerServer(s grpc.ServiceRegistrar, srv TimedschedulerServer) {
	s.RegisterService(&Timedscheduler_ServiceDesc, srv)
}

func _Timedscheduler_Ping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(Empty)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedschedulerServer).Ping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Timedscheduler_Ping_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedschedulerServer).Ping(ctx, req.(*Empty))
	}
	return interceptor(ctx, in, info, handler)
}

// Timedscheduler_ServiceDesc is the grpc.ServiceDesc for Timedscheduler service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Timedscheduler_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "timedscheduler.timedscheduler",
	HandlerType: (*TimedschedulerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ping",
			Handler:    _Timedscheduler_Ping_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/timedscheduler.proto",
}
