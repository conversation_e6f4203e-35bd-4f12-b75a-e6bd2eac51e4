Name: timedscheduler.rpc
ListenOn: 0.0.0.0:9284
Etcd:
  Hosts:
    - localhost:2379
  Key: timedscheduler.rpc
Event:
  
CacheRedis:
  - Host: localhost:6379
    Pass:
    Type: node
TimedJobRpc:
  Enable: true
  Conf:
    Timeout: 1000000
#Database:
#  DBType: pgsql
#  DSN: host=localhost user= password= dbname=gleketest port=5432 sslmode=disable TimeZone=Asia/Shanghai
Database:
  IsInitTable: true
  DBType: mysql
  DSN: root:password@tcp(localhost:3306)/iThings?charset=utf8mb4&collation=utf8mb4_bin&parseTime=true&loc=Asia%2FShanghai