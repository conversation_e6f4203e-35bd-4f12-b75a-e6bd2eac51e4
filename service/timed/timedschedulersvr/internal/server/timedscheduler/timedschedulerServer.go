// Code generated by goctl. DO NOT EDIT.
// Source: timedscheduler.proto

package server

import (
	"context"

	"github.com/FREEZONEX/Tier0-Backend/service/timed/timedschedulersvr/internal/logic/timedscheduler"
	"github.com/FREEZONEX/Tier0-Backend/service/timed/timedschedulersvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/timed/timedschedulersvr/pb/timedscheduler"
)

type TimedschedulerServer struct {
	svcCtx *svc.ServiceContext
	timedscheduler.UnimplementedTimedschedulerServer
}

func NewTimedschedulerServer(svcCtx *svc.ServiceContext) *TimedschedulerServer {
	return &TimedschedulerServer{
		svcCtx: svcCtx,
	}
}

func (s *TimedschedulerServer) Ping(ctx context.Context, in *timedscheduler.Empty) (*timedscheduler.Empty, error) {
	l := timedschedulerlogic.NewPingLogic(ctx, s.svcCtx)
	return l.<PERSON>(in)
}
