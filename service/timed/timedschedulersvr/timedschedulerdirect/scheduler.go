package timedschedulerdirect

import (
	client "github.com/FREEZONEX/Tier0-Backend/service/timed/timedschedulersvr/client/timedscheduler"
	server "github.com/FREEZONEX/Tier0-Backend/service/timed/timedschedulersvr/internal/server/timedscheduler"
)

var (
	schedulerSvr client.Timedscheduler
)

func NewScheduler(runSvr bool) client.Timedscheduler {
	svcCtx := GetSvcCtx()
	if runSvr {
		RunServer(svcCtx)
	}
	svr := client.NewDirectTimedscheduler(svcCtx, server.NewTimedschedulerServer(svcCtx))
	return svr
}
