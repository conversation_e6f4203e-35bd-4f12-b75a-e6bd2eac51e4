syntax = "proto3";

package timedjob;
option go_package="pb/timedjob";

message PageInfo{
  int64 page = 1;
  int64 size = 2;
  //排序信息
  repeated OrderBy orders = 3;
  message OrderBy {
    //排序的字段名
    string filed = 1;
    //排序方式：0 aes, 1 desc
    int64 sort = 2;
  }
}

message Response {
}

message WithCode{
  string code = 1;
}

message WithGroupCode{
  string code = 1;
  string groupCode =2;
}

service TimedManage {

  rpc TaskGroupCreate(TaskGroup)returns(Response);
  rpc TaskGroupUpdate(TaskGroup)returns(Response);
  rpc TaskGroupDelete(WithCode)returns(Response);
  rpc TaskGroupIndex(TaskGroupIndexReq)returns(TaskGroupIndexResp);
  rpc TaskGroupRead(WithCode)returns(TaskGroup);

  rpc TaskInfoCreate(TaskInfo)returns(Response);
  rpc TaskInfoUpdate(TaskInfo)returns(Response);
  rpc TaskInfoDelete(WithGroupCode)returns(Response);
  rpc TaskInfoIndex(TaskInfoIndexReq)returns(TaskInfoIndexResp);
  rpc TaskInfoRead(WithGroupCode)returns(TaskInfo);
  rpc TaskLogIndex(TaskLogIndexReq)returns(TaskLogIndexResp);

  //发送延时请求,如果任务不存在,则会自动创建,但是自动创建的需要填写param
  rpc TaskSend(TaskSendReq) returns(TaskWithTaskID);
  rpc TaskCancel(TaskWithTaskID) returns(Response);
}

message TaskWithTaskID {
  string taskID = 1;
}


message TaskLogIndexReq{
  PageInfo page = 1;//分页信息 只获取一个则不填
  string groupCode = 2;//组编码
  string taskCode = 5;//任务编码
}

message TaskLogIndexResp{
  repeated TaskLog list = 1;
  int64  total = 2;//总数(只有分页的时候会返回)
}

message TaskLog{
  int64 id =1;
  string groupCode =2; //组编码
  string taskCode =3; //任务编码
  string params =4; // 任务参数
  int64 resultCode =5; //结果code
  string resultMsg =6; //结果消息
  int64 createdTime =7;
  TaskLogSql sql =8;
  TaskLogScript script =9; //脚本日志
}

message TaskLogSql{
  int64 selectNum =2;//查询的数量
  int64 execNum =3;//执行的数量
}

message TaskLogScript{
  repeated TaskExecLog execLog =9; //执行日志
}

message TaskExecLog{
  string  level =1;//日志级别: info warn error
  string content =2;//日志内容
  int64 createdTime=3;//日志创建时间
}

message TaskGroupIndexReq{
  PageInfo page = 1;//分页信息 只获取一个则不填
}

message TaskGroupIndexResp{
  repeated TaskGroup list = 1;
  int64  total = 2;//总数(只有分页的时候会返回)
}
message TaskInfoIndexReq{
  PageInfo page = 1;//分页信息 只获取一个则不填
  string groupCode = 2;//组编码
}
message TaskInfoIndexResp{
  repeated TaskInfo list = 1;
  int64  total = 2;//总数(只有分页的时候会返回)
}

message TaskInfo {
  string groupCode = 2;//组编码
  int64 type = 3;//任务类型 1 定时任务 2 延时任务 3 消息队列触发
  string name = 4;// 任务名称
  string code = 5;//任务编码
  repeated string topics =1;//触发topic列表
  string params = 6;// 任务参数,延时任务如果没有传任务参数会拿数据库的参数来执行
  string cronExpr = 7;// cron执行表达式
  int64 status = 8;// 状态
  int64 priority = 9;//优先级: 10:critical 最高优先级  3: default 普通优先级 1:low 低优先级
}

message TaskGroup {
  string code =1;//任务组编码
  string name =2;// 组名
  string type =3;//组类型:queue(消息队列消息发送)  sql(执行sql) script(脚本执行) email(邮件发送) http(http请求)
  string subType =4;//组子类型 natsJs nats                    js
  int64 priority =5;//组优先级: 6:critical 最高优先级  3: default 普通优先级 1:low 低优先级
  map<string,string> env =6;//环境变量
  /*
		组的配置, sql类型配置格式如下,key若为select,则select默认会选择该配置,exec:exec执行sql默认会选择这个,执行sql的函数也可以指定连接
		database: map[string]TaskGroupDBConfig
	*/
  string config =7;
}

message TaskSendReq{
  string groupCode =1;//组需要提前创建好
  string code =2;//任务code
  TaskSendOption option = 3;//选项
  TaskParamQueue paramQueue = 4;//消息队列发送类型配置,如果不传则使用数据库定义的
  TaskParamSql paramSql = 5;//数据库执行类型配置,如果不传则使用数据库定义的
  TaskParamScript paramScript = 7;//数据库执行类型配置,如果不传则使用数据库定义的
}
message TaskParamSql{
  string sql =1;//填写脚本内容,如果不填,则会使用数据库中第一次初始化的参数
}

message TaskParamScript{
  string execContent =1;//填写脚本内容,如果不填,则会使用数据库中第一次初始化的参数
  map<string,string> param =2;//脚本参数,会通过函数入参传进去
}

message TaskParamQueue{
  string topic =1;
  string payload =2;
}

message TaskSendOption{
  int64 priority =1; //优先级: 6:critical 最高优先级  3: default 普通优先级 1:low 低优先级
  //以下两个参数优先使用ProcessIn
  int64 processIn =2;  //多久之后发 秒数
  int64 processAt =3;      // 固定时间发 秒时间戳
  int64 timeout =4;    //超时时间 优先使用 秒数
  int64 deadline =5;       //截止时间  秒时间戳
  string taskID =6;//指定taskID
}