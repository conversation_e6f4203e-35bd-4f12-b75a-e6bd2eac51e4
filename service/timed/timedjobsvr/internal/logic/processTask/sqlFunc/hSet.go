package sqlFunc

import (
	"encoding/json"
	"gitee.com/unitedrhino/share/errors"
	"github.com/dop251/goja"
	"github.com/spf13/cast"
)

func (s *SqlFunc) Hset() func(in goja.FunctionCall) goja.Value {
	return func(in goja.FunctionCall) goja.Value {
		if len(in.Arguments) < 3 {
			s.<PERSON><PERSON>("timed.SetFunc.Hset script use err,"+
				"need (key, field, value string),code:%v,script:%v",
				s.Task.Code, s.Task.Script.Param.ExecContent)
			panic(errors.Parameter)
		}
		v := in.Arguments[2].Export()
		value, err := cast.ToStringE(v)
		if err != nil {
			b, err := json.Marshal(v)
			if err != nil {
				return s.vm.ToValue(err)
			}
			value = string(b)
		}
		err = s.SvcCtx.Store.HsetCtx(s.ctx, s.<PERSON>(in.Arguments[0].String()),
			s.<PERSON>(in.Arguments[1].String()), value)
		if err != nil {
			s.<PERSON>rf("timed.SetFunc.Set script Store.HsetCtx err:%v", err)
			return s.vm.ToValue(err)
		}
		return nil
	}

}
