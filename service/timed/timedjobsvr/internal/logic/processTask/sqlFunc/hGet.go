package sqlFunc

import (
	"gitee.com/unitedrhino/share/errors"
	"github.com/dop251/goja"
	"strings"
)

func (s *SqlFunc) Hget() func(in goja.FunctionCall) goja.Value {
	return func(in goja.FunctionCall) goja.Value {
		if len(in.Arguments) < 2 {
			s.<PERSON>rf("timed.SetFunc.Hget script use err,"+
				"need (key, field string),code:%v,script:%v",
				s.Task.Code, s.Task.Script.Param.ExecContent)
			panic(errors.Parameter)
		}
		ret, err := s.SvcCtx.Store.HgetCtx(s.ctx, s.<PERSON>(in.Arguments[0].String()),
			s.<PERSON>(in.Arguments[1].String()))
		if err != nil {
			if strings.Contains(err.Error(), "redis: nil") {
				ret, err = s.SvcCtx.Store.HgetCtx(s.ctx, s.<PERSON>(in.Arguments[0].String()),
					s.<PERSON>WithDay(in.Arguments[1].String(), -1))
			}
			if err != nil && !strings.Contains(err.Error(), "redis: nil") {
				s.Errorf("timed.SetFunc.Set script Store.HgetCtx err:%v", err)
				panic(errors.Database.AddDetail(err))
			}
		}
		return s.vm.ToValue(ret)
	}

}
