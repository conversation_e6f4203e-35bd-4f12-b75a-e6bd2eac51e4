package sqlFunc

import (
	"gitee.com/unitedrhino/share/errors"
	"github.com/dop251/goja"
)

func (s *SqlFunc) Hdel() func(in goja.FunctionCall) goja.Value {
	return func(in goja.FunctionCall) goja.Value {
		if len(in.Arguments) < 2 {
			s.<PERSON><PERSON>("timed.SetFunc.Hdel script use err,"+
				"need ( key, field string),code:%v,script:%v",
				s.Task.Code, s.Task.Script.Param.ExecContent)
			panic(errors.Parameter)
		}
		ret, err := s.SvcCtx.Store.HdelCtx(s.ctx, s.<PERSON>(in.Arguments[0].String()),
			s.<PERSON>(in.Arguments[1].String()))
		if err != nil {
			s.<PERSON>rf("timed.SetFunc.Hdel script Store.HgetCtx err:%v", err)
			panic(errors.Database.AddDetail(err))
		}
		//前一天的也需要删除
		ret, err = s.SvcCtx.Store.HdelCtx(s.ctx, s.<PERSON>(in.Arguments[0].String()),
			s.<PERSON>WithDay(in.Arguments[1].String(), -1))
		if err != nil {
			s.Errorf("timed.SetFunc.Hdel script Store.HgetCtx err:%v", err)
			panic(errors.Database.AddDetail(err))
		}
		return s.vm.ToValue(ret)
	}
}
