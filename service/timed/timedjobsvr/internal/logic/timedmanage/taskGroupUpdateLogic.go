package timedmanagelogic

import (
	"context"
	"github.com/FREEZONEX/Tier0-Backend/service/timed/internal/repo/relationDB"

	"github.com/FREEZONEX/Tier0-Backend/service/timed/timedjobsvr/internal/svc"
	"github.com/FREEZONEX/Tier0-Backend/service/timed/timedjobsvr/pb/timedjob"

	"github.com/zeromicro/go-zero/core/logx"
)

type TaskGroupUpdateLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewTaskGroupUpdateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TaskGroupUpdateLogic {
	return &TaskGroupUpdateLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *TaskGroupUpdateLogic) TaskGroupUpdate(in *timedjob.TaskGroup) (*timedjob.Response, error) {
	repo := relationDB.NewTaskGroupRepo(l.ctx)
	oldPo, err := repo.FindOneByFilter(l.ctx, relationDB.TaskGroupFilter{Codes: []string{in.Code}})
	if err != nil {
		return nil, err
	}
	if in.Name != "" {
		oldPo.Name = in.Name
	}
	if in.Priority != 0 {
		oldPo.Priority = in.Priority
	}
	if in.Env != nil {
		oldPo.Env = in.Env
	}
	if in.Config != "" {
		oldPo.Config = in.Config
	}
	err = repo.Update(l.ctx, oldPo)
	if err != nil {
		return nil, err
	}
	return &timedjob.Response{}, nil
}
