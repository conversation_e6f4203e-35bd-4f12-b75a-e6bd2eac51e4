// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.3.0
// - protoc             v3.19.4
// source: proto/timedjob.proto

package timedjob

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

const (
	TimedManage_TaskGroupCreate_FullMethodName = "/timedjob.TimedManage/TaskGroupCreate"
	TimedManage_TaskGroupUpdate_FullMethodName = "/timedjob.TimedManage/TaskGroupUpdate"
	TimedManage_TaskGroupDelete_FullMethodName = "/timedjob.TimedManage/TaskGroupDelete"
	TimedManage_TaskGroupIndex_FullMethodName  = "/timedjob.TimedManage/TaskGroupIndex"
	TimedManage_TaskGroupRead_FullMethodName   = "/timedjob.TimedManage/TaskGroupRead"
	TimedManage_TaskInfoCreate_FullMethodName  = "/timedjob.TimedManage/TaskInfoCreate"
	TimedManage_TaskInfoUpdate_FullMethodName  = "/timedjob.TimedManage/TaskInfoUpdate"
	TimedManage_TaskInfoDelete_FullMethodName  = "/timedjob.TimedManage/TaskInfoDelete"
	TimedManage_TaskInfoIndex_FullMethodName   = "/timedjob.TimedManage/TaskInfoIndex"
	TimedManage_TaskInfoRead_FullMethodName    = "/timedjob.TimedManage/TaskInfoRead"
	TimedManage_TaskLogIndex_FullMethodName    = "/timedjob.TimedManage/TaskLogIndex"
	TimedManage_TaskSend_FullMethodName        = "/timedjob.TimedManage/TaskSend"
	TimedManage_TaskCancel_FullMethodName      = "/timedjob.TimedManage/TaskCancel"
)

// TimedManageClient is the client API for TimedManage service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TimedManageClient interface {
	TaskGroupCreate(ctx context.Context, in *TaskGroup, opts ...grpc.CallOption) (*Response, error)
	TaskGroupUpdate(ctx context.Context, in *TaskGroup, opts ...grpc.CallOption) (*Response, error)
	TaskGroupDelete(ctx context.Context, in *WithCode, opts ...grpc.CallOption) (*Response, error)
	TaskGroupIndex(ctx context.Context, in *TaskGroupIndexReq, opts ...grpc.CallOption) (*TaskGroupIndexResp, error)
	TaskGroupRead(ctx context.Context, in *WithCode, opts ...grpc.CallOption) (*TaskGroup, error)
	TaskInfoCreate(ctx context.Context, in *TaskInfo, opts ...grpc.CallOption) (*Response, error)
	TaskInfoUpdate(ctx context.Context, in *TaskInfo, opts ...grpc.CallOption) (*Response, error)
	TaskInfoDelete(ctx context.Context, in *WithGroupCode, opts ...grpc.CallOption) (*Response, error)
	TaskInfoIndex(ctx context.Context, in *TaskInfoIndexReq, opts ...grpc.CallOption) (*TaskInfoIndexResp, error)
	TaskInfoRead(ctx context.Context, in *WithGroupCode, opts ...grpc.CallOption) (*TaskInfo, error)
	TaskLogIndex(ctx context.Context, in *TaskLogIndexReq, opts ...grpc.CallOption) (*TaskLogIndexResp, error)
	// 发送延时请求,如果任务不存在,则会自动创建,但是自动创建的需要填写param
	TaskSend(ctx context.Context, in *TaskSendReq, opts ...grpc.CallOption) (*TaskWithTaskID, error)
	TaskCancel(ctx context.Context, in *TaskWithTaskID, opts ...grpc.CallOption) (*Response, error)
}

type timedManageClient struct {
	cc grpc.ClientConnInterface
}

func NewTimedManageClient(cc grpc.ClientConnInterface) TimedManageClient {
	return &timedManageClient{cc}
}

func (c *timedManageClient) TaskGroupCreate(ctx context.Context, in *TaskGroup, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, TimedManage_TaskGroupCreate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timedManageClient) TaskGroupUpdate(ctx context.Context, in *TaskGroup, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, TimedManage_TaskGroupUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timedManageClient) TaskGroupDelete(ctx context.Context, in *WithCode, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, TimedManage_TaskGroupDelete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timedManageClient) TaskGroupIndex(ctx context.Context, in *TaskGroupIndexReq, opts ...grpc.CallOption) (*TaskGroupIndexResp, error) {
	out := new(TaskGroupIndexResp)
	err := c.cc.Invoke(ctx, TimedManage_TaskGroupIndex_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timedManageClient) TaskGroupRead(ctx context.Context, in *WithCode, opts ...grpc.CallOption) (*TaskGroup, error) {
	out := new(TaskGroup)
	err := c.cc.Invoke(ctx, TimedManage_TaskGroupRead_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timedManageClient) TaskInfoCreate(ctx context.Context, in *TaskInfo, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, TimedManage_TaskInfoCreate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timedManageClient) TaskInfoUpdate(ctx context.Context, in *TaskInfo, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, TimedManage_TaskInfoUpdate_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timedManageClient) TaskInfoDelete(ctx context.Context, in *WithGroupCode, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, TimedManage_TaskInfoDelete_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timedManageClient) TaskInfoIndex(ctx context.Context, in *TaskInfoIndexReq, opts ...grpc.CallOption) (*TaskInfoIndexResp, error) {
	out := new(TaskInfoIndexResp)
	err := c.cc.Invoke(ctx, TimedManage_TaskInfoIndex_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timedManageClient) TaskInfoRead(ctx context.Context, in *WithGroupCode, opts ...grpc.CallOption) (*TaskInfo, error) {
	out := new(TaskInfo)
	err := c.cc.Invoke(ctx, TimedManage_TaskInfoRead_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timedManageClient) TaskLogIndex(ctx context.Context, in *TaskLogIndexReq, opts ...grpc.CallOption) (*TaskLogIndexResp, error) {
	out := new(TaskLogIndexResp)
	err := c.cc.Invoke(ctx, TimedManage_TaskLogIndex_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timedManageClient) TaskSend(ctx context.Context, in *TaskSendReq, opts ...grpc.CallOption) (*TaskWithTaskID, error) {
	out := new(TaskWithTaskID)
	err := c.cc.Invoke(ctx, TimedManage_TaskSend_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *timedManageClient) TaskCancel(ctx context.Context, in *TaskWithTaskID, opts ...grpc.CallOption) (*Response, error) {
	out := new(Response)
	err := c.cc.Invoke(ctx, TimedManage_TaskCancel_FullMethodName, in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TimedManageServer is the server API for TimedManage service.
// All implementations must embed UnimplementedTimedManageServer
// for forward compatibility
type TimedManageServer interface {
	TaskGroupCreate(context.Context, *TaskGroup) (*Response, error)
	TaskGroupUpdate(context.Context, *TaskGroup) (*Response, error)
	TaskGroupDelete(context.Context, *WithCode) (*Response, error)
	TaskGroupIndex(context.Context, *TaskGroupIndexReq) (*TaskGroupIndexResp, error)
	TaskGroupRead(context.Context, *WithCode) (*TaskGroup, error)
	TaskInfoCreate(context.Context, *TaskInfo) (*Response, error)
	TaskInfoUpdate(context.Context, *TaskInfo) (*Response, error)
	TaskInfoDelete(context.Context, *WithGroupCode) (*Response, error)
	TaskInfoIndex(context.Context, *TaskInfoIndexReq) (*TaskInfoIndexResp, error)
	TaskInfoRead(context.Context, *WithGroupCode) (*TaskInfo, error)
	TaskLogIndex(context.Context, *TaskLogIndexReq) (*TaskLogIndexResp, error)
	// 发送延时请求,如果任务不存在,则会自动创建,但是自动创建的需要填写param
	TaskSend(context.Context, *TaskSendReq) (*TaskWithTaskID, error)
	TaskCancel(context.Context, *TaskWithTaskID) (*Response, error)
	mustEmbedUnimplementedTimedManageServer()
}

// UnimplementedTimedManageServer must be embedded to have forward compatible implementations.
type UnimplementedTimedManageServer struct {
}

func (UnimplementedTimedManageServer) TaskGroupCreate(context.Context, *TaskGroup) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskGroupCreate not implemented")
}
func (UnimplementedTimedManageServer) TaskGroupUpdate(context.Context, *TaskGroup) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskGroupUpdate not implemented")
}
func (UnimplementedTimedManageServer) TaskGroupDelete(context.Context, *WithCode) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskGroupDelete not implemented")
}
func (UnimplementedTimedManageServer) TaskGroupIndex(context.Context, *TaskGroupIndexReq) (*TaskGroupIndexResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskGroupIndex not implemented")
}
func (UnimplementedTimedManageServer) TaskGroupRead(context.Context, *WithCode) (*TaskGroup, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskGroupRead not implemented")
}
func (UnimplementedTimedManageServer) TaskInfoCreate(context.Context, *TaskInfo) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskInfoCreate not implemented")
}
func (UnimplementedTimedManageServer) TaskInfoUpdate(context.Context, *TaskInfo) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskInfoUpdate not implemented")
}
func (UnimplementedTimedManageServer) TaskInfoDelete(context.Context, *WithGroupCode) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskInfoDelete not implemented")
}
func (UnimplementedTimedManageServer) TaskInfoIndex(context.Context, *TaskInfoIndexReq) (*TaskInfoIndexResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskInfoIndex not implemented")
}
func (UnimplementedTimedManageServer) TaskInfoRead(context.Context, *WithGroupCode) (*TaskInfo, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskInfoRead not implemented")
}
func (UnimplementedTimedManageServer) TaskLogIndex(context.Context, *TaskLogIndexReq) (*TaskLogIndexResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskLogIndex not implemented")
}
func (UnimplementedTimedManageServer) TaskSend(context.Context, *TaskSendReq) (*TaskWithTaskID, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskSend not implemented")
}
func (UnimplementedTimedManageServer) TaskCancel(context.Context, *TaskWithTaskID) (*Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskCancel not implemented")
}
func (UnimplementedTimedManageServer) mustEmbedUnimplementedTimedManageServer() {}

// UnsafeTimedManageServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TimedManageServer will
// result in compilation errors.
type UnsafeTimedManageServer interface {
	mustEmbedUnimplementedTimedManageServer()
}

func RegisterTimedManageServer(s grpc.ServiceRegistrar, srv TimedManageServer) {
	s.RegisterService(&TimedManage_ServiceDesc, srv)
}

func _TimedManage_TaskGroupCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskGroup)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskGroupCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskGroupCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskGroupCreate(ctx, req.(*TaskGroup))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimedManage_TaskGroupUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskGroup)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskGroupUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskGroupUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskGroupUpdate(ctx, req.(*TaskGroup))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimedManage_TaskGroupDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithCode)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskGroupDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskGroupDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskGroupDelete(ctx, req.(*WithCode))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimedManage_TaskGroupIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskGroupIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskGroupIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskGroupIndex_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskGroupIndex(ctx, req.(*TaskGroupIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimedManage_TaskGroupRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithCode)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskGroupRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskGroupRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskGroupRead(ctx, req.(*WithCode))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimedManage_TaskInfoCreate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskInfoCreate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskInfoCreate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskInfoCreate(ctx, req.(*TaskInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimedManage_TaskInfoUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskInfo)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskInfoUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskInfoUpdate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskInfoUpdate(ctx, req.(*TaskInfo))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimedManage_TaskInfoDelete_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithGroupCode)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskInfoDelete(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskInfoDelete_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskInfoDelete(ctx, req.(*WithGroupCode))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimedManage_TaskInfoIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskInfoIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskInfoIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskInfoIndex_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskInfoIndex(ctx, req.(*TaskInfoIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimedManage_TaskInfoRead_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(WithGroupCode)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskInfoRead(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskInfoRead_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskInfoRead(ctx, req.(*WithGroupCode))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimedManage_TaskLogIndex_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskLogIndexReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskLogIndex(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskLogIndex_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskLogIndex(ctx, req.(*TaskLogIndexReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimedManage_TaskSend_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskSendReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskSend(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskSend_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskSend(ctx, req.(*TaskSendReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _TimedManage_TaskCancel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TaskWithTaskID)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TimedManageServer).TaskCancel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TimedManage_TaskCancel_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TimedManageServer).TaskCancel(ctx, req.(*TaskWithTaskID))
	}
	return interceptor(ctx, in, info, handler)
}

// TimedManage_ServiceDesc is the grpc.ServiceDesc for TimedManage service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TimedManage_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "timedjob.TimedManage",
	HandlerType: (*TimedManageServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TaskGroupCreate",
			Handler:    _TimedManage_TaskGroupCreate_Handler,
		},
		{
			MethodName: "TaskGroupUpdate",
			Handler:    _TimedManage_TaskGroupUpdate_Handler,
		},
		{
			MethodName: "TaskGroupDelete",
			Handler:    _TimedManage_TaskGroupDelete_Handler,
		},
		{
			MethodName: "TaskGroupIndex",
			Handler:    _TimedManage_TaskGroupIndex_Handler,
		},
		{
			MethodName: "TaskGroupRead",
			Handler:    _TimedManage_TaskGroupRead_Handler,
		},
		{
			MethodName: "TaskInfoCreate",
			Handler:    _TimedManage_TaskInfoCreate_Handler,
		},
		{
			MethodName: "TaskInfoUpdate",
			Handler:    _TimedManage_TaskInfoUpdate_Handler,
		},
		{
			MethodName: "TaskInfoDelete",
			Handler:    _TimedManage_TaskInfoDelete_Handler,
		},
		{
			MethodName: "TaskInfoIndex",
			Handler:    _TimedManage_TaskInfoIndex_Handler,
		},
		{
			MethodName: "TaskInfoRead",
			Handler:    _TimedManage_TaskInfoRead_Handler,
		},
		{
			MethodName: "TaskLogIndex",
			Handler:    _TimedManage_TaskLogIndex_Handler,
		},
		{
			MethodName: "TaskSend",
			Handler:    _TimedManage_TaskSend_Handler,
		},
		{
			MethodName: "TaskCancel",
			Handler:    _TimedManage_TaskCancel_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "proto/timedjob.proto",
}
