// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.31.0
// 	protoc        v3.19.4
// source: proto/timedjob.proto

package timedjob

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PageInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page int64 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size int64 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	// 排序信息
	Orders []*PageInfo_OrderBy `protobuf:"bytes,3,rep,name=orders,proto3" json:"orders,omitempty"`
}

func (x *PageInfo) Reset() {
	*x = PageInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageInfo) ProtoMessage() {}

func (x *PageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageInfo.ProtoReflect.Descriptor instead.
func (*PageInfo) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{0}
}

func (x *PageInfo) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *PageInfo) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *PageInfo) GetOrders() []*PageInfo_OrderBy {
	if x != nil {
		return x.Orders
	}
	return nil
}

type Response struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *Response) Reset() {
	*x = Response{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{1}
}

type WithCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
}

func (x *WithCode) Reset() {
	*x = WithCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithCode) ProtoMessage() {}

func (x *WithCode) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithCode.ProtoReflect.Descriptor instead.
func (*WithCode) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{2}
}

func (x *WithCode) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type WithGroupCode struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code      string `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	GroupCode string `protobuf:"bytes,2,opt,name=groupCode,proto3" json:"groupCode,omitempty"`
}

func (x *WithGroupCode) Reset() {
	*x = WithGroupCode{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *WithGroupCode) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WithGroupCode) ProtoMessage() {}

func (x *WithGroupCode) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WithGroupCode.ProtoReflect.Descriptor instead.
func (*WithGroupCode) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{3}
}

func (x *WithGroupCode) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *WithGroupCode) GetGroupCode() string {
	if x != nil {
		return x.GroupCode
	}
	return ""
}

type TaskWithTaskID struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskID string `protobuf:"bytes,1,opt,name=taskID,proto3" json:"taskID,omitempty"`
}

func (x *TaskWithTaskID) Reset() {
	*x = TaskWithTaskID{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskWithTaskID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskWithTaskID) ProtoMessage() {}

func (x *TaskWithTaskID) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskWithTaskID.ProtoReflect.Descriptor instead.
func (*TaskWithTaskID) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{4}
}

func (x *TaskWithTaskID) GetTaskID() string {
	if x != nil {
		return x.TaskID
	}
	return ""
}

type TaskLogIndexReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      *PageInfo `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"`           //分页信息 只获取一个则不填
	GroupCode string    `protobuf:"bytes,2,opt,name=groupCode,proto3" json:"groupCode,omitempty"` //组编码
	TaskCode  string    `protobuf:"bytes,5,opt,name=taskCode,proto3" json:"taskCode,omitempty"`   //任务编码
}

func (x *TaskLogIndexReq) Reset() {
	*x = TaskLogIndexReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskLogIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskLogIndexReq) ProtoMessage() {}

func (x *TaskLogIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskLogIndexReq.ProtoReflect.Descriptor instead.
func (*TaskLogIndexReq) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{5}
}

func (x *TaskLogIndexReq) GetPage() *PageInfo {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *TaskLogIndexReq) GetGroupCode() string {
	if x != nil {
		return x.GroupCode
	}
	return ""
}

func (x *TaskLogIndexReq) GetTaskCode() string {
	if x != nil {
		return x.TaskCode
	}
	return ""
}

type TaskLogIndexResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*TaskLog `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total int64      `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"` //总数(只有分页的时候会返回)
}

func (x *TaskLogIndexResp) Reset() {
	*x = TaskLogIndexResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskLogIndexResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskLogIndexResp) ProtoMessage() {}

func (x *TaskLogIndexResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskLogIndexResp.ProtoReflect.Descriptor instead.
func (*TaskLogIndexResp) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{6}
}

func (x *TaskLogIndexResp) GetList() []*TaskLog {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TaskLogIndexResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type TaskLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          int64          `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	GroupCode   string         `protobuf:"bytes,2,opt,name=groupCode,proto3" json:"groupCode,omitempty"`    //组编码
	TaskCode    string         `protobuf:"bytes,3,opt,name=taskCode,proto3" json:"taskCode,omitempty"`      //任务编码
	Params      string         `protobuf:"bytes,4,opt,name=params,proto3" json:"params,omitempty"`          // 任务参数
	ResultCode  int64          `protobuf:"varint,5,opt,name=resultCode,proto3" json:"resultCode,omitempty"` //结果code
	ResultMsg   string         `protobuf:"bytes,6,opt,name=resultMsg,proto3" json:"resultMsg,omitempty"`    //结果消息
	CreatedTime int64          `protobuf:"varint,7,opt,name=createdTime,proto3" json:"createdTime,omitempty"`
	Sql         *TaskLogSql    `protobuf:"bytes,8,opt,name=sql,proto3" json:"sql,omitempty"`
	Script      *TaskLogScript `protobuf:"bytes,9,opt,name=script,proto3" json:"script,omitempty"` //脚本日志
}

func (x *TaskLog) Reset() {
	*x = TaskLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskLog) ProtoMessage() {}

func (x *TaskLog) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskLog.ProtoReflect.Descriptor instead.
func (*TaskLog) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{7}
}

func (x *TaskLog) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *TaskLog) GetGroupCode() string {
	if x != nil {
		return x.GroupCode
	}
	return ""
}

func (x *TaskLog) GetTaskCode() string {
	if x != nil {
		return x.TaskCode
	}
	return ""
}

func (x *TaskLog) GetParams() string {
	if x != nil {
		return x.Params
	}
	return ""
}

func (x *TaskLog) GetResultCode() int64 {
	if x != nil {
		return x.ResultCode
	}
	return 0
}

func (x *TaskLog) GetResultMsg() string {
	if x != nil {
		return x.ResultMsg
	}
	return ""
}

func (x *TaskLog) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

func (x *TaskLog) GetSql() *TaskLogSql {
	if x != nil {
		return x.Sql
	}
	return nil
}

func (x *TaskLog) GetScript() *TaskLogScript {
	if x != nil {
		return x.Script
	}
	return nil
}

type TaskLogSql struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SelectNum int64 `protobuf:"varint,2,opt,name=selectNum,proto3" json:"selectNum,omitempty"` //查询的数量
	ExecNum   int64 `protobuf:"varint,3,opt,name=execNum,proto3" json:"execNum,omitempty"`     //执行的数量
}

func (x *TaskLogSql) Reset() {
	*x = TaskLogSql{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskLogSql) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskLogSql) ProtoMessage() {}

func (x *TaskLogSql) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskLogSql.ProtoReflect.Descriptor instead.
func (*TaskLogSql) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{8}
}

func (x *TaskLogSql) GetSelectNum() int64 {
	if x != nil {
		return x.SelectNum
	}
	return 0
}

func (x *TaskLogSql) GetExecNum() int64 {
	if x != nil {
		return x.ExecNum
	}
	return 0
}

type TaskLogScript struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExecLog []*TaskExecLog `protobuf:"bytes,9,rep,name=execLog,proto3" json:"execLog,omitempty"` //执行日志
}

func (x *TaskLogScript) Reset() {
	*x = TaskLogScript{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskLogScript) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskLogScript) ProtoMessage() {}

func (x *TaskLogScript) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskLogScript.ProtoReflect.Descriptor instead.
func (*TaskLogScript) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{9}
}

func (x *TaskLogScript) GetExecLog() []*TaskExecLog {
	if x != nil {
		return x.ExecLog
	}
	return nil
}

type TaskExecLog struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Level       string `protobuf:"bytes,1,opt,name=level,proto3" json:"level,omitempty"`              //日志级别: info warn error
	Content     string `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`          //日志内容
	CreatedTime int64  `protobuf:"varint,3,opt,name=createdTime,proto3" json:"createdTime,omitempty"` //日志创建时间
}

func (x *TaskExecLog) Reset() {
	*x = TaskExecLog{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskExecLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskExecLog) ProtoMessage() {}

func (x *TaskExecLog) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskExecLog.ProtoReflect.Descriptor instead.
func (*TaskExecLog) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{10}
}

func (x *TaskExecLog) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *TaskExecLog) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *TaskExecLog) GetCreatedTime() int64 {
	if x != nil {
		return x.CreatedTime
	}
	return 0
}

type TaskGroupIndexReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page *PageInfo `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"` //分页信息 只获取一个则不填
}

func (x *TaskGroupIndexReq) Reset() {
	*x = TaskGroupIndexReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskGroupIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskGroupIndexReq) ProtoMessage() {}

func (x *TaskGroupIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskGroupIndexReq.ProtoReflect.Descriptor instead.
func (*TaskGroupIndexReq) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{11}
}

func (x *TaskGroupIndexReq) GetPage() *PageInfo {
	if x != nil {
		return x.Page
	}
	return nil
}

type TaskGroupIndexResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*TaskGroup `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total int64        `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"` //总数(只有分页的时候会返回)
}

func (x *TaskGroupIndexResp) Reset() {
	*x = TaskGroupIndexResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskGroupIndexResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskGroupIndexResp) ProtoMessage() {}

func (x *TaskGroupIndexResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskGroupIndexResp.ProtoReflect.Descriptor instead.
func (*TaskGroupIndexResp) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{12}
}

func (x *TaskGroupIndexResp) GetList() []*TaskGroup {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TaskGroupIndexResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type TaskInfoIndexReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Page      *PageInfo `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty"`           //分页信息 只获取一个则不填
	GroupCode string    `protobuf:"bytes,2,opt,name=groupCode,proto3" json:"groupCode,omitempty"` //组编码
}

func (x *TaskInfoIndexReq) Reset() {
	*x = TaskInfoIndexReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskInfoIndexReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskInfoIndexReq) ProtoMessage() {}

func (x *TaskInfoIndexReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskInfoIndexReq.ProtoReflect.Descriptor instead.
func (*TaskInfoIndexReq) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{13}
}

func (x *TaskInfoIndexReq) GetPage() *PageInfo {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *TaskInfoIndexReq) GetGroupCode() string {
	if x != nil {
		return x.GroupCode
	}
	return ""
}

type TaskInfoIndexResp struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List  []*TaskInfo `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Total int64       `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"` //总数(只有分页的时候会返回)
}

func (x *TaskInfoIndexResp) Reset() {
	*x = TaskInfoIndexResp{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskInfoIndexResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskInfoIndexResp) ProtoMessage() {}

func (x *TaskInfoIndexResp) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskInfoIndexResp.ProtoReflect.Descriptor instead.
func (*TaskInfoIndexResp) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{14}
}

func (x *TaskInfoIndexResp) GetList() []*TaskInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *TaskInfoIndexResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

type TaskInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupCode string   `protobuf:"bytes,2,opt,name=groupCode,proto3" json:"groupCode,omitempty"` //组编码
	Type      int64    `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`          //任务类型 1 定时任务 2 延时任务 3 消息队列触发
	Name      string   `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`           // 任务名称
	Code      string   `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`           //任务编码
	Topics    []string `protobuf:"bytes,1,rep,name=topics,proto3" json:"topics,omitempty"`       //触发topic列表
	Params    string   `protobuf:"bytes,6,opt,name=params,proto3" json:"params,omitempty"`       // 任务参数,延时任务如果没有传任务参数会拿数据库的参数来执行
	CronExpr  string   `protobuf:"bytes,7,opt,name=cronExpr,proto3" json:"cronExpr,omitempty"`   // cron执行表达式
	Status    int64    `protobuf:"varint,8,opt,name=status,proto3" json:"status,omitempty"`      // 状态
	Priority  int64    `protobuf:"varint,9,opt,name=priority,proto3" json:"priority,omitempty"`  //优先级: 10:critical 最高优先级  3: default 普通优先级 1:low 低优先级
}

func (x *TaskInfo) Reset() {
	*x = TaskInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskInfo) ProtoMessage() {}

func (x *TaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskInfo.ProtoReflect.Descriptor instead.
func (*TaskInfo) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{15}
}

func (x *TaskInfo) GetGroupCode() string {
	if x != nil {
		return x.GroupCode
	}
	return ""
}

func (x *TaskInfo) GetType() int64 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *TaskInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaskInfo) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *TaskInfo) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

func (x *TaskInfo) GetParams() string {
	if x != nil {
		return x.Params
	}
	return ""
}

func (x *TaskInfo) GetCronExpr() string {
	if x != nil {
		return x.CronExpr
	}
	return ""
}

func (x *TaskInfo) GetStatus() int64 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *TaskInfo) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

type TaskGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code     string            `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`                                                                                       //任务组编码
	Name     string            `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                                                                       // 组名
	Type     string            `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`                                                                                       //组类型:queue(消息队列消息发送)  sql(执行sql) script(脚本执行) email(邮件发送) http(http请求)
	SubType  string            `protobuf:"bytes,4,opt,name=subType,proto3" json:"subType,omitempty"`                                                                                 //组子类型 natsJs nats                    js
	Priority int64             `protobuf:"varint,5,opt,name=priority,proto3" json:"priority,omitempty"`                                                                              //组优先级: 6:critical 最高优先级  3: default 普通优先级 1:low 低优先级
	Env      map[string]string `protobuf:"bytes,6,rep,name=env,proto3" json:"env,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //环境变量
	// 组的配置, sql类型配置格式如下,key若为select,则select默认会选择该配置,exec:exec执行sql默认会选择这个,执行sql的函数也可以指定连接
	// database: map[string]TaskGroupDBConfig
	Config string `protobuf:"bytes,7,opt,name=config,proto3" json:"config,omitempty"`
}

func (x *TaskGroup) Reset() {
	*x = TaskGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskGroup) ProtoMessage() {}

func (x *TaskGroup) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskGroup.ProtoReflect.Descriptor instead.
func (*TaskGroup) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{16}
}

func (x *TaskGroup) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *TaskGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TaskGroup) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *TaskGroup) GetSubType() string {
	if x != nil {
		return x.SubType
	}
	return ""
}

func (x *TaskGroup) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *TaskGroup) GetEnv() map[string]string {
	if x != nil {
		return x.Env
	}
	return nil
}

func (x *TaskGroup) GetConfig() string {
	if x != nil {
		return x.Config
	}
	return ""
}

type TaskSendReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	GroupCode   string           `protobuf:"bytes,1,opt,name=groupCode,proto3" json:"groupCode,omitempty"`     //组需要提前创建好
	Code        string           `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`               //任务code
	Option      *TaskSendOption  `protobuf:"bytes,3,opt,name=option,proto3" json:"option,omitempty"`           //选项
	ParamQueue  *TaskParamQueue  `protobuf:"bytes,4,opt,name=paramQueue,proto3" json:"paramQueue,omitempty"`   //消息队列发送类型配置,如果不传则使用数据库定义的
	ParamSql    *TaskParamSql    `protobuf:"bytes,5,opt,name=paramSql,proto3" json:"paramSql,omitempty"`       //数据库执行类型配置,如果不传则使用数据库定义的
	ParamScript *TaskParamScript `protobuf:"bytes,7,opt,name=paramScript,proto3" json:"paramScript,omitempty"` //数据库执行类型配置,如果不传则使用数据库定义的
}

func (x *TaskSendReq) Reset() {
	*x = TaskSendReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskSendReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskSendReq) ProtoMessage() {}

func (x *TaskSendReq) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskSendReq.ProtoReflect.Descriptor instead.
func (*TaskSendReq) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{17}
}

func (x *TaskSendReq) GetGroupCode() string {
	if x != nil {
		return x.GroupCode
	}
	return ""
}

func (x *TaskSendReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *TaskSendReq) GetOption() *TaskSendOption {
	if x != nil {
		return x.Option
	}
	return nil
}

func (x *TaskSendReq) GetParamQueue() *TaskParamQueue {
	if x != nil {
		return x.ParamQueue
	}
	return nil
}

func (x *TaskSendReq) GetParamSql() *TaskParamSql {
	if x != nil {
		return x.ParamSql
	}
	return nil
}

func (x *TaskSendReq) GetParamScript() *TaskParamScript {
	if x != nil {
		return x.ParamScript
	}
	return nil
}

type TaskParamSql struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Sql string `protobuf:"bytes,1,opt,name=sql,proto3" json:"sql,omitempty"` //填写脚本内容,如果不填,则会使用数据库中第一次初始化的参数
}

func (x *TaskParamSql) Reset() {
	*x = TaskParamSql{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskParamSql) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskParamSql) ProtoMessage() {}

func (x *TaskParamSql) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskParamSql.ProtoReflect.Descriptor instead.
func (*TaskParamSql) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{18}
}

func (x *TaskParamSql) GetSql() string {
	if x != nil {
		return x.Sql
	}
	return ""
}

type TaskParamScript struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ExecContent string            `protobuf:"bytes,1,opt,name=execContent,proto3" json:"execContent,omitempty"`                                                                             //填写脚本内容,如果不填,则会使用数据库中第一次初始化的参数
	Param       map[string]string `protobuf:"bytes,2,rep,name=param,proto3" json:"param,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"` //脚本参数,会通过函数入参传进去
}

func (x *TaskParamScript) Reset() {
	*x = TaskParamScript{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskParamScript) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskParamScript) ProtoMessage() {}

func (x *TaskParamScript) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskParamScript.ProtoReflect.Descriptor instead.
func (*TaskParamScript) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{19}
}

func (x *TaskParamScript) GetExecContent() string {
	if x != nil {
		return x.ExecContent
	}
	return ""
}

func (x *TaskParamScript) GetParam() map[string]string {
	if x != nil {
		return x.Param
	}
	return nil
}

type TaskParamQueue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Topic   string `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	Payload string `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
}

func (x *TaskParamQueue) Reset() {
	*x = TaskParamQueue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskParamQueue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskParamQueue) ProtoMessage() {}

func (x *TaskParamQueue) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskParamQueue.ProtoReflect.Descriptor instead.
func (*TaskParamQueue) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{20}
}

func (x *TaskParamQueue) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *TaskParamQueue) GetPayload() string {
	if x != nil {
		return x.Payload
	}
	return ""
}

type TaskSendOption struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Priority int64 `protobuf:"varint,1,opt,name=priority,proto3" json:"priority,omitempty"` //优先级: 6:critical 最高优先级  3: default 普通优先级 1:low 低优先级
	// 以下两个参数优先使用ProcessIn
	ProcessIn int64  `protobuf:"varint,2,opt,name=processIn,proto3" json:"processIn,omitempty"` //多久之后发 秒数
	ProcessAt int64  `protobuf:"varint,3,opt,name=processAt,proto3" json:"processAt,omitempty"` // 固定时间发 秒时间戳
	Timeout   int64  `protobuf:"varint,4,opt,name=timeout,proto3" json:"timeout,omitempty"`     //超时时间 优先使用 秒数
	Deadline  int64  `protobuf:"varint,5,opt,name=deadline,proto3" json:"deadline,omitempty"`   //截止时间  秒时间戳
	TaskID    string `protobuf:"bytes,6,opt,name=taskID,proto3" json:"taskID,omitempty"`        //指定taskID
}

func (x *TaskSendOption) Reset() {
	*x = TaskSendOption{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TaskSendOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskSendOption) ProtoMessage() {}

func (x *TaskSendOption) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskSendOption.ProtoReflect.Descriptor instead.
func (*TaskSendOption) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{21}
}

func (x *TaskSendOption) GetPriority() int64 {
	if x != nil {
		return x.Priority
	}
	return 0
}

func (x *TaskSendOption) GetProcessIn() int64 {
	if x != nil {
		return x.ProcessIn
	}
	return 0
}

func (x *TaskSendOption) GetProcessAt() int64 {
	if x != nil {
		return x.ProcessAt
	}
	return 0
}

func (x *TaskSendOption) GetTimeout() int64 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *TaskSendOption) GetDeadline() int64 {
	if x != nil {
		return x.Deadline
	}
	return 0
}

func (x *TaskSendOption) GetTaskID() string {
	if x != nil {
		return x.TaskID
	}
	return ""
}

type PageInfo_OrderBy struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 排序的字段名
	Filed string `protobuf:"bytes,1,opt,name=filed,proto3" json:"filed,omitempty"`
	// 排序方式：0 aes, 1 desc
	Sort int64 `protobuf:"varint,2,opt,name=sort,proto3" json:"sort,omitempty"`
}

func (x *PageInfo_OrderBy) Reset() {
	*x = PageInfo_OrderBy{}
	if protoimpl.UnsafeEnabled {
		mi := &file_proto_timedjob_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PageInfo_OrderBy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PageInfo_OrderBy) ProtoMessage() {}

func (x *PageInfo_OrderBy) ProtoReflect() protoreflect.Message {
	mi := &file_proto_timedjob_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PageInfo_OrderBy.ProtoReflect.Descriptor instead.
func (*PageInfo_OrderBy) Descriptor() ([]byte, []int) {
	return file_proto_timedjob_proto_rawDescGZIP(), []int{0, 0}
}

func (x *PageInfo_OrderBy) GetFiled() string {
	if x != nil {
		return x.Filed
	}
	return ""
}

func (x *PageInfo_OrderBy) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

var File_proto_timedjob_proto protoreflect.FileDescriptor

var file_proto_timedjob_proto_rawDesc = []byte{
	0x0a, 0x14, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x08, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62,
	0x22, 0x9b, 0x01, 0x0a, 0x08, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x32, 0x0a, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62,
	0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x42,
	0x79, 0x52, 0x06, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x73, 0x1a, 0x33, 0x0a, 0x07, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x42, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x6f,
	0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x6f, 0x72, 0x74, 0x22, 0x0a,
	0x0a, 0x08, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1e, 0x0a, 0x08, 0x57, 0x69,
	0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x41, 0x0a, 0x0d, 0x57, 0x69,
	0x74, 0x68, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12,
	0x1c, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x28, 0x0a,
	0x0e, 0x54, 0x61, 0x73, 0x6b, 0x57, 0x69, 0x74, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x44, 0x12,
	0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x44, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x44, 0x22, 0x73, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b, 0x4c,
	0x6f, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64,
	0x6a, 0x6f, 0x62, 0x2e, 0x50, 0x61, 0x67, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x70, 0x61,
	0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x4f, 0x0a, 0x10,
	0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x25, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f,
	0x67, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xa4, 0x02,
	0x0a, 0x07, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x43,
	0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x43,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x12, 0x1e, 0x0a, 0x0a, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x0a, 0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x72,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x73, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x4d, 0x73, 0x67, 0x12, 0x20, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x03, 0x73,
	0x71, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64,
	0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x53, 0x71, 0x6c, 0x52, 0x03,
	0x73, 0x71, 0x6c, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x06, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x22, 0x44, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x53,
	0x71, 0x6c, 0x12, 0x1c, 0x0a, 0x09, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4e, 0x75, 0x6d, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x4e, 0x75, 0x6d,
	0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x65, 0x63, 0x4e, 0x75, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x07, 0x65, 0x78, 0x65, 0x63, 0x4e, 0x75, 0x6d, 0x22, 0x40, 0x0a, 0x0d, 0x54, 0x61,
	0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x2f, 0x0a, 0x07, 0x65,
	0x78, 0x65, 0x63, 0x4c, 0x6f, 0x67, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74,
	0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x78, 0x65, 0x63,
	0x4c, 0x6f, 0x67, 0x52, 0x07, 0x65, 0x78, 0x65, 0x63, 0x4c, 0x6f, 0x67, 0x22, 0x5f, 0x0a, 0x0b,
	0x54, 0x61, 0x73, 0x6b, 0x45, 0x78, 0x65, 0x63, 0x4c, 0x6f, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x6c,
	0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65,
	0x6c, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x3b, 0x0a,
	0x11, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52,
	0x65, 0x71, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x50, 0x61, 0x67, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x22, 0x53, 0x0a, 0x12, 0x54, 0x61,
	0x73, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x73, 0x70,
	0x12, 0x27, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13,
	0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22,
	0x58, 0x0a, 0x10, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x52, 0x65, 0x71, 0x12, 0x26, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x50, 0x61, 0x67,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x22, 0x51, 0x0a, 0x11, 0x54, 0x61, 0x73,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x73, 0x70, 0x12, 0x26,
	0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x74,
	0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x22, 0xe4, 0x01, 0x0a,
	0x08, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x70,
	0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x63, 0x72, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x72, 0x6f, 0x6e, 0x45, 0x78, 0x70, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x22, 0xfd, 0x01, 0x0a, 0x09, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x73, 0x75, 0x62, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x12, 0x2e, 0x0a, 0x03, 0x65, 0x6e, 0x76, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1c, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x45, 0x6e, 0x76, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x03,
	0x65, 0x6e, 0x76, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x36, 0x0a, 0x08, 0x45,
	0x6e, 0x76, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x9c, 0x02, 0x0a, 0x0b, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x65, 0x6e, 0x64,
	0x52, 0x65, 0x71, 0x12, 0x1c, 0x0a, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62,
	0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x65, 0x6e, 0x64, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x06, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x38, 0x0a, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x74, 0x69,
	0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x51, 0x75, 0x65, 0x75, 0x65, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x51, 0x75, 0x65, 0x75,
	0x65, 0x12, 0x32, 0x0a, 0x08, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x53, 0x71, 0x6c, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x53, 0x71, 0x6c, 0x52, 0x08, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x53, 0x71, 0x6c, 0x12, 0x3b, 0x0a, 0x0b, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x74, 0x69, 0x6d,
	0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x53,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x52, 0x0b, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x53, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x22, 0x20, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x53,
	0x71, 0x6c, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x71, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x73, 0x71, 0x6c, 0x22, 0xa9, 0x01, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x53, 0x63, 0x72, 0x69, 0x70, 0x74, 0x12, 0x20, 0x0a, 0x0b, 0x65, 0x78, 0x65, 0x63,
	0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65,
	0x78, 0x65, 0x63, 0x43, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x05, 0x70, 0x61,
	0x72, 0x61, 0x6d, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x74, 0x69, 0x6d, 0x65,
	0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x53, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x05, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x1a, 0x38, 0x0a, 0x0a, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x40, 0x0a, 0x0e, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x51, 0x75, 0x65,
	0x75, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c,
	0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x22, 0xb6, 0x01, 0x0a, 0x0e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x65, 0x6e, 0x64, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72, 0x69, 0x74,
	0x79, 0x12, 0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x49, 0x6e, 0x12,
	0x1c, 0x0a, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x09, 0x70, 0x72, 0x6f, 0x63, 0x65, 0x73, 0x73, 0x41, 0x74, 0x12, 0x18, 0x0a,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x07,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x64, 0x65, 0x61, 0x64, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x08, 0x64, 0x65, 0x61, 0x64, 0x6c,
	0x69, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x44, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x44, 0x32, 0xc1, 0x06, 0x0a, 0x0b,
	0x54, 0x69, 0x6d, 0x65, 0x64, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x12, 0x3a, 0x0a, 0x0f, 0x54,
	0x61, 0x73, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x13,
	0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x1a, 0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x13, 0x2e, 0x74, 0x69, 0x6d,
	0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x1a,
	0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x39, 0x0a, 0x0f, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f,
	0x62, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65, 0x1a, 0x12, 0x2e, 0x74, 0x69, 0x6d,
	0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4b,
	0x0a, 0x0e, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x64, 0x65, 0x78,
	0x12, 0x1b, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x1a, 0x1c, 0x2e,
	0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x73, 0x70, 0x12, 0x38, 0x0a, 0x0d, 0x54,
	0x61, 0x73, 0x6b, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x61, 0x64, 0x12, 0x12, 0x2e, 0x74,
	0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x43, 0x6f, 0x64, 0x65,
	0x1a, 0x13, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x38, 0x0a, 0x0e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66,
	0x6f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a,
	0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x12, 0x2e, 0x74, 0x69,
	0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x38, 0x0a, 0x0e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62,
	0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3d, 0x0a, 0x0e, 0x54, 0x61, 0x73,
	0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0x17, 0x2e, 0x74, 0x69,
	0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x57, 0x69, 0x74, 0x68, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x6f, 0x64, 0x65, 0x1a, 0x12, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x0d, 0x54, 0x61, 0x73, 0x6b,
	0x49, 0x6e, 0x66, 0x6f, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12, 0x1a, 0x2e, 0x74, 0x69, 0x6d, 0x65,
	0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62,
	0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65,
	0x73, 0x70, 0x12, 0x3b, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x61, 0x64, 0x12, 0x17, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x57, 0x69,
	0x74, 0x68, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x64, 0x65, 0x1a, 0x12, 0x2e, 0x74, 0x69,
	0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x45, 0x0a, 0x0c, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x12,
	0x19, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c,
	0x6f, 0x67, 0x49, 0x6e, 0x64, 0x65, 0x78, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x74, 0x69, 0x6d,
	0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x4c, 0x6f, 0x67, 0x49, 0x6e, 0x64,
	0x65, 0x78, 0x52, 0x65, 0x73, 0x70, 0x12, 0x3b, 0x0a, 0x08, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x65,
	0x6e, 0x64, 0x12, 0x15, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61,
	0x73, 0x6b, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x74, 0x69, 0x6d, 0x65,
	0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x57, 0x69, 0x74, 0x68, 0x54, 0x61, 0x73,
	0x6b, 0x49, 0x44, 0x12, 0x3a, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x12, 0x18, 0x2e, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x57, 0x69, 0x74, 0x68, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x44, 0x1a, 0x12, 0x2e, 0x74, 0x69,
	0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x0d, 0x5a, 0x0b, 0x70, 0x62, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x64, 0x6a, 0x6f, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_proto_timedjob_proto_rawDescOnce sync.Once
	file_proto_timedjob_proto_rawDescData = file_proto_timedjob_proto_rawDesc
)

func file_proto_timedjob_proto_rawDescGZIP() []byte {
	file_proto_timedjob_proto_rawDescOnce.Do(func() {
		file_proto_timedjob_proto_rawDescData = protoimpl.X.CompressGZIP(file_proto_timedjob_proto_rawDescData)
	})
	return file_proto_timedjob_proto_rawDescData
}

var file_proto_timedjob_proto_msgTypes = make([]protoimpl.MessageInfo, 25)
var file_proto_timedjob_proto_goTypes = []interface{}{
	(*PageInfo)(nil),           // 0: timedjob.PageInfo
	(*Response)(nil),           // 1: timedjob.Response
	(*WithCode)(nil),           // 2: timedjob.WithCode
	(*WithGroupCode)(nil),      // 3: timedjob.WithGroupCode
	(*TaskWithTaskID)(nil),     // 4: timedjob.TaskWithTaskID
	(*TaskLogIndexReq)(nil),    // 5: timedjob.TaskLogIndexReq
	(*TaskLogIndexResp)(nil),   // 6: timedjob.TaskLogIndexResp
	(*TaskLog)(nil),            // 7: timedjob.TaskLog
	(*TaskLogSql)(nil),         // 8: timedjob.TaskLogSql
	(*TaskLogScript)(nil),      // 9: timedjob.TaskLogScript
	(*TaskExecLog)(nil),        // 10: timedjob.TaskExecLog
	(*TaskGroupIndexReq)(nil),  // 11: timedjob.TaskGroupIndexReq
	(*TaskGroupIndexResp)(nil), // 12: timedjob.TaskGroupIndexResp
	(*TaskInfoIndexReq)(nil),   // 13: timedjob.TaskInfoIndexReq
	(*TaskInfoIndexResp)(nil),  // 14: timedjob.TaskInfoIndexResp
	(*TaskInfo)(nil),           // 15: timedjob.TaskInfo
	(*TaskGroup)(nil),          // 16: timedjob.TaskGroup
	(*TaskSendReq)(nil),        // 17: timedjob.TaskSendReq
	(*TaskParamSql)(nil),       // 18: timedjob.TaskParamSql
	(*TaskParamScript)(nil),    // 19: timedjob.TaskParamScript
	(*TaskParamQueue)(nil),     // 20: timedjob.TaskParamQueue
	(*TaskSendOption)(nil),     // 21: timedjob.TaskSendOption
	(*PageInfo_OrderBy)(nil),   // 22: timedjob.PageInfo.OrderBy
	nil,                        // 23: timedjob.TaskGroup.EnvEntry
	nil,                        // 24: timedjob.TaskParamScript.ParamEntry
}
var file_proto_timedjob_proto_depIdxs = []int32{
	22, // 0: timedjob.PageInfo.orders:type_name -> timedjob.PageInfo.OrderBy
	0,  // 1: timedjob.TaskLogIndexReq.page:type_name -> timedjob.PageInfo
	7,  // 2: timedjob.TaskLogIndexResp.list:type_name -> timedjob.TaskLog
	8,  // 3: timedjob.TaskLog.sql:type_name -> timedjob.TaskLogSql
	9,  // 4: timedjob.TaskLog.script:type_name -> timedjob.TaskLogScript
	10, // 5: timedjob.TaskLogScript.execLog:type_name -> timedjob.TaskExecLog
	0,  // 6: timedjob.TaskGroupIndexReq.page:type_name -> timedjob.PageInfo
	16, // 7: timedjob.TaskGroupIndexResp.list:type_name -> timedjob.TaskGroup
	0,  // 8: timedjob.TaskInfoIndexReq.page:type_name -> timedjob.PageInfo
	15, // 9: timedjob.TaskInfoIndexResp.list:type_name -> timedjob.TaskInfo
	23, // 10: timedjob.TaskGroup.env:type_name -> timedjob.TaskGroup.EnvEntry
	21, // 11: timedjob.TaskSendReq.option:type_name -> timedjob.TaskSendOption
	20, // 12: timedjob.TaskSendReq.paramQueue:type_name -> timedjob.TaskParamQueue
	18, // 13: timedjob.TaskSendReq.paramSql:type_name -> timedjob.TaskParamSql
	19, // 14: timedjob.TaskSendReq.paramScript:type_name -> timedjob.TaskParamScript
	24, // 15: timedjob.TaskParamScript.param:type_name -> timedjob.TaskParamScript.ParamEntry
	16, // 16: timedjob.TimedManage.TaskGroupCreate:input_type -> timedjob.TaskGroup
	16, // 17: timedjob.TimedManage.TaskGroupUpdate:input_type -> timedjob.TaskGroup
	2,  // 18: timedjob.TimedManage.TaskGroupDelete:input_type -> timedjob.WithCode
	11, // 19: timedjob.TimedManage.TaskGroupIndex:input_type -> timedjob.TaskGroupIndexReq
	2,  // 20: timedjob.TimedManage.TaskGroupRead:input_type -> timedjob.WithCode
	15, // 21: timedjob.TimedManage.TaskInfoCreate:input_type -> timedjob.TaskInfo
	15, // 22: timedjob.TimedManage.TaskInfoUpdate:input_type -> timedjob.TaskInfo
	3,  // 23: timedjob.TimedManage.TaskInfoDelete:input_type -> timedjob.WithGroupCode
	13, // 24: timedjob.TimedManage.TaskInfoIndex:input_type -> timedjob.TaskInfoIndexReq
	3,  // 25: timedjob.TimedManage.TaskInfoRead:input_type -> timedjob.WithGroupCode
	5,  // 26: timedjob.TimedManage.TaskLogIndex:input_type -> timedjob.TaskLogIndexReq
	17, // 27: timedjob.TimedManage.TaskSend:input_type -> timedjob.TaskSendReq
	4,  // 28: timedjob.TimedManage.TaskCancel:input_type -> timedjob.TaskWithTaskID
	1,  // 29: timedjob.TimedManage.TaskGroupCreate:output_type -> timedjob.Response
	1,  // 30: timedjob.TimedManage.TaskGroupUpdate:output_type -> timedjob.Response
	1,  // 31: timedjob.TimedManage.TaskGroupDelete:output_type -> timedjob.Response
	12, // 32: timedjob.TimedManage.TaskGroupIndex:output_type -> timedjob.TaskGroupIndexResp
	16, // 33: timedjob.TimedManage.TaskGroupRead:output_type -> timedjob.TaskGroup
	1,  // 34: timedjob.TimedManage.TaskInfoCreate:output_type -> timedjob.Response
	1,  // 35: timedjob.TimedManage.TaskInfoUpdate:output_type -> timedjob.Response
	1,  // 36: timedjob.TimedManage.TaskInfoDelete:output_type -> timedjob.Response
	14, // 37: timedjob.TimedManage.TaskInfoIndex:output_type -> timedjob.TaskInfoIndexResp
	15, // 38: timedjob.TimedManage.TaskInfoRead:output_type -> timedjob.TaskInfo
	6,  // 39: timedjob.TimedManage.TaskLogIndex:output_type -> timedjob.TaskLogIndexResp
	4,  // 40: timedjob.TimedManage.TaskSend:output_type -> timedjob.TaskWithTaskID
	1,  // 41: timedjob.TimedManage.TaskCancel:output_type -> timedjob.Response
	29, // [29:42] is the sub-list for method output_type
	16, // [16:29] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_proto_timedjob_proto_init() }
func file_proto_timedjob_proto_init() {
	if File_proto_timedjob_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_proto_timedjob_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Response); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*WithGroupCode); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskWithTaskID); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskLogIndexReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskLogIndexResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskLogSql); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskLogScript); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskExecLog); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskGroupIndexReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskGroupIndexResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskInfoIndexReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskInfoIndexResp); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskSendReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskParamSql); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskParamScript); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskParamQueue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TaskSendOption); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_proto_timedjob_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PageInfo_OrderBy); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_proto_timedjob_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   25,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_proto_timedjob_proto_goTypes,
		DependencyIndexes: file_proto_timedjob_proto_depIdxs,
		MessageInfos:      file_proto_timedjob_proto_msgTypes,
	}.Build()
	File_proto_timedjob_proto = out.File
	file_proto_timedjob_proto_rawDesc = nil
	file_proto_timedjob_proto_goTypes = nil
	file_proto_timedjob_proto_depIdxs = nil
}
