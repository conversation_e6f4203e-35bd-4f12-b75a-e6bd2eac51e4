# supOS后端服务


# 后端服务端口范围
9540-9639


# 测试环境信息

## 1panel 信息
[1Panel Log]: 外部地址:  http://13.212.91.247:9640/b1061a2cfc
[1Panel Log]: 内部地址:  http://192.168.1.139:9640/b1061a2cfc
[1Panel Log]: 面板用户:  40ddf274d3
[1Panel Log]: 面板密码:  bd78ab4a3b


## 服务器信息

服务器 13.212.91.247
用户名 ubuntu
密码 Supos@1304


## 开发规范
### 数据库规范
#### 字段规范
1. 主键 统一用 xxxID 进行命名
   a. 例如 有一张 dm_project_info表，则主键ID 应命名为 projectID
   b. 例如 有一张 dm_project_area_info表，则主键ID 应命名为 projectAreaID
   c. 如果 dm_project_area_info表 有一个 dm_project_info表 的外键 ，则外键命名为 projectID
2. 主键ID 一般采用 雪花算法生成，除非没有业务含义的表（例如 接口日志表 的主键 logID）
   a. 雪花算法参考代码：ithings/src/dmsvr/internal/svc/serviceContext.go:59

### 代码规范
#### 命名规范
##### 参数命名规范
1. 变量常量及函数名 使用小驼峰法命名,需要外部使用的使用大驼峰法命名即可
2. 如果是id这类有特殊含义的,那么如果i需要大写则命名为ID,也就说id只能为id或者ID
   文件命名规范
   使用小驼峰法命名
   接口定义规范
1. 尽量通用化
2. 修改接口需要支持修改单个变量或具有修改单个变量的能力
3. 数字类型需要避免将0具有定义
4. 不使用bool值,通过使用数字来定义,建议使用1为true,2为false
   输出日志规范
   用于定位日志位置可以使用 utils.FuncName(),如果不够可以用 .进行连接 后面填上需要打印的参数,参数之间用空格连接即可
   以下示例:
   s.Infof("%s topic:%v payload:%v", utils.FuncName(), topic, string(payload))
   l.Infof("%s.GetPropertyDataByID not find id:%s", utils.FuncName(), id)

##### 提交备注
* feat: 新功能
* upper: 功能增强
* fix: bug修复
* doc: 文档修改
* test: 测试代码

#### 分支规范
采取 mtr 分支模型：
一个功能，就是一个 feat 分支。命令规范为 feat-xxx。(举个🌰： feat-user)

开发及代码提交步骤：
1. 本地开发
2. 代码提交前的代码检查(已配置git hooks, 代码提交前会自动检查)
3.  代码检查通过
4. 特性分支代码提交pr到 dev 分支
5. 告知团队其他成员代码已提交，请帮忙 code review
6. code review 通过
7. 合并到 dev 分支
8. 测试环境验证完毕
9. 合并到 main 分支