nodered.protocol.unsupported=\u534F\u8BAE\u4E0D\u652F\u6301: {0}
nodered.template.read.error=\u8BFB\u53D6\u6A21\u7248\u6587\u4EF6\u5F02\u5E38
nodered.file.read.error=\u6587\u4EF6\u8BFB\u53D6\u5F02\u5E38
nodered.flow.exist=\u6D41\u7A0B\u5DF2\u7ECF\u5B58\u5728
nodered.flow.not.exist=\u6D41\u7A0B\u4E0D\u5B58\u5728
nodered.flowName.duplicate=\u6D41\u7A0B\u540D\u79F0\u4E0D\u80FD\u91CD\u590D
nodered.flowId.not.exist=\u4E0A\u4E0B\u6587\u73AF\u5883\u7F3A\u5931\u6D41\u7A0BID, \u8BF7\u91CD\u65B0\u8FDB\u5165\u6D41\u7A0B\u7F16\u8F91\u5668
nodered.flowName.has.used=\u6D41\u7A0B\u540D\u79F0\u5DF2\u88AB\u4F7F\u7528
nodered.flowId.empty=\u6D41\u7A0BID\u4E0D\u80FD\u4E3A\u7A7A
nodered.invalid.parameter=\u53C2\u6570\u4E0D\u5408\u6CD5
nodered.upload.invalid.format=\u4E0A\u4F20\u6587\u4EF6\u683C\u5F0F\u9519\u8BEF
nodered.app.not.exist=app\u4E0D\u5B58\u5728
nodered.app.exist=app\u5DF2\u5B58\u5728
nodered.app.html.not.exist=html\u6587\u4EF6\u4E0D\u5B58\u5728
nodered.app.not.specified=\u672A\u6307\u5B9A\u8981\u5220\u9664\u7684app
nodered.parameter.name.not.empty=\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
nodered.parameter.name.reg.invalid=app\u540D\u79F0\u53EA\u652F\u6301\u82F1\u6587\u3001\u6570\u5B57\u3001\u4E0B\u6ED1\u7EBF
nodered.server.config.not.exist=\u81EA\u5B9A\u4E49\u534F\u8BAE\u5BFC\u5165\u7F3A\u5C11server\u914D\u7F6E
nodered.protocol.has.exist=\u534F\u8BAE\u5DF2\u5B58\u5728
nodered.protocol.import.size.min=\u4E0A\u4F20\u534F\u8BAE\u914D\u7F6E\u9700\u5305\u542Bclient\u548Cserver\u7684json\u914D\u7F6E
nodered.protocol.import.size.max=\u534F\u8BAE\u8282\u70B9\u4E0D\u5E94\u591A\u4E8E2\u4E2A
nodered.protocol.not.exist=\u534F\u8BAE\u4E0D\u5B58\u5728
nodered.protocol.modbus.server.empty=modbus server \u5B57\u6BB5\u4E3A\u7A7A
nodered.server.exist=\u540D\u79F0\u5DF2\u7ECF\u5B58\u5728

system.error=\u7CFB\u7EDF\u5F02\u5E38
common.timestamp=\u65F6\u95F4\u6233
common.quality=\u8D28\u91CF\u7801
common.collector=\u91C7\u96C6\u5668
common.pagesize=pageSize\u5FC5\u987B\u5927\u4E8E0
common.pageno=pageNo\u5FC5\u987B\u5927\u4E8E0

uns.dataType.1=\u65F6\u5E8F\u7C7B\u578B
uns.dataType.2=\u5173\u7CFB\u7C7B\u578B
uns.dataType.3=\u5B9E\u65F6\u8BA1\u7B97
uns.dataType.4=\u6D41\u5F0F\u8BA1\u7B97
uns.dataType.5=\u544A\u8B66\u7C7B\u578B
uns.dataType.6=\u5408\u5E76\u7C7B\u578B
uns.dataType.7=\u5F15\u7528\u7C7B\u578B
uns.ref.invalid.dataType=UNS {0} \u7C7B\u578B\u4E3A {1}, \u4E0D\u80FD\u5F15\u7528 {2}

uns.type.0=\u76EE\u5F55
uns.type.1=\u6A21\u677F
uns.type.2=\u6587\u4EF6
uns.file.not.exist=\u6587\u4EF6\u4E0D\u5B58\u5728
uns.import.not.xlsx=\u5BFC\u5165\u5931\u8D25\uFF0C\u6587\u4EF6\u7C7B\u578B\u4E0D\u662Fxlsx\u6216json
uns.import.template.error=\u5BFC\u5165\u5931\u8D25\uFF0C\u6587\u4EF6\u6A21\u677F\u9519\u8BEF
uns.import.excel.empty=\u5BFC\u5165\u5931\u8D25\uFF0C\u5BFC\u5165\u7684excel\u6570\u636E\u4E3A\u7A7A
uns.import.error=\u5BFC\u5165\u5F02\u5E38
uns.import.rs.ok=\u5168\u90E8\u6210\u529F
uns.import.rs.hasErr=\u90E8\u5206\u5931\u8D25
uns.import.head.error=\u5BFC\u5165\u7684[{0}]\u7684\u8868\u5934\u4E0E\u6A21\u677F\u8868\u5934\u4E0D\u4E00\u81F4\uFF0C\u8BF7\u4E0B\u8F7D\u6700\u65B0\u7684\u6A21\u677F\u7528\u4E8E\u5BFC\u5165
uns.import.json.error=\u5BFC\u5165\u5931\u8D25\uFF0Cjson\u683C\u5F0F\u6709\u8BEF
uns.export.error=\u5BFC\u51FA\u5F02\u5E38
uns.alias.empty=\u522B\u540D\u4E0D\u80FD\u4E3A\u7A7A
uns.alias.has.exist=\u522B\u540D\u5DF2\u5B58\u5728
uns.alias.has.exist.type=\u522B\u540D\u5DF2\u5B58\u5728{0}\uFF0C\u4E14\u4E0D\u4E3A {1}
uns.alias.duplicate=\u5B58\u5728\u522B\u540D\u91CD\u590D\u7684\u6570\u636E
uns.instance.has.exist=\u6587\u4EF6\u5DF2\u5B58\u5728
uns.file.dataType.empty=\u6587\u4EF6\u7684\u6570\u636E\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
uns.file.dataType.invalid=\u6587\u4EF6\u7684\u6570\u636E\u7C7B\u578B\u4E0D\u5408\u6CD5:{0}
uns.file.dataType.change=\u6587\u4EF6\u7684\u6570\u636E\u7C7B\u578B\u4E0D\u80FD\u4FEE\u6539
uns.folder.or.file.not.found=\u6587\u4EF6\u5939\u6216\u6587\u4EF6\u4E0D\u5B58\u5728
uns.pathType.empty=\u8DEF\u5F84\u7C7B\u578B\u4E3A\u7A7A
uns.pathType.invalid=\u8DEF\u5F84\u7C7B\u578B\u4E0D\u5408\u6CD5: {0}
uns.file.not.found=\u627E\u4E0D\u5230\u6587\u4EF6
uns.folder.has.exist=\u76EE\u5F55\u5DF2\u5B58\u5728
uns.folder.not.found=\u76EE\u5F55\u4E0D\u5B58\u5728
uns.folder.empty=\u76EE\u5F55\u522B\u540D\u4E0D\u80FD\u4E3A\u7A7A
uns.folder.parent.not.found=\u4E0A\u7EA7\u76EE\u5F55\u4E0D\u5B58\u5728
uns.model.has.exist=\u6587\u4EF6\u5939\u5DF2\u5B58\u5728
uns.model.not.found=\u627E\u4E0D\u5230\u6587\u4EF6\u5939
uns.stream.not.found=Stream not found
uns.field.empty=\u5B57\u6BB5\u4E0D\u80FD\u4E3A\u7A7A
uns.field.tooLong=\u5B57\u6BB5\u540D\u8D85\u8FC7\u6700\u5927\u957F\u5EA6: 63
uns.field.duplicate=\u5B57\u6BB5\u540D\u91CD\u590D: {0}
uns.field.startWith.limit.underline=\u5B57\u6BB5\u540D\u4E0D\u5141\u8BB8\u4EE5 _ \u5F00\u5934: {0}
uns.field.startWith.limit.number=\u5B57\u6BB5\u540D\u4E0D\u5141\u8BB8\u4EE5 \u6570\u5B57\u5F00\u5934: {0}
uns.field.name.format.invalid=\u5B57\u6BB5\u540D\u4EC5\u652F\u6301\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\uFF08_\uFF09\uFF0C\u4E0D\u80FD\u4EE5\u4E0B\u5212\u7EBF\uFF08_\uFF09\u548C\u6570\u5B57\u5F00\u5934: {0}
uns.field.keyword=\u5B57\u6BB5\u540D\u4E0D\u5141\u8BB8\u662F\u4FDD\u7559\u5173\u952E\u5B57: {0}
uns.field.type.must.be.datetime=ct \u7C7B\u578B\u5FC5\u987B\u662F datetime
uns.field.type.must.be.long=id \u7C7B\u578B\u5FC5\u987B\u662F long
uns.field.type.empty=\u5B57\u6BB5\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
uns.field.type.invalid=\u65E0\u6548\u7684\u6570\u636E\u7C7B\u578B:{0}
uns.field.definition.empty=\u6587\u4EF6\u5939\u5B57\u6BB5\u5B9A\u4E49\u4E0D\u80FD\u4E3A\u7A7A
uns.field.not.found=\u5B57\u6BB5\u4E0D\u5B58\u5728
uns.field.type.un.match=\u5B57\u6BB5\u7C7B\u578B\u4E0D\u5339\u914D
uns.field.value.out.of.size=\u5B57\u6BB5\u503C\u8D85\u51FA\u6700\u5927\u957F\u5EA6\u9650\u5236
uns.fieldsIsEmpty=\u5F53\u4F7F\u7528 {0} \u65F6 fields[] \u4E3A\u7A7A
uns.rest.data404=\u4ECERestApi\u4E2D\u627E\u4E0D\u5230\u6570\u636E\u7ED3\u6784
uns.fieldsIndexAllEmpty=\u5F53\u4F7F\u7528 {0} fields \u7684 index\u5B57\u6BB5\u5FC5\u586B
uns.refer.idAndAliasEmpty=\u5F15\u7528\u7684id\u548Calias\u4E0D\u80FD\u90FD\u4E3A\u7A7A
uns.refer.path.empty=\u5F15\u7528\u7684path\u4E0D\u80FD\u4E3A\u7A7A
uns.refer.path.noexist=\u5F15\u7528\u7684path\u4E0D\u5B58\u5728
uns.refer.alias.noexist=\u5F15\u7528\u7684alias\u4E0D\u5B58\u5728
uns.refer.datatype.invalid=\u4EC5\u652F\u6301\u5BF9\u65F6\u5E8F\u578B\u6570\u636E\u6216\u5173\u7CFB\u578B\u6570\u636E\u7684\u5F15\u7528
uns.field.frequency.empty=\u6587\u4EF6\u8BA1\u7B97\u5468\u671F\u4E0D\u80FD\u4E3A\u7A7A
uns.field.frequency.invalid=\u6587\u4EF6\u8BA1\u7B97\u5468\u671F\u65E0\u6548
uns.topic.data.type.empty=\u6570\u636E\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
uns.topic.calc.expression.empty=\u8868\u8FBE\u5F0F\u4E0D\u80FD\u4E3A\u7A7A
uns.topic.calc.expression.invalid=\u8868\u8FBE\u5F0F\u8BED\u6CD5\u9519\u8BEF:{0}
uns.topic.calc.expression.func.invalid=\u4E0D\u652F\u6301\u7684\u51FD\u6570: {0}
uns.topic.calc.expression.fields.empty=\u6CA1\u6709\u5F15\u7528\u4EFB\u4F55\u5B9E\u4F8B\u5B57\u6BB5
uns.topic.calc.expression.fields.ref.invalid=\u53D8\u91CF\u683C\u5F0F\u975E\u6CD5: {0}
uns.topic.calc.expression.fields.ref.notFound=\u627E\u4E0D\u5230\u5F15\u7528\u5B57\u6BB5: {0}.{1}
uns.topic.calc.expression.fields.ref.invalidType=\u975E\u6CD5\u7684\u5F15\u7528\u5B57\u6BB5\u7C7B\u578B: {0}.{1} {2}
uns.topic.calc.expression.topic.ref.notFound=\u627E\u4E0D\u5230\u5F15\u7528\u5B9E\u4F8B: {0}
uns.topic.calc.expression.fields.ref.indexOutOfBounds=\u5B57\u6BB5\u5F15\u7528\u4E0B\u6807 {0} \u8D85\u51FA\u8FB9\u754C {1}
uns.topic.data.type.invalid=\u65E0\u6548\u7684\u6570\u636E\u7C7B\u578B: {0}
uns.topic.length.limit.exceed=path\u8D85\u957F\uFF0C\u4E0D\u80FD\u8D85\u8FC7190\u4E2A\u5B57\u7B26
uns.topic.length.limit.short=topic\u592A\u77ED
uns.topic.must.startWith.slash=\u8DEF\u5F84\u5FC5\u987B\u4EE5'/' \u5F00\u5934
uns.topic.format.invalid=Topic \u683C\u5F0F\u4E0D\u5408\u6CD5\uFF0C\u4EC5\u652F\u6301\u4E2D\u6587\u3001\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\uFF08_\uFF09\u3001\u8FDE\u5B57\u7B26\uFF08-\uFF09\u548C\u659C\u6760\uFF08/\uFF09\uFF0C\u4E0D\u80FD\u4EE5\u659C\u6760\uFF08/\uFF09\u5F00\u5934
uns.topic.empty=path\u4E3A\u7A7A
uns.name.empty=name\u4E3A\u7A7A
uns.folder.format.invalid=\u6587\u4EF6\u5939\u540D\u4EC5\u652F\u6301\u4E2D\u6587\u3001\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\uFF08_\uFF09\u3001\u8FDE\u5B57\u7B26\uFF08-\uFF09\u548C\u659C\u6760\uFF08/\uFF09\uFF0C\u4E0D\u80FD\u4EE5\u659C\u6760\uFF08/\uFF09\u5F00\u5934
uns.folder.length.limit.exceed=\u6587\u4EF6\u5939\u540D\u8D85\u957F\uFF0C\u4E0D\u80FD\u8D85\u8FC7{0}\u4E2A\u5B57\u7B26
uns.alias.length.limit.exceed=\u522B\u540D\u8D85\u957F\uFF0C\u4E0D\u80FD\u8D85\u8FC7{0}\u4E2A\u5B57\u7B26
uns.alias.format.invalid=\u65E0\u6548\u7684\u522B\u540D\u683C\u5F0F\uFF0C\u4EC5\u652F\u6301\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\uFF08_\uFF09\uFF0C\u4E0D\u80FD\u4EE5\u6570\u5B57\u5F00\u5934
uns.excel.fileNameEmpty=Excel \u6587\u4EF6\u540D\u4E3A\u7A7A
uns.excel.cellValue=\u65B0\u6570\u636E
uns.excel.autoFlow.invalid=autoFlow\u7684\u503C\u5FC5\u987B\u662Ftrue\u6216false\uFF0C\u4E0D\u586B\u9ED8\u8BA4false
uns.excel.autoDashboard.invalid=autoDashboard\u7684\u503C\u5FC5\u987B\u662Ftrue\u6216false\uFF0C\u4E0D\u586B\u9ED8\u8BA4false
uns.excel.persistence.invalid=persistence\u7684\u503C\u5FC5\u987B\u662Ftrue\u6216false\uFF0C\u4E0D\u586B\u9ED8\u8BA4false
uns.excel.duplicate.item=\u91CD\u590D\u9879\u76F4\u63A5\u8DF3\u8FC7\uFF1A{0}
uns.excel.explanation.template.alias=Template alias \u552F\u4E00\u6807\u8BC6\u4E00\u4E2A\u6A21\u677F\uFF0C\u4E0D\u5141\u8BB8\u91CD\u590D\u63D0\u4EA4\uFF0Calias\u672A\u586B\u65F6\uFF0C\u5E73\u53F0\u5C06\u81EA\u52A8\u751F\u6210\u3002
uns.excel.explanation.folder.path=Folder path \u786E\u5B9A\u6587\u4EF6\u5939\u7684\u5C42\u7EA7\u5173\u7CFB\uFF0C\u5C42\u7EA7\u6700\u540E\u4E00\u4E2A\u65E2\u662F\u8BE5\u6587\u4EF6\u5939\u7684\u539F\u59CB\u540D\u79F0\u3002\u901A\u8FC7path\u6765\u6807\u8BC6\u4E00\u4E2A\u6587\u4EF6\u5939\u662F\u4E0D\u53EF\u9760\u7684\uFF0C\u5F53alias\u672A\u586B\uFF0C\u4E14\u5E73\u53F0\u5B58\u5728\u540C\u540Dpath\u7684\u6587\u4EF6\u5939\u65F6\uFF0C\u5C06\u521B\u5EFA\u65B0\u7684\u6587\u4EF6\u5939\uFF0C\u8BE5\u6587\u4EF6\u5939path\u540E\u9762\u5C06\u6DFB\u52A0\u6570\u5B57\u7528\u4E8E\u533A\u5206\u3002
uns.excel.explanation.folder.alias=Folder alias \u552F\u4E00\u6807\u8BC6\u4E00\u4E2A\u6587\u4EF6\u5939\uFF0C\u4E0D\u5141\u8BB8\u91CD\u590D\u63D0\u4EA4\uFF0Calias\u672A\u586B\u65F6\uFF0C\u5E73\u53F0\u5C06\u81EA\u52A8\u751F\u6210\u3002
uns.excel.explanation.folder.templatealias=Folder templateAlias \u6587\u4EF6\u5939\u5173\u8054\u7684\u6A21\u677F\u7684alias\uFF0C\u5FC5\u987B\u5728\u5E73\u53F0\u6216\u5BFC\u5165\u6587\u4EF6\u4E2D\u80FD\u5B9A\u4F4D\u5230\u5BF9\u5E94\u7684\u6A21\u677F\u3002
uns.excel.explanation.file.alias=File alias \u552F\u4E00\u6807\u8BC6\u4E00\u4E2A\u6587\u4EF6\uFF0C\u4E0D\u5141\u8BB8\u91CD\u590D\u63D0\u4EA4\uFF0Calias\u672A\u586B\u65F6\uFF0C\u5E73\u53F0\u5C06\u81EA\u52A8\u751F\u6210\u3002
uns.excel.explanation.file.templatealias=File templateAlias \u6587\u4EF6\u5173\u8054\u7684\u6A21\u677F\u7684alias\uFF0C\u5FC5\u987B\u5728\u5E73\u53F0\u6216\u5BFC\u5165\u6587\u4EF6\u4E2D\u80FD\u5B9A\u4F4D\u5230\u5BF9\u5E94\u7684\u6A21\u677F\u3002
uns.excel.explanation.file.refers=File refers \u6587\u4EF6\u9700\u8981\u7528\u5230\u7684\u5176\u5B83\u6587\u4EF6\u7684\u6807\u8BC6\uFF0C\u5176\u4E2Dalias\u6216\u8005path\u9700\u8981\u5728\u5E73\u53F0\u6216\u5BFC\u5165\u6587\u4EF6\u4E2D\u80FD\u5B9A\u4F4D\u5230\u5BF9\u5E94\u7684\u6587\u4EF6\u3002
uns.excel.explanation.file.expression=File expression \u8BA1\u7B97\u7C7B\u578B\u6587\u4EF6\u4F7F\u7528\u7684\u8868\u8FBE\u5F0F\uFF0C\u914D\u5408refers\u8BA1\u7B97\u5F97\u5230\u6587\u4EF6\u7684\u503C\uFF0C\u5FC5\u987B\u91C7\u7528a1\u3001a2\u3001a3...\u5F62\u5F0F\u547D\u540D\uFF0C\u5176\u4E2Da1\u5BF9\u5E94refers\u4E2D\u7B2C\u4E00\u4E2Apath\u5BF9\u5E94\u7684\u6587\u4EF6\uFF0Ca2\u5BF9\u5E94refers\u4E2D\u7B2C\u4E8C\u4E2Apath\u5BF9\u5E94\u7684\u6587\u4EF6\uFF0C\u4F9D\u6B21\u7C7B\u63A8\u3002
uns.excel.explanation.file.frequency=File frequency \u805A\u5408\u7C7B\u578B\u6587\u4EF6\u4F7F\u7528\u7684\u805A\u5408\u9891\u7387\uFF0C\u4EE5h\uFF08\u5C0F\u65F6\uFF09\u3001m\uFF08\u5206\u949F\uFF09\u3001s\uFF08\u79D2\uFF09\u7ED3\u5C3E\u3002
uns.excel.explanation.file.label=File label \u6587\u4EF6\u7ED1\u5B9A\u7684\u6807\u7B7E\uFF0C\u5FC5\u987B\u5728\u5E73\u53F0\u6216\u5BFC\u5165\u6587\u4EF6\u4E2D\u80FD\u5B9A\u4F4D\u5230\u5BF9\u5E94\u7684\u6807\u7B7E\u3002
uns.fieldsIsEmptyAt=topic: {0}, fields\u4E3A\u7A7A
uns.fsAndTopicIsEmpty=fields\u548Ctopic\u5747\u4E3A\u7A7A
uns.stream.name.duplicate=stream \u540D\u79F0\u5DF2\u7ECF\u5B58\u5728
uns.stream.name.hasBlank=stream \u540D\u79F0\u5305\u542B\u7A7A\u683C
uns.stream.sql.invalid=stream sql\u683C\u5F0F\u4E0D\u6B63\u786E
uns.stream.invalid.datetime=\u4E0D\u652F\u6301\u7684\u65E5\u671F\u683C\u5F0F: {0}
uns.create.status.running=\u542F\u52A8
uns.create.status.finished=\u505C\u6B62
uns.create.status.error=\u6267\u884C\u9519\u8BEF
uns.create.empty.frequency=frequency \u4E0D\u80FD\u4E3A\u7A7A
uns.update.field.tips1=\u8BE5\u6A21\u578B\u5DF2\u88AB\u6D41\u7A0B\u5F15\u7528\uFF0C\u662F\u5426\u7EE7\u7EED\uFF1F
uns.update.field.tips2=\u8BE5\u6A21\u578B\u5DF2\u88AB\u8BA1\u7B97\u5B9E\u4F8B\u5F15\u7528\uFF0C\u662F\u5426\u7EE7\u7EED\uFF1F
uns.update.field.tips4=\u8BE5\u6A21\u578B\u5DF2\u88AB\u544A\u8B66\u5F15\u7528\uFF0C\u662F\u5426\u7EE7\u7EED\uFF1F
uns.update.field.tips3=\u8BE5\u6A21\u578B\u5DF2\u88AB\u6D41\u7A0B\u548C\u8BA1\u7B97\u5B9E\u4F8B\u5F15\u7528\uFF0C\u662F\u5426\u7EE7\u7EED\uFF1F
uns.update.field.tips5=\u8BE5\u6A21\u578B\u5DF2\u88AB\u6D41\u7A0B\u548C\u544A\u8B66\u5F15\u7528\uFF0C\u662F\u5426\u7EE7\u7EED\uFF1F
uns.update.field.tips6=\u8BE5\u6A21\u578B\u5DF2\u88AB\u544A\u8B66\u548C\u8BA1\u7B97\u5B9E\u4F8B\u5F15\u7528\uFF0C\u662F\u5426\u7EE7\u7EED\uFF1F
uns.update.field.tips7=\u8BE5\u6A21\u578B\u5DF2\u88AB\u544A\u8B66\u3001\u8BA1\u7B97\u5B9E\u4F8B\u4EE5\u53CA\u6D41\u7A0B\u5F15\u7528\uFF0C\u662F\u5426\u7EE7\u7EED\uFF1F
uns.restapi.call.error=Rest Api\u63A5\u53E3\u65E0\u6CD5\u6B63\u5E38\u8BBF\u95EE\uFF0C\u8BF7\u786E\u8BA4\u540E\u518D\u6B21\u5C1D\u8BD5
uns.create.extend.invalid=\u6269\u5C55\u5B57\u6BB5\u4E0D\u7B26\u5408\u8981\u6C42

uns.window.interval.empty=\u65F6\u95F4\u7A97\u53E3\u7684\u95F4\u9694\u4E0D\u80FD\u4E3A\u7A7A
uns.window.interval.tooSmall=\u65F6\u95F4\u7A97\u53E3\u81F3\u5C11\u4E3A1\u79D2
uns.hist.limit.min=limit \u5FC5\u987B\u4E0D\u5C0F\u4E8E 1
uns.hist.limit.max=limit \u5FC5\u987B\u4E0D\u5927\u4E8E 10000
uns.hist.offset.min=offset \u5FC5\u987B\u4E0D\u5C0F\u4E8E 0
uns.hist.select.empty=select \u4E0D\u80FD\u4E3A\u7A7A
uns.hist.select.table.empty=\u67E5\u8BE2\u7684\u8868\u4E0D\u80FD\u4E3A\u7A7A
uns.hist.select.column.empty=\u67E5\u8BE2\u7684\u5B57\u6BB5\u4E0D\u80FD\u4E3A\u7A7A
uns.hist.where.empty=where \u6761\u4EF6\u4E0D\u80FD\u4E3A\u7A7A
uns.hist.where.name.empty=where \u5B57\u6BB5\u540D \u4E0D\u80FD\u4E3A\u7A7A
uns.hist.where.op.empty=where \u64CD\u4F5C \u4E0D\u80FD\u4E3A\u7A7A
uns.hist.where.value.empty=where \u503C \u4E0D\u80FD\u4E3A\u7A7A
uns.hist.groupBy.empty=\u4F7F\u7528\u805A\u5408\u67E5\u8BE2\u65F6,groupBy \u4E0D\u80FD\u4E3A\u7A7A

uns.create.task.name.init=\u521D\u59CB\u5316
uns.create.task.name.excel=\u5904\u7406 Excel
uns.create.task.name.flow=\u521B\u5EFA\u6D41\u7A0B
uns.create.task.name.dashboard=\u521B\u5EFA\u770B\u677F
uns.create.task.name.mqtt=MQTT \u8BA2\u9605
uns.create.task.name.hasura=Hasura track

uns.alarm.rule.already.exists=\u62A5\u8B66\u540D\u79F0\u5DF2\u5B58\u5728
uns.alarm.rule.update.failed=\u62A5\u8B66\u89C4\u5219\u66F4\u65B0\u5931\u8D25
uns.alarm.rule.not.exist=\u62A5\u8B66\u89C4\u5219\u4E0D\u5B58\u5728
uns.alarm.confirm.failed=\u62A5\u8B66\u8BBE\u7F6E\u786E\u8BA4\u5931\u8D25
uns.alarm.emptyName=\u62A5\u8B66\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
uns.alarm.name.size=\u62A5\u8B66\u540D\u79F0\u6700\u591A63\u4E2A\u5B57\u7B26
uns.alarm.description.size=\u62A5\u8B66\u63CF\u8FF0\u6700\u591A255\u4E2A\u5B57\u7B26
uns.template.name.not.empty=\u6A21\u677F\u540D\u79F0\u4E0D\u53EF\u4E3A\u7A7A
uns.template.name.already.exists=\u6A21\u677F\u540D\u79F0\u5DF2\u5B58\u5728
uns.template.alias.already.exists=\u6A21\u677F\u522B\u540D\u5DF2\u5B58\u5728
uns.template.not.exists=\u6A21\u677F\u4E0D\u5B58\u5728
uns.template.not=\u64CD\u4F5C\u5BF9\u8C61\u4E0D\u662F\u4E00\u4E2AUNS\u6A21\u677F \u6216\u76EE\u5F55
uns.template.name.length=\u6A21\u677F\u540D\u79F0\u6700\u591A63\u4E2A\u5B57\u7B26

uns.pride.json.parser.error=Pride JSON\u89E3\u6790\u9519\u8BEF: {0}

app.secret.key.null.error=\u5F53\u524D\u5BC6\u94A5\u4E3A\u7A7A\uFF0C\u4E0D\u652F\u6301\u5220\u9664\u64CD\u4F5C
app.secret.key.status.error=\u5F53\u524D\u5BC6\u94A5\u7684\u72B6\u6001\u4E0D\u662F\u7981\u7528\u72B6\u6001\uFF0C\u4E0D\u652F\u6301\u5220\u9664\u64CD\u4F5C
app.secret.emptyKey=\u5BC6\u94A5key\u4E0D\u5141\u8BB8\u4E3A\u7A7A
app.secret.key.three.error=\u6700\u591A\u53EA\u5141\u8BB8\u65B0\u589E3\u4E2A\u5BC6\u94A5
app.secret.key.duplicate.error=\u5BC6\u94A5key\u5DF2\u5B58\u5728,\u4E0D\u652F\u6301\u65B0\u589E
app.secret.key.not.exist=\u5F53\u524D\u5BC6\u94A5\u4E0D\u5B58\u5728

app.menu.appId.null=APP ID\u4E0D\u53EF\u4E3A\u7A7A
app.menu.appName.null=APP\u540D\u79F0\u4E0D\u53EF\u4E3A\u7A7A
app.menu.menuUrl.null=APP\u83DC\u5355\u5730\u5740\u4E0D\u53EF\u4E3A\u7A7A

Logs=\u65E5\u5FD7\u7BA1\u7406
CICD=CICD
CollectionGatewayManagement=\u91C7\u96C6\u5668\u7F51\u5173\u7BA1\u7406
Alert=\u62A5\u8B66\u7BA1\u7406
LowCodeTool=\u4F4E\u4EE3\u7801\u5DE5\u5177
StreamProcessing=\u6D41\u8BA1\u7B97
GenApps=AI \u5E94\u7528\u96C6
MqttBroker=MqttBroker
EventFlow=\u4E8B\u4EF6\u6D41\u7A0B
Namespace=\u6570\u636E\u5EFA\u6A21
graphQL=GraphQL
RoutingManagement=\u8DEF\u7531\u914D\u7F6E
apm=APM
SourceFlow=\u6570\u636E\u8FDE\u63A5
GenerativeUI=AI \u5F00\u53D1\u5DE5\u5177
objectStorageServer=\u6587\u4EF6\u5B58\u50A8
grafana=Grafana
Home=\u9996\u9875
Authentication=\u8EAB\u4EFD\u4E0E\u8BBF\u95EE\u7BA1\u7406
Dashboards=\u6570\u636E\u770B\u677F
UserManagement=\u7528\u6237\u7BA1\u7406
AboutUs=\u5173\u4E8E\u6211\u4EEC
ContainerManagement=\u5BB9\u5668\u7BA1\u7406
devtools=\u5DE5\u5177\u96C6
settings=\u7CFB\u7EDF\u7BA1\u7406
AdvancedUse=\u9AD8\u9636\u4F7F\u7528
DBConnect=\u6570\u636E\u6E90\u8FDE\u63A5
SQLEditor=SQL\u7F16\u8F91\u5668
McpClient=MCP \u5BA2\u6237\u7AEF
CodeManagement=\u7CFB\u7EDF\u7F16\u7801\u7BA1\u7406
AppManagement=App\u7BA1\u7406
NotificationManagement=\u6D88\u606F\u914D\u7F6E
open-api-docs=open-api-docs
WebHooks=WebHook
OpenData=\u7EDF\u4E00\u6570\u636E\u5F00\u653E
uns=\u6570\u636E\u7BA1\u7406
apps=\u5E94\u7528\u96C6
PluginManagement=\u63D2\u4EF6\u7BA1\u7406

menu.tag.uns=\u6570\u636E\u7BA1\u7406
menu.tag.devtools=\u5DE5\u5177\u96C6
menu.tag.appspace=\u5E94\u7528\u96C6
menu.tag.system=\u7CFB\u7EDF\u7BA1\u7406

menu.desc.logs=\u65E5\u5FD7\u6536\u96C6\u548C\u7BA1\u7406\uFF0C\u7528\u4E8E\u6536\u96C6\u5E73\u53F0\u548C\u7B2C\u4E09\u65B9APP \u7684\u5E94\u7528\u65E5\u5FD7\u5E76\u63D0\u4F9B\u5206\u6790\u670D\u52A1\u548C\u770B\u677F\u5C55\u793A
menu.desc.cicd=\u63D0\u4F9B\u8F7B\u91CF\u7684CICD \u5DE5\u5177\u96C6\uFF0C\u652F\u6301\u4EE3\u7801\u6258\u7BA1\u548C\u6301\u7EED\u6784\u5EFA\u3002
menu.desc.genApps=AI \u5E94\u7528\u96C6\u5408\uFF0C\u901A\u8FC7AIGC \u5DE5\u5177\u751F\u6210APPs\u96C6\u5408\u3002
menu.desc.mqttBroker=\u63D0\u4F9B\u652F\u6301MQTT\u534F\u8BAE\u7684\u6D88\u606F\u4E2D\u95F4\u4EF6\u3002
menu.desc.dataModeling=\u63D0\u4F9B\u7EDF\u4E00\u7684\u6570\u636E\u6A21\u578B\u7BA1\u7406\uFF0C\u8FDE\u63A5OT\u3001IT \u7C7B\u6570\u636E\u4EE5\u5B9E\u73B0\u5B9E\u65F6\u5DE5\u4E1A\u6570\u636E\u96C6\u6210\u3002
menu.desc.graphQL=GraphQL\u63D0\u4F9B\u66F4\u7075\u6D3B\u3001\u66F4\u9AD8\u6548\u7684\u6570\u636E\u83B7\u53D6\u65B9\u5F0F\u3002
menu.desc.konga=\u63D0\u4F9B\u4E86\u56FE\u5F62\u5316\u754C\u9762\uFF0C\u4F7F\u5F97\u8DEF\u7531\u7684\u914D\u7F6E\u548C\u7BA1\u7406\u53D8\u5F97\u66F4\u52A0\u7B80\u5355\u76F4\u89C2
menu.desc.apm=\u76D1\u63A7\u5E94\u7528\u7A0B\u5E8F\u7684\u5065\u5EB7\u72B6\u51B5\u548C\u6027\u80FD\uFF0C\u786E\u4FDD\u5E73\u7A33\u8FD0\u884C\u5E76\u5FEB\u901F\u89E3\u51B3\u95EE\u9898\u3002
menu.desc.nodered.flow=\u63D0\u4F9B\u6570\u636E\u8FDE\u63A5\u548C\u91C7\u96C6\uFF0C\u652F\u6301\u5404\u7C7B\u6570\u636E\u91C7\u96C6\u534F\u8BAE
menu.desc.generativeUI=\u901A\u8FC7\u5927\u6A21\u578B\u7684\u80FD\u529B\uFF0C\u652F\u6301APP \u7684\u4EA4\u4E92\u5F0F\u5F00\u53D1
menu.desc.objectStorageServer=\u63D0\u4F9B\u6587\u4EF6\u5B58\u50A8\u548C\u7BA1\u7406\u529F\u80FD
menu.desc.grafana=\u63D0\u4F9B\u6570\u636E\u770B\u677F\u7684\u96C6\u5408\uFF0C\u652F\u6301\u6570\u636E\u7248\u672C\u7684\u5F00\u53D1\u548C\u5C55\u793A\u3002
menu.desc.home=\u63D0\u4F9B\u4EA7\u54C1\u6982\u8FF0\u548C\u4F7F\u7528\u4ECB\u7ECD\uFF0C\u5FEB\u901F\u7684\u65B9\u4FBF\u7684\u5B9A\u4F4D\u5230\u5177\u4F53\u7684\u529F\u80FD\u70B9\u3002
menu.desc.keycloak=\u8EAB\u4EFD\u548C\u8BBF\u95EE\u7BA1\u7406\uFF08Identity and Access Management\uFF0C\u7B80\u79F0IAM\uFF09
menu.desc.dashboards=\u63D0\u4F9B\u6570\u636E\u770B\u677F\u7684\u96C6\u5408\uFF0C\u652F\u6301\u6570\u636E\u7248\u672C\u7684\u5F00\u53D1\u548C\u5C55\u793A\u3002
menu.desc.account=\u63D0\u4F9B\u57FA\u672C\u7528\u6237\u7BA1\u7406\u529F\u80FD
menu.desc.dockerMgmt=\u63D0\u4F9B\u5BB9\u5668\u7EB3\u7BA1\u548C\u5BB9\u5668\u7BA1\u7406\u3002
menu.desc.aboutus=\u5173\u4E8E\u4EA7\u54C1\u5F00\u53D1\u7406\u5FF5\u548C\u5F00\u6E90\u7B56\u7565\uFF0C\u4EE5\u53CA\u4F9D\u8D56\u7684\u5F00\u6E90\u7EC4\u4EF6\u6E05\u5355\u8BF4\u660E\u3002
menu.desc.alert=\u63D0\u4F9B\u9488\u5BF9\u5B9E\u65F6\u6570\u636E\u7684\u62A5\u8B66\u89C4\u5219\u914D\u7F6E\u548C\u62A5\u8B66\u901A\u77E5\u7BA1\u7406\u3002
menu.desc.advanceUse=\u5F00\u653E\u4E86\u8F6F\u4EF6\u7684\u5168\u90E8\u6743\u9650\uFF0C\u9488\u5BF9\u5F00\u53D1\u8005\u66F4\u53CB\u597D\uFF01
menu.desc.dbconnect=\u63D0\u4F9B\u5BF9\u5185\u7F6E\u548C\u7B2C\u4E09\u65B9\u6570\u636E\u5E93\u94FE\u63A5\u7BA1\u7406\uFF0C\u652F\u6301\u591A\u79CD\u7C7B\u578B\u7684\u6570\u636E\u5E93\uFF0C\u5305\u62EC\uFF1A\u5173\u7CFB\u3001\u6570\u4ED3\u3001\u5185\u5B58\u7B49\u7C7B\u578B\u7684\u6570\u636E\u5E93\u3002
menu.desc.sqledit=\u7528\u4E8E\u7F16\u5199\u3001\u7F16\u8F91\u548C\u6267\u884C SQL \u8BED\u53E5\u7684\u5DE5\u5177\uFF0C\u5B83\u63D0\u4F9B\u4E86\u76F4\u89C2\u7684\u754C\u9762\u548C\u4E30\u5BCC\u7684\u529F\u80FD\uFF0C\u5E2E\u52A9\u6570\u636E\u5E93\u7BA1\u7406\u5458\u548C\u5F00\u53D1\u4EBA\u5458\u9AD8\u6548\u5730\u8FDB\u884C\u6570\u636E\u5E93\u67E5\u8BE2\u3001\u66F4\u65B0\u3001\u7BA1\u7406\u548C\u4F18\u5316\u7B49\u64CD\u4F5C\u3002
menu.desc.eventflow=\u4E00\u4E2A\u57FA\u4E8E Node-RED \u7684\u4E8B\u4EF6\u7F16\u6392\u7CFB\u7EDF\u901A\u8FC7\u8FDE\u63A5\u548C\u81EA\u52A8\u5316\u4E0D\u540C\u7684\u6570\u636E\u6E90\u548C\u64CD\u4F5C\u5B9E\u73B0\u65E0\u7F1D\u7684\u4E8B\u4EF6\u9A71\u52A8\u5DE5\u4F5C\u6D41\u3002
menu.desc.mcpclient=\u8FDE\u63A5\u5927\u578B\u8BED\u8A00\u6A21\u578B\u4E0E MCP \u670D\u52A1\u5668\u7684\u6865\u6881\u5B9E\u73B0\u65E0\u7F1D\u7684\u5DE5\u5177\u8C03\u7528\u548C\u6570\u636E\u8BBF\u95EE\u589E\u5F3A AI \u7684\u667A\u80FD\u6027\u548C\u4E0A\u4E0B\u6587\u611F\u77E5\u80FD\u529B\u3002
menu.desc.collectionGatewayManagement=\u96C6\u4E2D\u7BA1\u7406\u91C7\u96C6\u5668\u4E0E\u7F51\u5173\u8BBE\u5907\u4E4B\u95F4\u7684\u901A\u4FE1\u3001\u6570\u636E\u4F20\u8F93\u548C\u8BBE\u5907\u63A7\u5236\uFF0C\u4EE5\u786E\u4FDD\u6570\u636E\u91C7\u96C6\u548C\u4F20\u8F93\u7684\u9AD8\u6548\u6027\u548C\u7A33\u5B9A\u6027\u3002
menu.desc.CodeManagement=\u63D0\u4F9B\u7CFB\u7EDF\u7684\u7EDF\u4E00\u7F16\u7801\u7EF4\u62A4\u548C\u5173\u7CFB\uFF0C\u652F\u6301\u6309\u6A21\u5757\u5206\u7EC4\uFF0C\u652F\u6301\u591A\u79CD\u7F16\u7801\u503C\u7BA1\u7406\u3002
menu.desc.AppManagement=\u5B9E\u73B0\u5BF9APP \u7684\u7EDF\u4E00\u7BA1\u7406\uFF0C\u652F\u6301APP \u7684\u72B6\u6001\u67E5\u770B\u548C\u64CD\u4F5C\u7EF4\u62A4
menu.desc.NotificationManagement=\u652F\u6301\u5BF9\u6D88\u606F\u6A21\u7248\u548C\u901A\u77E5\u65B9\u5F0F\u7EF4\u62A4\u7BA1\u7406\uFF0C\u652F\u6301\u591A\u79CD\u7C7B\u578B\u7684\u901A\u77E5\uFF0C\u5305\u62EC\uFF1A\u9489\u9489\u3001\u4F01\u4E1A\u5FAE\u4FE1\u7B49
menu.desc.Webhooks=Webhook\u662F\u4E00\u79CD\u56DE\u8C03\u673A\u5236\uFF0C\u5F53\u4E8B\u4EF6\u53D1\u751F\u65F6\u5411\u76EE\u6807\u5E94\u7528\u7A0B\u5E8F\u53D1\u9001\u5B9E\u65F6HTTP\u8BF7\u6C42\uFF0C\u7528\u4E8E\u5B9E\u65F6\u6570\u636E\u4F20\u8F93\u548C\u7CFB\u7EDF\u96C6\u6210\u3002
menu.desc.OpenData=\u5E73\u53F0\u7EDF\u4E00\u6570\u636E\u5F00\u653E\uFF0C\u652F\u6301APPkey \u7BA1\u7406\u548C\u4E0D\u540C\u7C7B\u578B\u7684\u6570\u636E\u8BA2\u9605\u548C\u8C03\u7528\u8BF4\u660E\u3002
menu.desc.PluginManagement=\u652F\u6301\u5BF9\u63D2\u4EF6\u8FDB\u884C\u5B89\u88C5\u3001\u66F4\u65B0\u3001\u542F\u7528\u3001\u7981\u7528\u3001\u5378\u8F7D\u7B49\u4E00\u7CFB\u5217\u64CD\u4F5C\uFF0C\u4EE5\u5B9E\u73B0\u7075\u6D3B\u62D3\u5C55\u548C\u4F18\u5316\u7684\u7BA1\u7406\u3002
menu.desc.PRIDE=\u9488\u5BF9PRIDE \u4EA7\u54C1\u5B9A\u5411\u63D2\u4EF6\u5305\uFF0C\u652F\u6301PRIDE \u7684\u5B9A\u5236\u4E1A\u52A1\uFF01

menu.servicename.invalid=\u65E0\u6548\u7684\u670D\u52A1\u540D\u79F0\uFF0C\u4EC5\u652F\u6301\u82F1\u6587\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\u548C\u6A2A\u6760\u7EC4\u5408\uFF0C\u6700\u591A64\u4E2A\u5B57\u7B26
menu.name.null=\u83DC\u5355\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
menu.name.invalid=\u65E0\u6548\u7684\u83DC\u5355\u540D\u79F0\uFF0C\u4EC5\u652F\u6301\u82F1\u6587\u3001\u4E0B\u5212\u7EBF\uFF0C\u6700\u591A64\u4E2A\u5B57\u7B26
menu.showname.null=\u83DC\u5355\u663E\u793A\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
menu.showname.length=\u83DC\u5355\u663E\u793A\u540D\u79F0\u6700\u591A64\u4E2A\u5B57\u7B26
menu.showname.exist=\u83DC\u5355\u663E\u793A\u540D\u79F0\u5DF2\u5B58\u5728
menu.description.length=\u83DC\u5355\u63CF\u8FF0\u6700\u591A512\u4E2A\u5B57\u7B26
menu.baseurl.length=baseUrl\u6700\u591A1024\u4E2A\u5B57\u7B26
menu.baseurl.invalid=\u65E0\u6548\u7684baseUrl
menu.baseurl.dns.invalid=baseUrl\u7684host\u65E0\u6CD5\u901A\u8FC7DNS\u89E3\u6790
menu.opentype.invalid=\u65E0\u6548\u7684openType
menu.icon.invalid=\u56FE\u6807\u6587\u4EF6\u6269\u5C55\u540D\u5FC5\u987B\u4E3Asvg
menu.icon.save.failed=\u56FE\u6807\u6587\u4EF6\u4FDD\u5B58\u5931\u8D25
menu.save.failed=\u83DC\u5355\u4FDD\u5B58\u5931\u8D25

uns.create.task.name.pg=Postgresql \u5EFA\u8868
uns.create.task.name.tmsc=TimescaleDB \u5EFA\u8868
uns.create.task.name.td=TdEngine \u5EFA\u8868
uns.create.task.name.uns=\u4FDD\u5B58 UNS
uns.create.task.name.final=\u6700\u7EC8\u68C0\u67E5

user.role.supAdmin=\u8D85\u7EA7\u7BA1\u7406\u5458
user.role.admin=\u7BA1\u7406\u5458
user.role.normalUser=\u666E\u901A\u7528\u6237
user.not.login=\u7528\u6237\u672A\u767B\u5F55
user.create.already.exists=\u7528\u6237\u5DF2\u5B58\u5728
user.create.failed=\u521B\u5EFA\u7528\u6237\u5931\u8D25
user.set.password.failed=\u8BBE\u7F6E\u5BC6\u7801\u5931\u8D25
user.update.failed=\u66F4\u65B0\u7528\u6237\u4FE1\u606F\u5931\u8D25
user.login.password.error=\u5BC6\u7801\u9519\u8BEF
user.username.null=\u7528\u6237\u540D\u4E0D\u80FD\u4E3A\u7A7A
user.username.invalid=\u65E0\u6548\u7684\u7528\u6237\u540D\uFF0C\u4EC5\u652F\u6301\u4E2D\u6587\u3001\u82F1\u6587\u3001\u6570\u5B57\u548C\u90E8\u5206\u7279\u6B8A\u7B26\u53F7_-.@&+\uFF0C\u6700\u5C113\u4E2A\u5B57\u7B26\uFF0C\u6700\u591A200\u4E2A\u5B57\u7B26
user.firstName.invalid=\u65E0\u6548\u7684\u5C55\u793A\u540D\uFF0C\u4EC5\u652F\u6301\u4E2D\u6587\u3001\u82F1\u6587\u3001\u6570\u5B57\u548C\u90E8\u5206\u7279\u6B8A\u7B26\u53F7_-.@&+\uFF0C\u6700\u5C113\u4E2A\u5B57\u7B26\uFF0C\u6700\u591A200\u4E2A\u5B57\u7B26
user.password.null=\u5BC6\u7801\u4E0D\u80FD\u4E3A\u7A7A
user.not.exist=\u7528\u6237\u4E0D\u5B58\u5728
user.email.already.exists=\u7528\u6237\u90AE\u7BB1\u5DF2\u5B58\u5728
user.supos.delete=\u9ED8\u8BA4\u7528\u6237supos\u4E0D\u80FD\u5220\u9664
user.password.invalid=\u5BC6\u7801\u53EA\u652F\u6301\u82F1\u6587\u5927\u5C0F\u5199\u3001\u6570\u5B57\u3001\u7279\u6B8A\u5B57\u7B263-10\u4F4D

uns.dashboard.not.exit=\u4EEA\u8868\u76D8\u4E0D\u5B58\u5728\uFF0C\u53EF\u80FD\u5DF2\u88AB\u5220\u9664
uns.dashboard.create.failed=\u4EEA\u8868\u76D8\u521B\u5EFA\u5931\u8D25
uns.dashboard.name.duplicate=\u4EEA\u8868\u76D8\u540D\u79F0\u91CD\u590D

# javax.validation.ConstraintValidator ??
uns.invalid.emptyFields=\u6570\u636E\u6E90 fields \u4E0D\u80FD\u4E3A\u7A7A
uns.invalid.emptyFieldName=\u5B57\u6BB5 name \u4E0D\u80FD\u4E3A\u7A7A
uns.invalid.emptyFieldType=columnType \u4E0D\u80FD\u4E3A\u7A7A
uns.invalid.json=JSON\u683C\u5F0F\u9519\u8BEF
uns.invalid.type=\u6570\u636E\u7C7B\u578B\u4E0D\u5408\u6CD5: {0}
uns.invalid.toLong=\u5B57\u6BB5\u503C\u8D85\u51FA\u5141\u8BB8\u8303\u56F4: {0}
uns.invalid.field.type=\u65E0\u6548\u7684\u5B57\u6BB5\u7C7B\u578B
uns.invalid.url=URL \u683C\u5F0F\u9519\u8BEF
uns.invalid.topic=Topic \u683C\u5F0F\u4E0D\u5408\u6CD5\uFF0C\u4EC5\u652F\u6301\u4E2D\u6587\u3001\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\uFF08_\uFF09\u3001\u8FDE\u5B57\u7B26\uFF08-\uFF09\u548C\u659C\u6760\uFF08/\uFF09\uFF0C\u4E0D\u80FD\u4EE5\u659C\u6760\uFF08/\uFF09\u5F00\u5934
uns.invalid.alias=\u65E0\u6548\u7684\u522B\u540D\u683C\u5F0F\uFF0C\u4EC5\u652F\u6301\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\uFF08_\uFF09\uFF0C\u4E0D\u80FD\u4EE5\u6570\u5B57\u5F00\u5934
uns.invalid.alias.empty=\u522B\u540D\u4E0D\u80FD\u4E3A\u7A7A
uns.invalid.stream.trigger=\u672A\u77E5\u7684\u89E6\u53D1\u7C7B\u578B: {0}
uns.invalid.stream.minDelay=\u6D41\u5EF6\u8FDF\u65F6\u95F4\u5FC5\u987B\u5927\u4E8E5\u79D2
uns.invalid.stream.invalidCondition=\u6761\u4EF6\u683C\u5F0F\u6709\u8BEF: {0}
uns.invalid.stream.NotBoolCondition=\u6761\u4EF6\u8868\u8FBE\u5F0F\u7ED3\u679C\u5FC5\u987B\u662F\u5E03\u5C14\u7C7B\u578B: {0}
uns.invalid.stream.time=\u65E0\u6548\u7684\u65F6\u95F4\u683C\u5F0F: {0},\u652F\u6301\u7684\u683C\u5F0F\u5F62\u5982: 2025-05-21T06:00:00Z
uns.invalid.stream.window=\u7A97\u53E3\u53C2\u6570\u5F02\u5E38
uns.invalid.stream.window.type=\u672A\u77E5\u7A97\u53E3\u7C7B\u578B: {0}
uns.invalid.stream.window.emptyOptions=\u6CA1\u6709\u4EFB\u4F55\u7A97\u53E3\u9009\u9879
uns.invalid.stream.window.emptyStreamOptions=\u6D41\u8BA1\u7B97\u7684 streamOptions \u4E0D\u80FD\u4E3A\u7A7A
uns.invalid.stream.window.slidingGtCount=\u6ED1\u52A8\u503C\u4E0D\u80FD\u5927\u4E8E 
uns.invalid.stream.window.slidingLt1=\u6ED1\u52A8\u503C\u5FC5\u987B\u5927\u4E8E0
uns.invalid.stream.window.countWindow.waterMarkEmpty=CountWindow \u7684 Watermark \u5FC5\u987B\u5927\u4E8E0
uns.invalid.stream.window.intervalGtOffset=\u95F4\u9694\u504F\u79FB\u4E0D\u80FD\u5927\u4E8E\u95F4\u9694\u503C
uns.invalid.stream.state.colType=STATE_WINDOW \u5F15\u7528\u7684\u5B57\u6BB5\u53EA\u652F\u6301\u8FD9\u4E9B\u7C7B\u578B\uFF1A int/long/boolean/string: {0}({1})
uns.invalid.stream.index.invalid=index \u9700\u8981\u5305\u542B\u4E00\u4E2A\u7EDF\u8BA1\u51FD\u6570
uns.invalid.stream.func.invalid=\u4E0D\u652F\u6301\u7684\u51FD\u6570:{0}
uns.invalid.stream.empty.referTopic=refers \u4E0D\u80FD\u4E3A\u7A7A

# TopologyLog
uns.topology.mqtt.init=MQTT\u670D\u52A1\u8FDE\u63A5\u5931\u8D25
uns.topology.mqtt.consume=\u62C9\u53D6MQTT\u6D88\u606F\u5931\u8D25
uns.topology.mqtt.parse=\u89E3\u6790MQTT\u6D88\u606F\u5931\u8D25
uns.topology.db.pg=\u5173\u7CFB\u6570\u636E\u5165\u5E93\u5931\u8D25
uns.topology.db.td=\u65F6\u5E8F\u6570\u636E\u5165\u5E93\u5931\u8D25

# Attachment
uns.attachment.max.size=\u4E0A\u4F20\u6587\u4EF6\u4E0D\u80FD\u8D85\u8FC710M
uns.attachment.not.exit=\u6587\u4EF6\u5DF2\u4E0D\u5B58\u5728

uns.label.not.exists=\u6807\u7B7E\u4E0D\u5B58\u5728
uns.label.already.exists=\u6807\u7B7E\u5DF2\u5B58\u5728
uns.label.name.error=\u6807\u7B7E\u540D\u79F0\u5B58\u5728\u7279\u6B8A\u7B26\u53F7
uns.label.length.limit.exceed=\u6807\u7B7E\u8D85\u957F\uFF0C\u4E0D\u80FD\u8D85\u8FC7{0}\u4E2A\u5B57\u7B26

#\u7CFB\u7EDF\u6A21\u5757
system.module.alarm=\u62A5\u8B66\u7BA1\u7406

#\u5DE5\u4F5C\u6D41
workflow.process.definition.not.exists=\u5DE5\u4F5C\u6D41\u6D41\u7A0B\u5B9A\u4E49\u4E0D\u5B58\u5728
workflow.process.definition.stop.failed=\u5F53\u524D\u6709\u8FD0\u884C\u4E2D\u7684\u5B9E\u4F8B\uFF0C\u4E0D\u5141\u8BB8\u6682\u505C

#\u5F85\u529E\u4E2D\u5FC3\u6D88\u606F\u6A21\u677F
todo.template.alarm=\u3010{0}\u3011.\u3010{1}\u3011\u4E8E {2} {3}\u9884\u5B9A\u7684\u9600\u503C\u3010{4}\u3011\uFF0C\u5F53\u524D\u503C\u4E3A\u3010{5}\u3011\uFF0C\u8BF7\u53CA\u65F6\u5904\u7406\u3002
todo.template.alarm.cancel=\u3010{0}\u3011.\u3010{1}\u3011\u4E8E {2} \u62A5\u8B66\u6D88\u5931\uFF0C\u5F53\u524D\u503C\u4E3A\u3010{3}\u3011\u3002

# --------------------------open-api-doc start------------------------------------------
openapi.tag.uns.management=UNS\u6570\u636E\u5EFA\u6A21
openapi.tag.folder.management=\u6587\u4EF6\u5939/\u6587\u4EF6\u7BA1\u7406
openapi.tag.label.management=\u6807\u7B7E\u7BA1\u7406
openapi.tag.template.management=\u6A21\u677F\u7BA1\u7406
openapi.tag.collector.management=\u91C7\u96C6\u5668\u7F51\u5173\u7BA1\u7406
openapi.tag.menu.management=\u83DC\u5355\u7BA1\u7406
openapi.tag.user.management=\u7528\u6237\u7BA1\u7406

openapi.searchTree.$summary=\u641C\u7D22\u4E3B\u9898\u6811\uFF0C\u9ED8\u8BA4\u6574\u4E2A\u6811
openapi.searchTree.$parameter.key=\u5B50\u8282\u70B9\u6A21\u7CCA\u641C\u7D22\u8BCD
openapi.searchTree.$parameter.showRec=\u663E\u793A\u8BB0\u5F55\u6761\u6570
openapi.searchTree.$parameter.type=\u641C\u7D22\u7C7B\u578B: 1--\u6587\u672C\u641C\u7D22, 2--\u6807\u7B7E\u641C\u7D22

openapi.listTypes.$summary=\u679A\u4E3E\u6570\u636E\u7C7B\u578B
openapi.listTypes.$description=\u5217\u51FA\u6240\u6709\u652F\u6301\u7684\u6570\u636E\u7C7B\u578B\uFF0C\u4F9B\u5EFA\u8868\u65F6\u4E0B\u62C9\u9009\u62E9

openapi.getModelDefinition.$summary=\u67E5\u8BE2\u6587\u4EF6\u5939\u8BE6\u60C5
openapi.getModelDefinition.$parameter.topic=\u6587\u4EF6\u5939\u5BF9\u5E94\u7684\u4E3B\u9898\u8DEF\u5F84

openapi.getInstanceDetail.$summary=\u67E5\u8BE2\u6587\u4EF6\u8BE6\u60C5
openapi.getInstanceDetail.$parameter.topic=\u6587\u4EF6\u5BF9\u5E94\u7684\u4E3B\u9898\u8DEF\u5F84

openapi.createModelInstance.$summary=\u521B\u5EFA\u6587\u4EF6\u5939\u548C\u6587\u4EF6
openapi.createModelInstance.$body=\u6587\u4EF6\u5939\u5B57\u6BB5\u5B9A\u4E49

openapi.updateFieldAndDesc.$summary=\u4FEE\u6539\u6587\u4EF6\u5939\u5B57\u6BB5\uFF08\u53EA\u652F\u6301\u5220\u9664\u548C\u65B0\u589E\uFF09\u548C\u63CF\u8FF0
openapi.updateFieldAndDesc.$body=\u6587\u4EF6\u5939\u5B57\u6BB5\u5B9A\u4E49

openapi.removeModelOrInstance.$summary=\u5220\u9664\u6307\u5B9A\u8DEF\u5F84\u4E0B\u7684\u6240\u6709\u6587\u4EF6\u5939\u548C\u6587\u4EF6
openapi.removeModelOrInstance.$parameter.path=\u4E3B\u9898\uFF0C\u6587\u4EF6\u5939\u6216\u6587\u4EF6\u7684 path\u8DEF\u5F84\uFF0C\u4E5F\u53EF\u80FD\u53EA\u662F\u67D0\u6BB5\u8DEF\u5F84
openapi.removeModelOrInstance.$parameter.withFlow=\u662F\u5426\u5220\u9664\u76F8\u5173\u6D41\u7A0B
openapi.removeModelOrInstance.$parameter.withDashboard=\u662F\u5426\u5220\u9664\u76F8\u5173\u53EF\u89C6\u5316\u9762\u677F
openapi.removeModelOrInstance.$parameter.cascade=\u662F\u5426\u5220\u9664\u5173\u8054\u7684\u6587\u4EF6

openapi.allLabels.$summary=\u6807\u7B7E\u5217\u8868
openapi.allLabels.$description=\u5217\u51FA\u6240\u6709\u652F\u6301\u7684\u6807\u7B7E\uFF0C\u4E0B\u62C9\u9009\u62E9\uFF0C\u652F\u6301\u6A21\u7CCA\u641C\u7D22
openapi.allLabels.$parameter.key=\u5173\u952E\u5B57

openapi.labelDetail.$summary=\u6807\u7B7E\u8BE6\u60C5
openapi.labelDetail.$parameter.id=\u6807\u7B7EID

openapi.createLabel.$summary=\u521B\u5EFA\u6807\u7B7E
openapi.createLabel.$parameter.name=\u6807\u7B7E\u540D\u79F0

openapi.deleteLabel.$summary=\u5220\u9664\u6807\u7B7E
openapi.deleteLabel.$parameter.id=\u6807\u7B7EID

openapi.updateLabel.$summary=\u4FEE\u6539\u6807\u7B7E
openapi.updateLabel.$body=\u6807\u7B7E\u5B9A\u4E49

openapi.makeLabel.$summary=\u6587\u4EF6\u6253\u6807\u7B7E
openapi.makeLabel.$parameter.alias=\u6587\u4EF6\u522B\u540D
openapi.makeLabel.$body=\u6807\u7B7E\u96C6\u5408\uFF0C\u4E3A\u7A7A\u5219\u53D6\u6D88\u6240\u6709\u6807\u7B7E

openapi.templatePageList.$summary=\u67E5\u8BE2\u6A21\u677F\u5217\u8868
openapi.templatePageList.$parameter.$body=\u67E5\u8BE2\u53C2\u6570

openapi.templateDetail.$summary=\u67E5\u8BE2\u6A21\u677F\u8BE6\u60C5
openapi.templateDetail.$parameter.id=\u6A21\u677FID

openapi.createTemplate.$summary=\u65B0\u589E\u6A21\u677F
openapi.createTemplate.$parameter.id=\u6A21\u677F\u5B9A\u4E49

openapi.updateTemplate.$summary=\u4FEE\u6539\u6A21\u677F
openapi.updateTemplate.$parameter.id=\u6A21\u677FID
openapi.updateTemplate.$parameter.path=\u6A21\u677F\u540D\u79F0

openapi.deleteTemplate.$summary=\u5220\u9664\u6A21\u677F
openapi.deleteTemplate.$parameter.id=\u6A21\u677FID

openapi.collectorGatewayPageList.$summary=\u5206\u9875\u67E5\u8BE2\u91C7\u96C6\u5668\u7F51\u5173
openapi.collectorGatewayPageList.$body=\u91C7\u96C6\u5668\u7F51\u5173\u641C\u7D22\u5B57\u6BB5\u5B9A\u4E49

openapi.getCollectorGatewayDetailByUuid.$summary=\u83B7\u53D6\u91C7\u96C6\u5668\u7F51\u5173\u914D\u7F6E\u8BE6\u60C5
openapi.getCollectorGatewayDetailByUuid.$parameter.id=\u9274\u6743UUID

openapi.createCollectorGateway.$summary=\u65B0\u589E\u91C7\u96C6\u5668\u7F51\u5173
openapi.createCollectorGateway.$body=\u91C7\u96C6\u5668\u7F51\u5173\u5B57\u6BB5\u5B9A\u4E49

openapi.updateCollectorGateway.$summary=\u4FEE\u6539\u91C7\u96C6\u5668\u7F51\u5173
openapi.updateCollectorGateway.$parameter.authUuid=\u9274\u6743UUID
openapi.updateCollectorGateway.$body=\u91C7\u96C6\u5668\u7F51\u5173\u5B57\u6BB5\u5B9A\u4E49

openapi.deleteCollectorGatewayByAuthUuid.$summary=\u5220\u9664\u91C7\u96C6\u5668\u7F51\u5173
openapi.deleteCollectorGatewayByAuthUuid.$parameter.authUuid=\u9274\u6743UUID
openapi.sys.management=\u7CFB\u7EDF\u7F16\u7801\u7BA1\u7406

openapi.saveMenu.$summary=\u4FDD\u5B58\u83DC\u5355
openapi.updateCollectorGateway.$parameter.serviceName=\u670D\u52A1\u540D
openapi.updateCollectorGateway.$parameter.name=\u540D\u79F0
openapi.updateCollectorGateway.$parameter.showName=\u663E\u793A\u540D
openapi.updateCollectorGateway.$parameter.description=\u63CF\u8FF0
openapi.updateCollectorGateway.$parameter.baseUrl=baseUrl
openapi.updateCollectorGateway.$parameter.openType=\u8DF3\u8F6C\u65B9\u5F0Ftag\uFF0C0:iframe\u6253\u5F00  1:\u6253\u5F00\u65B0\u9875\u9762
openapi.updateCollectorGateway.$parameter.file=icon\u6587\u4EF6

openapi.openUserPageList.$summary=\u7528\u6237\u7BA1\u7406\u5217\u8868
openapi.openUserPageList.$body=\u7528\u6237\u67E5\u8BE2\u6761\u4EF6\u5B9A\u4E49

openapi.openCreateUser.$summary=\u521B\u5EFA\u7528\u6237
openapi.openUpdateUser.$summary=\u4FEE\u6539\u7528\u6237
openapi.openUserDelete.$summary=\u5220\u9664\u7528\u6237
openapi.openUserDelete.$parameter.username=\u7528\u6237\u540D
openapi.openUserResetPwd.$summary=\u91CD\u7F6E\u5BC6\u7801

openapi.tag.pride=Pride


# --------------------------open-api-doc end------------------------------------------

aboutus.postgresqlDescription:\u5173\u7CFB\u578B\u6570\u636E\u5E93
aboutus.nodeRedDescription:\u7BA1\u7406\u5E76\u81EA\u52A8\u5316\u6570\u636E\u6536\u96C6\u8FC7\u7A0B\uFF0C\u7B80\u5316\u6570\u636E\u6444\u5165\u4EE5\u7528\u4E8E\u5206\u6790\u548C\u62A5\u544A
aboutus.grafanaDescription:\u5F00\u6E90\u7684\u6570\u636E\u53EF\u89C6\u5316\u548C\u76D1\u63A7\u5E73\u53F0\uFF0C\u5B83\u5141\u8BB8\u7528\u6237\u901A\u8FC7\u521B\u5EFA\u52A8\u6001\u4EEA\u8868\u677F\u6765\u76D1\u89C6\u548C\u5206\u6790\u6570\u636E
aboutus.tdengineDescription:\u65F6\u5E8F\u6570\u636E\u5E93
aboutus.emqxDescription:\u5F00\u6E90\u3001\u4F01\u4E1A\u7EA7\u7684 MQTT \u5E73\u53F0\uFF0C\u4E13\u4E3A\u7269\u8054\u7F51(IoT)\u3001\u8F66\u8054\u7F51\u548C\u5DE5\u4E1A\u7269\u8054\u7F51\u8BBE\u8BA1\uFF0C\u63D0\u4F9B\u9AD8\u6027\u80FD\u3001\u9AD8\u53EF\u9760\u6027\u548C\u6613\u7528\u6027\u7684\u6570\u636E\u5168\u94FE\u8DEF\u5904\u7406\u80FD\u529B
aboutus.keycloakDescription:\u5F00\u6E90\u7684\u8EAB\u4EFD\u548C\u8BBF\u95EE\u7BA1\u7406\uFF08IAM\uFF09\u89E3\u51B3\u65B9\u6848\uFF0C\u4E13\u4E3A\u73B0\u4EE3\u5E94\u7528\u548C\u670D\u52A1\u8BBE\u8BA1
aboutus.hasuraDescription:\u5F00\u6E90\u7684 GraphQL \u5F15\u64CE\uFF0C\u7B80\u5728\u7B80\u5316\u548C\u52A0\u901F API \u5F00\u53D1
aboutus.kongaDescription:\u4F01\u4E1A\u7EA7\u670D\u52A1\u7F51\u5173\uFF0C\u5B83\u4EE5\u5176\u9AD8\u6027\u80FD\u3001\u9AD8\u53EF\u7528\u6027\u3001\u6613\u6269\u5C55\u548C\u6A21\u5757\u5316\u7684\u7279\u70B9\u800C\u95FB\u540D
aboutus.minioDescription:MinIO\u662F\u4E00\u4E2A\u9AD8\u6027\u80FD\u3001\u5F00\u6E90\u7684\u5BF9\u8C61\u5B58\u50A8\u670D\u52A1
aboutus.swaggerDescription:Swagger \u662F\u4E00\u4E2A\u89C4\u8303\u548C\u5B8C\u6574\u7684\u6846\u67B6\uFF0C\u7528\u4E8E\u751F\u6210\u3001\u63CF\u8FF0\u3001\u8C03\u7528\u548C\u53EF\u89C6\u5316 RESTful \u98CE\u683C\u7684 Web \u670D\u52A1

uns.example.name.it-demo=\u8BA2\u5355\u7BA1\u7406\u6848\u4F8B
uns.example.description.it-demo=\u5185\u7F6E\u751F\u4EA7\u8BA2\u5355\u7BA1\u7406\u6A21\u578B\u53CA\u6570\u636E\u91C7\u96C6\u6D41\u7A0B\uFF0C\u540C\u6B65\u57FA\u4E8E\u4F4E\u4EE3\u7801\u5DE5\u5177\u63D0\u4F9B\u4E86\u5927\u5C4F\u9875\u9762\u5C55\u793A\u3002
uns.example.name.ot-demo=\u65B0\u80FD\u6E90\u7BA1\u7406\u6848\u4F8B
uns.example.description.ot-demo=\u5185\u7F6E\u65B0\u80FD\u6E90\u5149\u4F0F\u7535\u7AD9\u5EFA\u6A21\u53CA\u6570\u636E\u91C7\u96C6\u6D41\u7A0B\uFF0C\u901A\u8FC7\u4F4E\u4EE3\u7801\u5DE5\u5177\u5B9E\u65F6\u67E5\u770B\u6240\u6709\u88C5\u7F6E\u7684\u5B9E\u65F6\u6570\u636E\u548C\u76D1\u63A7\u3002

collector.gateway.not.exists=\u91C7\u96C6\u5668\u7F51\u5173\u4E0D\u5B58\u5728
collector.gateway.alias.already.exists=\u91C7\u96C6\u5668\u7F51\u5173\u522B\u540D\u5DF2\u5B58\u5728
collector.gateway.uuid.already.exists=\u91C7\u96C6\u5668\u7F51\u5173UUID\u9274\u6743\u6807\u8BC6\u5DF2\u5B58\u5728
collector.gateway.type.invalid=\u65E0\u6548\u7684\u91C7\u96C6\u5668\u7F51\u5173\u7C7B\u578B
collector.gateway.data.timestamp.src.invalid=\u65E0\u6548\u7684\u91C7\u96C6\u5668\u7F51\u5173\u6570\u636E\u65F6\u95F4\u6233\u7C7B\u578B
collector.gateway.id.null=\u91C7\u96C6\u5668\u7F51\u5173ID\u4E0D\u80FD\u4E3A\u7A7A
collector.gateway.displayname.null=\u91C7\u96C6\u5668\u7F51\u5173\u663E\u793A\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A
collector.gateway.displayname.length=\u91C7\u96C6\u5668\u7F51\u5173\u663E\u793A\u540D\u79F0\u4E0D\u80FD\u8D85\u8FC764\u4E2A\u5B57\u7B26
collector.gateway.aliasname.null=\u91C7\u96C6\u5668\u7F51\u5173\u522B\u540D\u4E0D\u80FD\u4E3A\u7A7A
collector.gateway.aliasname.invalid=\u65E0\u6548\u7684\u91C7\u96C6\u5668\u7F51\u5173\u522B\u540D\uFF0C\u53EA\u652F\u6301\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\u7EC4\u5408\uFF0C\u4E0D\u80FD\u8D85\u8FC764\u4E2A\u5B57\u7B26\uFF0C\u4E0D\u80FD\u4EE5\u6570\u5B57\u5F00\u5934
collector.gateway.aliasname.invalid2=\u91C7\u96C6\u5668\u7F51\u5173\u522B\u540D\u5728\u547D\u540D\u7A7A\u95F4\u5185\u5B58\u5728\uFF0C\u8BF7\u4FEE\u6539\u540E\u91CD\u8BD5
collector.gateway.gatewayType.null=\u91C7\u96C6\u5668\u7F51\u5173\u7C7B\u578B\u4E0D\u80FD\u4E3A\u7A7A
collector.gateway.uuid.null=\u91C7\u96C6\u5668\u7F51\u5173UUID\u4E0D\u80FD\u4E3A\u7A7A
collector.gateway.uuid.invalid=\u65E0\u6548\u7684\u91C7\u96C6\u5668\u7F51\u5173UUID\uFF0C\u53EA\u652F\u6301\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\u3001\u6A2A\u6760\u7EC4\u5408\uFF0C\u4E0D\u80FD\u8D85\u8FC764\u4E2A\u5B57\u7B26
collector.gateway.description.length=\u91C7\u96C6\u5668\u7F51\u5173\u63CF\u8FF0\u6700\u591A512\u4E2A\u5B57\u7B26
collector.gateway.datatimestampsrc.null=\u91C7\u96C6\u5668\u7F51\u5173\u6570\u636E\u65F6\u95F4\u6233\u6765\u6E90\u4E0D\u80FD\u4E3A\u7A7A
collector.gateway.ips.length=\u91C7\u96C6\u5668\u7F51\u5173ip\u96C6\u5408\u6700\u591A1024\u4E2A\u5B57\u7B26
collector.gateway.uns.folder=\u91C7\u96C6\u5668
collector.gateway.verify.uuid.null=\u63A5\u5165\u9274\u6743UUID\u4E0D\u80FD\u4E3A\u7A7A
collector.gateway.verify.endpointidentify.length=\u8BBE\u5907\u7684ID\u4E0D\u80FD\u4E3A\u7A7A\uFF0C\u4E0D\u80FD\u8D85\u8FC750\u4E2A\u5B57\u7B26
collector.gateway.verify.endpointidentify.invalid=\u65E0\u6548\u7684\u8BBE\u5907ID\uFF0C\u53EA\u652F\u6301\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\u7EC4\u5408\uFF0C\u4E0D\u80FD\u4EE5\u6570\u5B57\u5F00\u5934
collector.gateway.verify.endpointname.length=\u8BBE\u5907\u540D\u4E0D\u80FD\u8D85\u8FC763\u4E2A\u5B57\u7B26
collector.gateway.verify.endpointidentify.invalid2=\u8BBE\u5907ID\u5728\u547D\u540D\u7A7A\u95F4\u5185\u5B58\u5728\uFF0C\u8BF7\u4FEE\u6539\u540E\u91CD\u8BD5
collector.gateway.tag.repetition=\u4F4D\u53F7\u540D{0}\u5B58\u5728\u91CD\u590D
collector.gateway.tag.name.null=\u4F4D\u53F7\u540D\u4E0D\u80FD\u4E3A\u7A7A
collector.gateway.tag.showname.null=\u4F4D\u53F7\u663E\u793A\u540D\u4E0D\u80FD\u4E3A\u7A7A

grpc.collector.not.verify=\u91C7\u96C6\u5668\u672A\u8BA4\u8BC1

sys.page.page_no_error=\u5F53\u524D\u9875\u6570\u9700\u4E3A\u6B63\u6574\u6570
sys.page.page_size_error=\u5F53\u524D\u6761\u6570\u9700\u4E3A\u6B63\u6574\u6570

sys.module.code_pattern_error=\u7F16\u7801\u3010{0}\u3011\u53EA\u652F\u6301\u5B57\u6BCD\u3001\u6570\u5B57\u3001\u4E0B\u5212\u7EBF\u7EC4\u5408
sys.module.code_length_error=\u7F16\u7801\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7200\u4E2A\u5B57\u7B26
sys.module.name_length_error=\u7F16\u7801\u3010{0}\u3011\u540D\u79F0\u4E0D\u4E3A\u7A7A\u4E14\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7200\u4E2A\u5B57\u7B26
sys.module.code_is_null=\u7F16\u7801\u4E0D\u80FD\u4E3A\u7A7A
sys.module.code_exist=\u7F16\u7801\u3010{0}\u3011\u5DF2\u5B58\u5728
sys.code.code_not_exist=\u7F16\u7801\u3010{0}\u3011\u4E0D\u5B58\u5728
sys.code.desc_length_error=\u7F16\u7801\u3010{0}\u3011\u5907\u6CE8C\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7255\u4E2A\u5B57\u7B26
sys.code.desb_length_error=\u7F16\u7801\u3010{0}\u3011\u5907\u6CE8B\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7255\u4E2A\u5B57\u7B26
sys.code.desa_length_error=\u7F16\u7801\u3010{0}\u3011\u5907\u6CE8A\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7255\u4E2A\u5B57\u7B26
sys.code.des_length_error=\u7F16\u7801\u3010{0}\u3011\u63CF\u8FF0\u957F\u5EA6\u4E0D\u80FD\u8D85\u8FC7255\u4E2A\u5B57\u7B26
sys.code.current_id_is_null=\u6392\u5E8F\u4E3A\u5FC5\u586B\u53C2\u6570\uFF0C\u4E0D\u53EF\u4EE5\u4E3A\u7A7A
sys.entity.code_is_default=\u7CFB\u7EDF\u9ED8\u8BA4\u7F16\u7801\u3010{0}\u3011\u4E0D\u80FD\u5220\u9664

sysModule.sys_webhook=webhook
sysModule.sys_webhook.service=\u670D\u52A1
sysModule.sys_webhook.service.auth=\u7528\u6237\u6743\u9650\u670D\u52A1
sysModule.sys_webhook.meta_auth=\u7528\u6237\u6743\u9650\u5143\u6570\u636E
sysModule.sys_webhook.meta_auth.user=\u7528\u6237
sysModule.sys_webhook.meta_auth.role=\u89D2\u8272
sysModule.sys_webhook.action_user=\u7528\u6237\u4E8B\u4EF6
sysModule.sys_webhook.action_user.add=\u65B0\u589E
sysModule.sys_webhook.action_user.modify=\u4FEE\u6539
sysModule.sys_webhook.action_user.delete=\u5220\u9664
sysModule.sys_webhook.action_role=\u89D2\u8272\u4E8B\u4EF6
sysModule.sys_webhook.action_role.add=\u65B0\u589E
sysModule.sys_webhook.action_role.modify=\u4FEE\u6539
sysModule.sys_webhook.action_role.delete=\u5220\u9664
sysModule.sys_webhook.service.uns=uns\u670D\u52A1
sysModule.sys_webhook.meta_uns=uns\u5143\u6570\u636E
sysModule.sys_webhook.meta_uns.field=\u5B57\u6BB5
sysModule.sys_webhook.action_field=\u5B57\u6BB5\u4E8B\u4EF6
sysModule.sys_webhook.action_field.add=\u65B0\u589E
sysModule.sys_webhook.action_field.modify=\u4FEE\u6539
sysModule.sys_webhook.action_field.delete=\u5220\u9664



webhook.manage.name_exist=\u540D\u79F0\u5DF2\u5B58\u5728
webhook.manage.data_not_exist=\u6570\u636E\u4E0D\u5B58\u5728

role.get.failed=\u83B7\u53D6\u89D2\u8272\u5931\u8D25
role.name.exist=\u89D2\u8272\u540D\u5DF2\u5B58\u5728
role.name.null=\u89D2\u8272\u540D\u4E0D\u80FD\u4E3A\u7A7A
role.name.invalid=\u65E0\u6548\u7684\u89D2\u8272\u540D\uFF0C\u4EC5\u652F\u6301\u4E2D\u6587\u3001\u5B57\u6BCD\u3001\u6570\u5B57\u7EC4\u5408\uFF0C\u4E0D\u80FD\u4EE5deny-\u5F00\u5934\uFF0C\u4E0D\u80FD\u8D85\u8FC710\u4E2A\u5B57\u7B26
role.id.null=\u89D2\u8272ID\u4E0D\u80FD\u4E3A\u7A7A
role.no.exist=\u89D2\u8272\u4E0D\u5B58\u5728
role.super.update=\u8D85\u7EA7\u7BA1\u7406\u5458\u4E0D\u80FD\u4FEE\u6539
role.super.delete=\u8D85\u7EA7\u7BA1\u7406\u5458\u4E0D\u80FD\u5220\u9664
role.max.limit=\u6700\u591A\u53EA\u80FD\u65B0\u589E10\u4E2A\u89D2\u8272

app.manager.delete.invalid.status=\u53EA\u6709\u5F85\u5B89\u88C5\u72B6\u6001\u6216\u5B89\u88C5\u5931\u8D25\u72B6\u6001\u7684APP\u624D\u80FD\u5220\u9664
app.manager.null=APP\u4E0D\u5B58\u5728
app.manager.install.invalid.status=\u53EA\u6709\u5F85\u5B89\u88C5\u72B6\u6001\u7684APP\u624D\u80FD\u5B89\u88C5
app.manager.uninstall.invalid.status=\u53EA\u6709\u5F85\u542F\u52A8\u72B6\u6001\u6216\u505C\u6B62\u72B6\u6001\u7684APP\u624D\u80FD\u5378\u8F7D
app.manager.start.invalid.status=\u53EA\u6709\u5F85\u542F\u52A8\u72B6\u6001\u6216\u505C\u6B62\u72B6\u6001\u7684APP\u624D\u80FD\u542F\u52A8
app.manager.stop.invalid.status=\u53EA\u6709\u8FD0\u884C\u72B6\u6001\u7684APP\u624D\u80FD\u6682\u505C
app.manager.upload.null=\u4E0A\u4F20APP\u5305\u4E0D\u80FD\u4E3A\u7A7A
app.manager.upload.invalid=\u4E0A\u4F20APP\u5305\u683C\u5F0F\u9519\u8BEF\uFF0C\u6269\u5C55\u540D\u5FC5\u987B\u662Fzip
app.manager.upload.format=\u4E0A\u4F20APP\u5305\u5185\u5BB9\u6709\u8BEF
app.manager.upload.duplicate=\u8BF7\u52FF\u4E0A\u4F20\u91CD\u590D\u7684APP\u5305
app.manager.upload.error=\u4E0A\u4F20APP\u5305\u5931\u8D25

notify.template.code.duplicate=\u901A\u77E5\u6A21\u677F\u7F16\u7801\u91CD\u590D
notify.template.name.null=\u901A\u77E5\u6A21\u677F\u540D\u79F0\u4E0D\u53EF\u4E3A\u7A7A
notify.template.code.null=\u901A\u77E5\u6A21\u677F\u7F16\u7801\u4E0D\u53EF\u4E3A\u7A7A
notify.template.type.null=\u901A\u77E5\u6A21\u677F\u901A\u77E5\u65B9\u5F0F\u4E0D\u53EF\u4E3A\u7A7A
notify.template.id.not.exist=\u901A\u77E5\u6A21\u677F\u4E0D\u5B58\u5728
notify.template.config.not.exist=\u5F53\u524D\u901A\u77E5\u65B9\u5F0F\u914D\u7F6E\u4E0D\u5B58\u5728
notify.template.config.type.not.exist=\u5F53\u524D\u901A\u77E5\u65B9\u5F0F\u4E0D\u5B58\u5728
notify.template.email.host.error=\u90AE\u4EF6\u670D\u52A1\u5668\u57DF\u540D\u89E3\u6790\u5931\u8D25

plugin.manager.null=\u63D2\u4EF6\u4E0D\u5B58\u5728
plugin.manager.install.invalid.status=\u63D2\u4EF6\u5DF2\u5B89\u88C5
plugin.manager.install.error=\u63D2\u4EF6\u5B89\u88C5\u5931\u8D25
plugin.manager.uninstall.error=\u63D2\u4EF6\u5378\u8F7D\u5931\u8D25
plugin.manager.install.dependency=\u4F9D\u8D56\u63D2\u4EF6{0}\u672A\u5B89\u88C5
plugin.manager.uninstall.dependency=\u63D2\u4EF6\u6B63\u88AB\u63D2\u4EF6{0}\u4F9D\u8D56\uFF0C\u65E0\u6CD5\u5378\u8F7D
plugin.manager.uninstall.app.check=\u63D2\u4EF6\u5378\u8F7D\u5931\u8D25\uFF0C\u5B58\u5728app\uFF0C\u8BF7\u5220\u9664app\u540E\u518D\u5C1D\u8BD5\uFF01