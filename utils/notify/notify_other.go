// Copyright 2015 <PERSON> and The Caddy Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

//go:build !linux && !windows

package notify

func Ready() error               { return nil }
func Reloading() error           { return nil }
func Stopping() error            { return nil }
func Status(_ string) error      { return nil }
func Error(_ error, _ int) error { return nil }
