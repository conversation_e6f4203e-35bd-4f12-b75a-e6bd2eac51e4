{"name": "supos-community", "private": true, "version": "1.0.8+public", "type": "module", "scripts": {"intl": "node ./scripts/i18n.js", "start": "concurrently \"vite\" \"npm run watch:intl\"", "start:vite": "vite", "watch:intl": "nodemon --watch src/locale/index.js --exec npm run intl", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "commit": "git-cz", "prepare": "husky install"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix"], "*.{css,less,scss}": ["stylelint --fix --allow-empty-input"], "*.{json,md}": ["prettier --write"]}, "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "dependencies": {"@ant-design/icons": "5.5.2", "@antv/x6": "^2.18.1", "@antv/x6-react-shape": "^2.2.3", "@carbon/icons-react": "11.60.0", "@codemirror/lang-html": "^6.4.9", "@codemirror/lang-javascript": "^6.2.2", "@codemirror/lang-sql": "^6.8.0", "@codemirror/lang-xml": "^6.1.0", "@copilotkit/react-core": "1.8.13", "@copilotkit/react-ui": "1.8.13", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@graphiql/plugin-explorer": "^3.2.5", "@graphiql/toolkit": "^0.11.1", "@ibm/plex-sans": "^1.1.0", "@module-federation/enhanced": "^0.14.3", "@module-federation/vite": "^1.4.0", "@uiw/react-codemirror": "^4.23.7", "ahooks": "3.8.5", "antd": "5.25.2", "axios": "1.8.2", "blueimp-md5": "^2.19.0", "braft-editor": "^2.3.9", "classnames": "^2.5.1", "clipboard": "^2.0.11", "codemirror": "^5.65.18", "dayjs": "^1.11.13", "graphiql": "^3.8.3", "graphql": "^16.10.0", "immer": "^10.1.1", "insert-css": "^2.0.0", "js-beautify": "^1.15.1", "js-cookie": "^3.0.5", "lodash": "4.17.21", "mermaid": "^11.5.0", "pinyin-pro": "^3.26.0", "qs": "^6.13.1", "react": "18.3.1", "react-cookie": "^7.2.2", "react-data-mapping": "^1.3.18", "react-dom": "18.3.1", "react-draggable": "^4.4.6", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.1", "react-resizable": "^3.0.5", "react-router-dom": "6.27.0", "react-sticky-box": "^2.0.5", "sass": "1.80.4", "shepherd.js": "^14.4.0", "uuid": "^11.0.3", "vite-plugin-top-level-await": "^1.5.0", "zustand": "^5.0.5"}, "devDependencies": {"@commitlint/cli": "^19.6.1", "@commitlint/config-conventional": "^19.6.0", "@eslint/js": "^9.17.0", "@types/blueimp-md5": "^2.18.2", "@types/insert-css": "^2.0.3", "@types/js-beautify": "^1.14.3", "@types/js-cookie": "^3.0.6", "@types/lodash": "^4.17.13", "@types/node": "^22.10.2", "@types/qs": "^6.9.17", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@types/react-resizable": "^3.0.8", "@vitejs/plugin-legacy": "^6.1.1", "@vitejs/plugin-react": "^4.3.4", "commitizen": "^4.3.1", "concurrently": "^9.1.2", "cz-conventional-changelog": "^3.3.0", "dotenv": "^16.4.7", "eslint": "^9.17.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "https-proxy-agent": "^7.0.6", "husky": "^9.1.7", "lint-staged": "^15.2.11", "nodemon": "^3.1.10", "picocolors": "^1.1.1", "postcss": "^8.4.49", "postcss-scss": "^4.0.9", "prettier": "^3.4.2", "react-intl": "^7.0.4", "stylelint": "^16.12.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-standard": "^36.0.1", "stylelint-config-standard-scss": "^13.1.0", "stylelint-prettier": "^5.0.2", "stylelint-scss": "^6.10.0", "tencentcloud-sdk-nodejs-tmt": "^4.1.39", "typescript": "~5.6.3", "typescript-eslint": "^8.18.1", "vite": "^6.3.5", "vite-plugin-html": "^3.2.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}